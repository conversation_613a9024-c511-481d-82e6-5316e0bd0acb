<template>
  <div>
    <!-- Hero Section -->
    <section class="relative bg-white overflow-hidden">
      <div class="max-w-7xl mx-auto">
        <div class="relative z-10 pb-8 bg-white sm:pb-16 md:pb-20 lg:max-w-2xl lg:w-full lg:pb-28 xl:pb-32">
          <svg
            class="hidden lg:block absolute right-0 inset-y-0 h-full w-48 text-white transform translate-x-1/2"
            fill="currentColor"
            viewBox="0 0 100 100"
            preserveAspectRatio="none"
            aria-hidden="true"
          >
            <polygon points="50,0 100,0 50,100 0,100" />
          </svg>

          <main class="mt-10 mx-auto max-w-7xl px-4 sm:mt-12 sm:px-6 md:mt-16 lg:mt-20 lg:px-8 xl:mt-28">
            <div class="sm:text-center lg:text-left">
              <h1 class="text-4xl tracking-tight font-extrabold text-gray-900 sm:text-5xl md:text-6xl">
                <span class="block xl:inline">Cardápio digital</span>
                <span class="block text-primary-600 xl:inline">para seu restaurante</span>
              </h1>
              <p class="mt-3 text-base text-gray-500 sm:mt-5 sm:text-lg sm:max-w-xl sm:mx-auto md:mt-5 md:text-xl lg:mx-0">
                Crie um cardápio digital profissional, gerencie pedidos online e aumente suas vendas. 
                Solução completa para restaurantes, lanchonetes e food trucks.
              </p>
              <div class="mt-5 sm:mt-8 sm:flex sm:justify-center lg:justify-start">
                <div class="rounded-md shadow">
                  <router-link
                    to="/auth/register"
                    class="btn btn-primary btn-xl w-full flex items-center justify-center"
                  >
                    Começar grátis
                  </router-link>
                </div>
                <div class="mt-3 sm:mt-0 sm:ml-3">
                  <a
                    href="#demo"
                    class="btn btn-outline btn-xl w-full flex items-center justify-center"
                    @click="scrollToSection('demo')"
                  >
                    Ver demonstração
                  </a>
                </div>
              </div>
            </div>
          </main>
        </div>
      </div>
      <div class="lg:absolute lg:inset-y-0 lg:right-0 lg:w-1/2">
        <img
          class="h-56 w-full object-cover sm:h-72 md:h-96 lg:w-full lg:h-full"
          src="https://images.unsplash.com/photo-1555396273-367ea4eb4db5?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80"
          alt="Restaurante moderno"
        />
      </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="py-12 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="lg:text-center">
          <h2 class="text-base text-primary-600 font-semibold tracking-wide uppercase">Recursos</h2>
          <p class="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
            Tudo que você precisa para digitalizar seu negócio
          </p>
          <p class="mt-4 max-w-2xl text-xl text-gray-500 lg:mx-auto">
            Nossa plataforma oferece todas as ferramentas necessárias para modernizar seu restaurante.
          </p>
        </div>

        <div class="mt-10">
          <div class="space-y-10 md:space-y-0 md:grid md:grid-cols-2 md:gap-x-8 md:gap-y-10">
            <div v-for="feature in features" :key="feature.name" class="relative">
              <div class="absolute flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white">
                <component :is="feature.icon" class="h-6 w-6" />
              </div>
              <p class="ml-16 text-lg leading-6 font-medium text-gray-900">{{ feature.name }}</p>
              <p class="mt-2 ml-16 text-base text-gray-500">{{ feature.description }}</p>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Demo Section -->
    <section id="demo" class="py-12 bg-gray-50">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="lg:text-center">
          <h2 class="text-base text-primary-600 font-semibold tracking-wide uppercase">Demonstração</h2>
          <p class="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
            Veja como funciona
          </p>
        </div>

        <div class="mt-10">
          <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="px-6 py-8">
              <div class="text-center">
                <div class="mx-auto h-64 w-full bg-gray-200 rounded-lg flex items-center justify-center">
                  <div class="text-center">
                    <PlayIcon class="mx-auto h-16 w-16 text-gray-400" />
                    <p class="mt-2 text-sm text-gray-500">Vídeo demonstrativo em breve</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- Pricing Section -->
    <section id="pricing" class="py-12 bg-white">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="lg:text-center">
          <h2 class="text-base text-primary-600 font-semibold tracking-wide uppercase">Preços</h2>
          <p class="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
            Planos para todos os tamanhos
          </p>
          <p class="mt-4 max-w-2xl text-xl text-gray-500 lg:mx-auto">
            Escolha o plano ideal para seu negócio. Comece grátis e evolua conforme cresce.
          </p>
        </div>

        <div class="mt-10 space-y-4 sm:mt-16 sm:space-y-0 sm:grid sm:grid-cols-2 sm:gap-6 lg:max-w-4xl lg:mx-auto xl:max-w-none xl:mx-0 xl:grid-cols-3">
          <div v-for="plan in pricingPlans" :key="plan.name" class="border border-gray-200 rounded-lg shadow-sm divide-y divide-gray-200">
            <div class="p-6">
              <h2 class="text-lg leading-6 font-medium text-gray-900">{{ plan.name }}</h2>
              <p class="mt-4 text-sm text-gray-500">{{ plan.description }}</p>
              <p class="mt-8">
                <span class="text-4xl font-extrabold text-gray-900">{{ plan.price }}</span>
                <span v-if="plan.price !== 'Grátis'" class="text-base font-medium text-gray-500">/mês</span>
              </p>
              <router-link
                to="/auth/register"
                :class="[
                  'mt-8 block w-full py-2 px-4 border border-transparent rounded-md text-sm font-medium text-center transition-colors duration-200',
                  plan.featured
                    ? 'bg-primary-600 text-white hover:bg-primary-700'
                    : 'bg-primary-50 text-primary-700 hover:bg-primary-100'
                ]"
              >
                {{ plan.cta }}
              </router-link>
            </div>
            <div class="pt-6 pb-8 px-6">
              <h3 class="text-xs font-medium text-gray-900 tracking-wide uppercase">O que está incluído</h3>
              <ul class="mt-6 space-y-4">
                <li v-for="feature in plan.features" :key="feature" class="flex space-x-3">
                  <CheckIcon class="flex-shrink-0 h-5 w-5 text-success-500" />
                  <span class="text-sm text-gray-500">{{ feature }}</span>
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </section>

    <!-- CTA Section -->
    <section class="bg-primary-700">
      <div class="max-w-2xl mx-auto text-center py-16 px-4 sm:py-20 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-extrabold text-white sm:text-4xl">
          <span class="block">Pronto para começar?</span>
          <span class="block">Crie sua conta gratuita hoje.</span>
        </h2>
        <p class="mt-4 text-lg leading-6 text-primary-200">
          Junte-se a centenas de restaurantes que já digitalizaram seus cardápios.
        </p>
        <router-link
          to="/auth/register"
          class="mt-8 w-full inline-flex items-center justify-center px-5 py-3 border border-transparent text-base font-medium rounded-md text-primary-600 bg-white hover:bg-primary-50 sm:w-auto transition-colors duration-200"
        >
          Começar grátis
        </router-link>
      </div>
    </section>
  </div>
</template>

<script setup lang="ts">
import {
  BookOpenIcon,
  DevicePhoneMobileIcon,
  ChartBarIcon,
  CreditCardIcon,
  CloudIcon,
  ShieldCheckIcon,
  CheckIcon,
  PlayIcon
} from '@heroicons/vue/24/outline'

const features = [
  {
    name: 'Cardápio Digital',
    description: 'Crie um cardápio online profissional com fotos, descrições e preços atualizados em tempo real.',
    icon: BookOpenIcon,
  },
  {
    name: 'Pedidos Online',
    description: 'Receba pedidos diretamente pelo seu cardápio digital, com notificações em tempo real.',
    icon: DevicePhoneMobileIcon,
  },
  {
    name: 'Relatórios e Analytics',
    description: 'Acompanhe suas vendas, produtos mais pedidos e performance do seu negócio.',
    icon: ChartBarIcon,
  },
  {
    name: 'Pagamentos Integrados',
    description: 'Aceite pagamentos online com PIX, cartão de crédito e débito de forma segura.',
    icon: CreditCardIcon,
  },
  {
    name: 'Armazenamento na Nuvem',
    description: 'Seus dados ficam seguros na nuvem, acessíveis de qualquer lugar e dispositivo.',
    icon: CloudIcon,
  },
  {
    name: 'Segurança Garantida',
    description: 'Proteção de dados com criptografia e conformidade com a LGPD.',
    icon: ShieldCheckIcon,
  },
]

const pricingPlans = [
  {
    name: 'Básico',
    price: 'Grátis',
    description: 'Perfeito para começar',
    cta: 'Começar grátis',
    featured: false,
    features: [
      'Cardápio digital básico',
      'Até 20 itens no menu',
      'Suporte por email',
      'Domínio personalizado',
    ],
  },
  {
    name: 'Profissional',
    price: 'R$ 29',
    description: 'Para restaurantes em crescimento',
    cta: 'Começar teste grátis',
    featured: true,
    features: [
      'Cardápio digital completo',
      'Itens ilimitados',
      'Pedidos online',
      'Relatórios básicos',
      'Suporte prioritário',
      'Integração com delivery',
    ],
  },
  {
    name: 'Empresarial',
    price: 'R$ 79',
    description: 'Para redes e franquias',
    cta: 'Falar com vendas',
    featured: false,
    features: [
      'Múltiplos estabelecimentos',
      'Relatórios avançados',
      'API personalizada',
      'Suporte 24/7',
      'Treinamento incluído',
      'Gerente de conta dedicado',
    ],
  },
]

const scrollToSection = (sectionId: string) => {
  const element = document.getElementById(sectionId)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}
</script>
