<template>
  <TransitionRoot as="template" :show="true">
    <Dialog as="div" class="relative z-50" @close="$emit('close')">
      <TransitionChild
        as="template"
        enter="ease-out duration-300"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="ease-in duration-200"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
      </TransitionChild>

      <div class="fixed inset-0 z-10 overflow-y-auto">
        <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <TransitionChild
            as="template"
            enter="ease-out duration-300"
            enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            enter-to="opacity-100 translate-y-0 sm:scale-100"
            leave="ease-in duration-200"
            leave-from="opacity-100 translate-y-0 sm:scale-100"
            leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
          >
            <DialogPanel class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-2xl sm:p-6">
              <!-- Header -->
              <div class="flex items-center justify-between mb-6">
                <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900">
                  {{ isEditing ? 'Editar Item' : 'Novo Item' }}
                </DialogTitle>
                <button
                  @click="$emit('close')"
                  class="rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <XMarkIcon class="h-6 w-6" />
                </button>
              </div>

              <!-- Formulário -->
              <form @submit.prevent="handleSubmit" class="space-y-6">
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <!-- Coluna esquerda -->
                  <div class="space-y-6">
                    <!-- Nome -->
                    <div class="form-group">
                      <label for="name" class="form-label">
                        Nome do item *
                      </label>
                      <input
                        id="name"
                        v-model="form.name"
                        type="text"
                        required
                        :class="[
                          'input',
                          errors.name ? 'input-error' : ''
                        ]"
                        placeholder="Ex: Hambúrguer Artesanal"
                        :disabled="isLoading"
                      />
                      <p v-if="errors.name" class="form-error">
                        {{ errors.name }}
                      </p>
                    </div>

                    <!-- Categoria -->
                    <div class="form-group">
                      <label for="category_id" class="form-label">
                        Categoria *
                      </label>
                      <select
                        id="category_id"
                        v-model="form.category_id"
                        required
                        :class="[
                          'input',
                          errors.category_id ? 'input-error' : ''
                        ]"
                        :disabled="isLoading"
                      >
                        <option value="">Selecione uma categoria</option>
                        <option
                          v-for="category in categories"
                          :key="category.id"
                          :value="category.id"
                        >
                          {{ category.name }}
                        </option>
                      </select>
                      <p v-if="errors.category_id" class="form-error">
                        {{ errors.category_id }}
                      </p>
                    </div>

                    <!-- Preço -->
                    <div class="form-group">
                      <label for="price" class="form-label">
                        Preço *
                      </label>
                      <div class="relative">
                        <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                          <span class="text-gray-500 sm:text-sm">R$</span>
                        </div>
                        <input
                          id="price"
                          v-model.number="form.price"
                          type="number"
                          step="0.01"
                          min="0"
                          required
                          :class="[
                            'input pl-10',
                            errors.price ? 'input-error' : ''
                          ]"
                          placeholder="0,00"
                          :disabled="isLoading"
                        />
                      </div>
                      <p v-if="errors.price" class="form-error">
                        {{ errors.price }}
                      </p>
                    </div>

                    <!-- Tempo de preparo -->
                    <div class="form-group">
                      <label for="prep_time_minutes" class="form-label">
                        Tempo de preparo (minutos)
                      </label>
                      <input
                        id="prep_time_minutes"
                        v-model.number="form.prep_time_minutes"
                        type="number"
                        min="0"
                        :class="[
                          'input',
                          errors.prep_time_minutes ? 'input-error' : ''
                        ]"
                        placeholder="15"
                        :disabled="isLoading"
                      />
                      <p v-if="errors.prep_time_minutes" class="form-error">
                        {{ errors.prep_time_minutes }}
                      </p>
                    </div>

                    <!-- Estoque -->
                    <div class="form-group">
                      <label for="stock_quantity" class="form-label">
                        Quantidade em estoque
                      </label>
                      <input
                        id="stock_quantity"
                        v-model.number="form.stock_quantity"
                        type="number"
                        min="0"
                        :class="[
                          'input',
                          errors.stock_quantity ? 'input-error' : ''
                        ]"
                        placeholder="Deixe vazio para ilimitado"
                        :disabled="isLoading"
                      />
                      <p class="form-help">
                        Deixe vazio para estoque ilimitado
                      </p>
                      <p v-if="errors.stock_quantity" class="form-error">
                        {{ errors.stock_quantity }}
                      </p>
                    </div>
                  </div>

                  <!-- Coluna direita -->
                  <div class="space-y-6">
                    <!-- Descrição -->
                    <div class="form-group">
                      <label for="description" class="form-label">
                        Descrição
                      </label>
                      <textarea
                        id="description"
                        v-model="form.description"
                        rows="4"
                        :class="[
                          'input',
                          errors.description ? 'input-error' : ''
                        ]"
                        placeholder="Descreva os ingredientes e características do item..."
                        :disabled="isLoading"
                      />
                      <p v-if="errors.description" class="form-error">
                        {{ errors.description }}
                      </p>
                    </div>

                    <!-- Informações nutricionais -->
                    <div class="space-y-4">
                      <h4 class="text-sm font-medium text-gray-900">Informações Nutricionais</h4>
                      
                      <div class="grid grid-cols-2 gap-4">
                        <div class="form-group">
                          <label for="calories" class="form-label">
                            Calorias
                          </label>
                          <input
                            id="calories"
                            v-model.number="form.calories"
                            type="number"
                            min="0"
                            class="input"
                            placeholder="0"
                            :disabled="isLoading"
                          />
                        </div>

                        <div class="form-group">
                          <label for="protein" class="form-label">
                            Proteínas (g)
                          </label>
                          <input
                            id="protein"
                            v-model.number="form.protein"
                            type="number"
                            step="0.1"
                            min="0"
                            class="input"
                            placeholder="0.0"
                            :disabled="isLoading"
                          />
                        </div>

                        <div class="form-group">
                          <label for="carbs" class="form-label">
                            Carboidratos (g)
                          </label>
                          <input
                            id="carbs"
                            v-model.number="form.carbs"
                            type="number"
                            step="0.1"
                            min="0"
                            class="input"
                            placeholder="0.0"
                            :disabled="isLoading"
                          />
                        </div>

                        <div class="form-group">
                          <label for="fat" class="form-label">
                            Gorduras (g)
                          </label>
                          <input
                            id="fat"
                            v-model.number="form.fat"
                            type="number"
                            step="0.1"
                            min="0"
                            class="input"
                            placeholder="0.0"
                            :disabled="isLoading"
                          />
                        </div>
                      </div>
                    </div>

                    <!-- Configurações -->
                    <div class="space-y-4">
                      <h4 class="text-sm font-medium text-gray-900">Configurações</h4>
                      
                      <div class="space-y-3">
                        <label class="flex items-center">
                          <input
                            v-model="form.is_available"
                            type="checkbox"
                            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                            :disabled="isLoading"
                          />
                          <span class="ml-3 text-sm text-gray-700">Disponível para pedidos</span>
                        </label>

                        <label class="flex items-center">
                          <input
                            v-model="form.is_featured"
                            type="checkbox"
                            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                            :disabled="isLoading"
                          />
                          <span class="ml-3 text-sm text-gray-700">Item em destaque</span>
                        </label>

                        <label class="flex items-center">
                          <input
                            v-model="form.is_vegetarian"
                            type="checkbox"
                            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                            :disabled="isLoading"
                          />
                          <span class="ml-3 text-sm text-gray-700">🌱 Vegetariano</span>
                        </label>

                        <label class="flex items-center">
                          <input
                            v-model="form.is_vegan"
                            type="checkbox"
                            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                            :disabled="isLoading"
                          />
                          <span class="ml-3 text-sm text-gray-700">🌿 Vegano</span>
                        </label>

                        <label class="flex items-center">
                          <input
                            v-model="form.is_spicy"
                            type="checkbox"
                            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                            :disabled="isLoading"
                          />
                          <span class="ml-3 text-sm text-gray-700">🌶️ Picante</span>
                        </label>

                        <label class="flex items-center">
                          <input
                            v-model="form.is_gluten_free"
                            type="checkbox"
                            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                            :disabled="isLoading"
                          />
                          <span class="ml-3 text-sm text-gray-700">Sem glúten</span>
                        </label>

                        <label class="flex items-center">
                          <input
                            v-model="form.is_dairy_free"
                            type="checkbox"
                            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                            :disabled="isLoading"
                          />
                          <span class="ml-3 text-sm text-gray-700">Sem lactose</span>
                        </label>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Botões -->
                <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                  <button
                    type="button"
                    @click="$emit('close')"
                    class="btn btn-outline"
                    :disabled="isLoading"
                  >
                    Cancelar
                  </button>
                  
                  <button
                    type="submit"
                    :disabled="isLoading || !isFormValid"
                    class="btn btn-primary"
                  >
                    <div v-if="isLoading" class="flex items-center">
                      <div class="spinner w-4 h-4 mr-2"></div>
                      Salvando...
                    </div>
                    <span v-else>
                      {{ isEditing ? 'Atualizar' : 'Criar' }} Item
                    </span>
                  </button>
                </div>
              </form>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue'
import { XMarkIcon } from '@heroicons/vue/24/outline'

import { useMenuStore } from '@/stores/menu'
import type { MenuItem, Category, MenuItemCreate, MenuItemUpdate } from '@/types'

interface Props {
  item?: MenuItem | null
  categories: Category[]
}

interface Emits {
  (e: 'close'): void
  (e: 'saved'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const menuStore = useMenuStore()

// Estado do formulário
const form = ref<MenuItemCreate & { 
  calories?: number
  protein?: number
  carbs?: number
  fat?: number
  fiber?: number
  stock_quantity?: number
}>({
  name: '',
  description: '',
  price: 0,
  category_id: '',
  is_available: true,
  stock_quantity: undefined,
  calories: undefined,
  protein: undefined,
  carbs: undefined,
  fat: undefined,
  fiber: undefined,
  prep_time_minutes: 15,
  is_featured: false,
  is_spicy: false,
  is_vegetarian: false,
  is_vegan: false,
  is_gluten_free: false,
  is_dairy_free: false,
  sort_order: 0
})

const isLoading = ref(false)
const errors = ref<Record<string, string>>({})

// Computed
const isEditing = computed(() => !!props.item)

const isFormValid = computed(() => {
  return form.value.name.trim().length >= 2 &&
         form.value.category_id &&
         form.value.price > 0
})

// Métodos
const validateField = (field: string, value: any) => {
  errors.value[field] = ''
  
  switch (field) {
    case 'name':
      if (!value || value.trim().length < 2) {
        errors.value[field] = 'Nome deve ter pelo menos 2 caracteres'
      }
      break
      
    case 'category_id':
      if (!value) {
        errors.value[field] = 'Categoria é obrigatória'
      }
      break
      
    case 'price':
      if (!value || value <= 0) {
        errors.value[field] = 'Preço deve ser maior que zero'
      }
      break
      
    case 'stock_quantity':
      if (value !== undefined && value < 0) {
        errors.value[field] = 'Estoque não pode ser negativo'
      }
      break
      
    case 'prep_time_minutes':
      if (value < 0) {
        errors.value[field] = 'Tempo de preparo não pode ser negativo'
      }
      break
  }
}

const validateForm = () => {
  validateField('name', form.value.name)
  validateField('category_id', form.value.category_id)
  validateField('price', form.value.price)
  validateField('stock_quantity', form.value.stock_quantity)
  validateField('prep_time_minutes', form.value.prep_time_minutes)
  
  return Object.values(errors.value).every(error => !error)
}

const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }
  
  isLoading.value = true
  
  try {
    const itemData: MenuItemCreate | MenuItemUpdate = {
      name: form.value.name.trim(),
      description: form.value.description?.trim() || undefined,
      price: form.value.price,
      category_id: form.value.category_id,
      is_available: form.value.is_available,
      stock_quantity: form.value.stock_quantity || undefined,
      calories: form.value.calories || undefined,
      protein: form.value.protein || undefined,
      carbs: form.value.carbs || undefined,
      fat: form.value.fat || undefined,
      fiber: form.value.fiber || undefined,
      prep_time_minutes: form.value.prep_time_minutes,
      is_featured: form.value.is_featured,
      is_spicy: form.value.is_spicy,
      is_vegetarian: form.value.is_vegetarian,
      is_vegan: form.value.is_vegan,
      is_gluten_free: form.value.is_gluten_free,
      is_dairy_free: form.value.is_dairy_free,
      sort_order: form.value.sort_order
    }
    
    if (isEditing.value && props.item) {
      await menuStore.updateMenuItem(props.item.id, itemData as MenuItemUpdate)
    } else {
      await menuStore.createMenuItem(itemData as MenuItemCreate)
    }
    
    emit('saved')
  } catch (error) {
    console.error('Erro ao salvar item:', error)
    
    // Tratar erros de validação do servidor
    if (error.response?.status === 422) {
      const serverErrors = error.response.data.detail
      if (Array.isArray(serverErrors)) {
        serverErrors.forEach((err: any) => {
          const field = err.loc?.[1]
          if (field && Object.keys(form.value).includes(field)) {
            errors.value[field] = err.msg
          }
        })
      }
    }
  } finally {
    isLoading.value = false
  }
}

// Watchers para validação em tempo real
watch(() => form.value.name, (newValue) => {
  if (errors.value.name) {
    validateField('name', newValue)
  }
})

watch(() => form.value.price, (newValue) => {
  if (errors.value.price) {
    validateField('price', newValue)
  }
})

watch(() => form.value.category_id, (newValue) => {
  if (errors.value.category_id) {
    validateField('category_id', newValue)
  }
})

// Inicializar formulário
onMounted(() => {
  if (props.item) {
    form.value = {
      name: props.item.name,
      description: props.item.description || '',
      price: props.item.price,
      category_id: props.item.category_id,
      is_available: props.item.is_available,
      stock_quantity: props.item.stock_quantity || undefined,
      calories: props.item.calories || undefined,
      protein: props.item.protein || undefined,
      carbs: props.item.carbs || undefined,
      fat: props.item.fat || undefined,
      fiber: props.item.fiber || undefined,
      prep_time_minutes: props.item.prep_time_minutes,
      is_featured: props.item.is_featured,
      is_spicy: props.item.is_spicy,
      is_vegetarian: props.item.is_vegetarian,
      is_vegan: props.item.is_vegan,
      is_gluten_free: props.item.is_gluten_free,
      is_dairy_free: props.item.is_dairy_free,
      sort_order: props.item.sort_order
    }
  }
})
</script>
