<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Gestão do Cardápio</h1>
        <p class="text-gray-600">Gerencie os itens do seu cardápio</p>
      </div>
      
      <button
        @click="openCreateModal"
        class="btn btn-primary"
      >
        <PlusIcon class="h-5 w-5 mr-2" />
        Novo Item
      </button>
    </div>

    <!-- Filtros -->
    <div class="card">
      <div class="card-body">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <!-- Busca -->
          <div class="md:col-span-2">
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Buscar itens..."
              class="input"
            />
          </div>
          
          <!-- Categoria -->
          <div>
            <select v-model="selectedCategoryId" class="input">
              <option value="">Todas as categorias</option>
              <option
                v-for="category in activeCategories"
                :key="category.id"
                :value="category.id"
              >
                {{ category.name }}
              </option>
            </select>
          </div>
          
          <!-- Filtros adicionais -->
          <div class="flex items-center space-x-4">
            <label class="flex items-center">
              <input
                v-model="showUnavailable"
                type="checkbox"
                class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <span class="ml-2 text-sm text-gray-700">Indisponíveis</span>
            </label>
          </div>
        </div>
        
        <!-- Filtros rápidos -->
        <div class="flex flex-wrap gap-2 mt-4">
          <button
            @click="quickFilter = quickFilter === 'featured' ? '' : 'featured'"
            :class="[
              'btn btn-sm',
              quickFilter === 'featured' ? 'btn-primary' : 'btn-outline'
            ]"
          >
            <StarIcon class="h-4 w-4 mr-1" />
            Destaques
          </button>
          
          <button
            @click="quickFilter = quickFilter === 'vegetarian' ? '' : 'vegetarian'"
            :class="[
              'btn btn-sm',
              quickFilter === 'vegetarian' ? 'btn-success' : 'btn-outline'
            ]"
          >
            🌱 Vegetarianos
          </button>
          
          <button
            @click="quickFilter = quickFilter === 'vegan' ? '' : 'vegan'"
            :class="[
              'btn btn-sm',
              quickFilter === 'vegan' ? 'btn-success' : 'btn-outline'
            ]"
          >
            🌿 Veganos
          </button>
          
          <button
            @click="quickFilter = quickFilter === 'spicy' ? '' : 'spicy'"
            :class="[
              'btn btn-sm',
              quickFilter === 'spicy' ? 'btn-error' : 'btn-outline'
            ]"
          >
            🌶️ Picantes
          </button>
        </div>
      </div>
    </div>

    <!-- Lista de itens -->
    <div class="card">
      <div class="card-header">
        <h3 class="text-lg font-medium text-gray-900">
          Itens do Menu ({{ filteredItems.length }})
        </h3>
      </div>
      
      <div class="card-body p-0">
        <!-- Loading state -->
        <div v-if="isLoading" class="flex justify-center py-12">
          <div class="spinner w-8 h-8"></div>
        </div>

        <!-- Empty state -->
        <div v-else-if="filteredItems.length === 0" class="text-center py-12">
          <BookOpenIcon class="mx-auto h-12 w-12 text-gray-400" />
          <h3 class="mt-2 text-sm font-medium text-gray-900">Nenhum item encontrado</h3>
          <p class="mt-1 text-sm text-gray-500">
            {{ searchQuery || selectedCategoryId || quickFilter ? 'Tente ajustar os filtros de busca.' : 'Comece criando seu primeiro item do menu.' }}
          </p>
          <div v-if="!searchQuery && !selectedCategoryId && !quickFilter" class="mt-6">
            <button
              @click="openCreateModal"
              class="btn btn-primary"
            >
              <PlusIcon class="h-5 w-5 mr-2" />
              Novo Item
            </button>
          </div>
        </div>

        <!-- Grid de itens -->
        <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 p-6">
          <div
            v-for="item in filteredItems"
            :key="item.id"
            class="menu-item-card relative"
          >
            <!-- Imagem -->
            <div class="aspect-w-16 aspect-h-9 bg-gray-200 rounded-t-lg overflow-hidden">
              <img
                v-if="item.image_url"
                :src="item.image_url"
                :alt="item.name"
                class="w-full h-48 object-cover"
              />
              <div
                v-else
                class="w-full h-48 flex items-center justify-center bg-gray-100"
              >
                <PhotoIcon class="h-12 w-12 text-gray-400" />
              </div>
              
              <!-- Badges sobre a imagem -->
              <div class="absolute top-2 left-2 flex flex-wrap gap-1">
                <span v-if="item.is_featured" class="badge badge-warning text-xs">
                  ⭐ Destaque
                </span>
                <span v-if="!item.is_available" class="badge badge-error text-xs">
                  Indisponível
                </span>
                <span v-if="item.is_vegetarian" class="badge badge-success text-xs">
                  🌱
                </span>
                <span v-if="item.is_vegan" class="badge badge-success text-xs">
                  🌿
                </span>
                <span v-if="item.is_spicy" class="badge badge-error text-xs">
                  🌶️
                </span>
              </div>
            </div>

            <!-- Conteúdo -->
            <div class="p-4">
              <div class="flex justify-between items-start mb-2">
                <h4 class="text-lg font-medium text-gray-900 truncate">
                  {{ item.name }}
                </h4>
                <span class="text-lg font-bold text-primary-600 ml-2">
                  {{ formatCurrency(item.price) }}
                </span>
              </div>
              
              <p v-if="item.description" class="text-sm text-gray-500 mb-3 line-clamp-2">
                {{ item.description }}
              </p>
              
              <!-- Informações adicionais -->
              <div class="flex items-center justify-between text-xs text-gray-500 mb-4">
                <span>{{ getCategoryName(item.category_id) }}</span>
                <span v-if="item.prep_time_minutes">{{ item.prep_time_minutes }}min</span>
              </div>
              
              <!-- Ações -->
              <div class="flex items-center justify-between">
                <!-- Toggle disponibilidade -->
                <button
                  @click="toggleAvailability(item)"
                  :class="[
                    'btn btn-sm',
                    item.is_available ? 'btn-success' : 'btn-secondary'
                  ]"
                >
                  {{ item.is_available ? 'Disponível' : 'Indisponível' }}
                </button>
                
                <!-- Menu de ações -->
                <div class="flex items-center space-x-1">
                  <!-- Toggle destaque -->
                  <button
                    @click="toggleFeatured(item)"
                    :class="[
                      'p-2 rounded-md transition-colors duration-200',
                      item.is_featured
                        ? 'text-warning-600 hover:bg-warning-50'
                        : 'text-gray-400 hover:bg-gray-100'
                    ]"
                    :title="item.is_featured ? 'Remover destaque' : 'Destacar item'"
                  >
                    <StarIcon :class="item.is_featured ? 'fill-current' : ''" class="h-4 w-4" />
                  </button>

                  <!-- Upload de imagem -->
                  <button
                    @click="openImageUpload(item)"
                    class="p-2 rounded-md text-gray-400 hover:bg-gray-100 transition-colors duration-200"
                    title="Alterar imagem"
                  >
                    <PhotoIcon class="h-4 w-4" />
                  </button>

                  <!-- Editar -->
                  <button
                    @click="openEditModal(item)"
                    class="p-2 rounded-md text-gray-400 hover:bg-gray-100 transition-colors duration-200"
                    title="Editar item"
                  >
                    <PencilIcon class="h-4 w-4" />
                  </button>

                  <!-- Excluir -->
                  <button
                    @click="confirmDelete(item)"
                    class="p-2 rounded-md text-error-400 hover:bg-error-50 transition-colors duration-200"
                    title="Excluir item"
                  >
                    <TrashIcon class="h-4 w-4" />
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de criação/edição -->
    <MenuItemModal
      v-if="showModal"
      :item="selectedItem"
      :categories="activeCategories"
      @close="closeModal"
      @saved="handleItemSaved"
    />

    <!-- Modal de upload de imagem -->
    <ImageUploadModal
      v-if="showImageUpload"
      :title="`Imagem do item: ${selectedItem?.name}`"
      @close="closeImageUpload"
      @uploaded="handleImageUploaded"
    />

    <!-- Modal de confirmação de exclusão -->
    <ConfirmModal
      v-if="showDeleteConfirm"
      title="Excluir item"
      :message="`Tem certeza que deseja excluir o item '${itemToDelete?.name}'? Esta ação não pode ser desfeita.`"
      confirm-text="Excluir"
      confirm-class="btn-error"
      @confirm="handleDelete"
      @cancel="closeDeleteConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  PlusIcon,
  BookOpenIcon,
  PhotoIcon,
  PencilIcon,
  TrashIcon,
  StarIcon
} from '@heroicons/vue/24/outline'
import { useToast } from 'vue-toastification'

import { useMenuStore } from '@/stores/menu'
import type { MenuItem, Category } from '@/types'

// Componentes (serão criados posteriormente)
const MenuItemModal = defineAsyncComponent(() => import('@/components/modals/MenuItemModal.vue'))
const ImageUploadModal = defineAsyncComponent(() => import('@/components/modals/ImageUploadModal.vue'))
const ConfirmModal = defineAsyncComponent(() => import('@/components/modals/ConfirmModal.vue'))

const toast = useToast()
const menuStore = useMenuStore()

// Estado local
const searchQuery = ref('')
const selectedCategoryId = ref('')
const showUnavailable = ref(false)
const quickFilter = ref('')
const showModal = ref(false)
const showImageUpload = ref(false)
const showDeleteConfirm = ref(false)
const selectedItem = ref<MenuItem | null>(null)
const itemToDelete = ref<MenuItem | null>(null)

// Computed
const isLoading = computed(() => menuStore.isLoading)
const menuItems = computed(() => menuStore.menuItems)
const activeCategories = computed(() => menuStore.activeCategories)

const filteredItems = computed(() => {
  let filtered = menuItems.value

  // Filtrar por disponibilidade
  if (!showUnavailable.value) {
    filtered = filtered.filter(item => item.is_available)
  }

  // Filtrar por categoria
  if (selectedCategoryId.value) {
    filtered = filtered.filter(item => item.category_id === selectedCategoryId.value)
  }

  // Filtrar por busca
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(item =>
      item.name.toLowerCase().includes(query) ||
      item.description?.toLowerCase().includes(query)
    )
  }

  // Filtros rápidos
  if (quickFilter.value) {
    switch (quickFilter.value) {
      case 'featured':
        filtered = filtered.filter(item => item.is_featured)
        break
      case 'vegetarian':
        filtered = filtered.filter(item => item.is_vegetarian)
        break
      case 'vegan':
        filtered = filtered.filter(item => item.is_vegan)
        break
      case 'spicy':
        filtered = filtered.filter(item => item.is_spicy)
        break
    }
  }

  // Ordenar por sort_order e depois por nome
  return filtered.sort((a, b) => {
    if (a.sort_order !== b.sort_order) {
      return a.sort_order - b.sort_order
    }
    return a.name.localeCompare(b.name)
  })
})

// Métodos
const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value)
}

const getCategoryName = (categoryId: string): string => {
  const category = activeCategories.value.find(cat => cat.id === categoryId)
  return category?.name || 'Sem categoria'
}

const openCreateModal = () => {
  selectedItem.value = null
  showModal.value = true
}

const openEditModal = (item: MenuItem) => {
  selectedItem.value = item
  showModal.value = true
}

const closeModal = () => {
  showModal.value = false
  selectedItem.value = null
}

const openImageUpload = (item: MenuItem) => {
  selectedItem.value = item
  showImageUpload.value = true
}

const closeImageUpload = () => {
  showImageUpload.value = false
  selectedItem.value = null
}

const confirmDelete = (item: MenuItem) => {
  itemToDelete.value = item
  showDeleteConfirm.value = true
}

const closeDeleteConfirm = () => {
  showDeleteConfirm.value = false
  itemToDelete.value = null
}

const handleItemSaved = async () => {
  closeModal()
  toast.success('Item salvo com sucesso!')
  await loadMenuItems()
}

const handleImageUploaded = async (imageUrl: string) => {
  if (selectedItem.value) {
    toast.success('Imagem atualizada com sucesso!')
    await loadMenuItems()
  }
  closeImageUpload()
}

const toggleAvailability = async (item: MenuItem) => {
  try {
    await menuStore.toggleMenuItemAvailability(item.id)
    toast.success(
      item.is_available 
        ? 'Item marcado como indisponível' 
        : 'Item marcado como disponível'
    )
  } catch (error) {
    console.error('Erro ao alterar disponibilidade:', error)
    toast.error('Erro ao alterar disponibilidade do item')
  }
}

const toggleFeatured = async (item: MenuItem) => {
  try {
    await menuStore.toggleMenuItemFeatured(item.id)
    toast.success(
      item.is_featured 
        ? 'Item removido dos destaques' 
        : 'Item adicionado aos destaques'
    )
  } catch (error) {
    console.error('Erro ao alterar destaque:', error)
    toast.error('Erro ao alterar destaque do item')
  }
}

const handleDelete = async () => {
  if (!itemToDelete.value) return

  try {
    await menuStore.deleteMenuItem(itemToDelete.value.id)
    toast.success('Item excluído com sucesso!')
    closeDeleteConfirm()
  } catch (error) {
    console.error('Erro ao excluir item:', error)
    toast.error('Erro ao excluir item')
  }
}

const loadMenuItems = async () => {
  try {
    await menuStore.fetchMenuItems({ include_inactive: true })
  } catch (error) {
    console.error('Erro ao carregar itens:', error)
    toast.error('Erro ao carregar itens do menu')
  }
}

const loadCategories = async () => {
  try {
    await menuStore.fetchCategories()
  } catch (error) {
    console.error('Erro ao carregar categorias:', error)
  }
}

// Lifecycle
onMounted(async () => {
  await Promise.all([
    loadCategories(),
    loadMenuItems()
  ])
})

// Lazy loading de componentes
import { defineAsyncComponent } from 'vue'
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
