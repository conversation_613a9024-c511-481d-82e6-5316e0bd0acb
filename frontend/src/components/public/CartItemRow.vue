<template>
  <div class="px-6 py-4">
    <div class="flex items-start space-x-4">
      <!-- Imagem do item -->
      <div class="flex-shrink-0">
        <img
          v-if="item.menu_item?.image_url"
          :src="item.menu_item.image_url"
          :alt="item.item_name"
          class="h-16 w-16 rounded-lg object-cover"
        />
        <div
          v-else
          class="h-16 w-16 rounded-lg bg-gray-100 flex items-center justify-center"
        >
          <PhotoIcon class="h-8 w-8 text-gray-400" />
        </div>
      </div>

      <!-- Informações do item -->
      <div class="flex-1 min-w-0">
        <div class="flex items-start justify-between">
          <div class="flex-1">
            <h3 class="text-lg font-medium text-gray-900">
              {{ item.item_name }}
            </h3>
            
            <p v-if="item.item_description" class="text-sm text-gray-600 mt-1">
              {{ item.item_description }}
            </p>

            <!-- Observações do item -->
            <div v-if="item.notes || showNotesInput" class="mt-2">
              <div v-if="!showNotesInput" class="flex items-center space-x-2">
                <span class="text-sm text-gray-600">
                  <strong>Obs:</strong> {{ item.notes }}
                </span>
                <button
                  @click="editNotes"
                  class="text-xs text-primary-600 hover:text-primary-700"
                >
                  Editar
                </button>
              </div>
              
              <div v-else class="space-y-2">
                <textarea
                  v-model="notesInput"
                  rows="2"
                  class="w-full text-sm border border-gray-300 rounded-md px-2 py-1 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                  placeholder="Observações para este item..."
                />
                <div class="flex space-x-2">
                  <button
                    @click="saveNotes"
                    class="text-xs bg-primary-600 text-white px-2 py-1 rounded hover:bg-primary-700"
                  >
                    Salvar
                  </button>
                  <button
                    @click="cancelNotes"
                    class="text-xs bg-gray-300 text-gray-700 px-2 py-1 rounded hover:bg-gray-400"
                  >
                    Cancelar
                  </button>
                </div>
              </div>
            </div>

            <!-- Botão para adicionar observações -->
            <button
              v-if="!item.notes && !showNotesInput"
              @click="addNotes"
              class="text-sm text-primary-600 hover:text-primary-700 mt-2"
            >
              + Adicionar observação
            </button>
          </div>

          <!-- Preço unitário -->
          <div class="text-right ml-4">
            <p class="text-lg font-medium text-gray-900">
              {{ formatCurrency(item.unit_price) }}
            </p>
            <p class="text-sm text-gray-500">cada</p>
          </div>
        </div>

        <!-- Controles de quantidade e total -->
        <div class="flex items-center justify-between mt-4">
          <!-- Controles de quantidade -->
          <div class="flex items-center space-x-3">
            <button
              @click="decreaseQuantity"
              :disabled="isUpdating || item.quantity <= 1"
              class="w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transition-colors duration-200"
            >
              <MinusIcon class="h-4 w-4 text-gray-600" />
            </button>

            <div class="flex items-center space-x-2">
              <span class="text-lg font-medium text-gray-900 min-w-[2rem] text-center">
                {{ item.quantity }}
              </span>
              
              <div v-if="isUpdating" class="spinner w-4 h-4"></div>
            </div>

            <button
              @click="increaseQuantity"
              :disabled="isUpdating"
              class="w-8 h-8 rounded-full bg-primary-100 hover:bg-primary-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center transition-colors duration-200"
            >
              <PlusIcon class="h-4 w-4 text-primary-600" />
            </button>
          </div>

          <!-- Total do item e ações -->
          <div class="flex items-center space-x-4">
            <div class="text-right">
              <p class="text-xl font-bold text-gray-900">
                {{ formatCurrency(item.total_price) }}
              </p>
            </div>

            <button
              @click="removeItem"
              class="text-red-600 hover:text-red-700 p-1 transition-colors duration-200"
              title="Remover item"
            >
              <TrashIcon class="h-5 w-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  PhotoIcon,
  PlusIcon,
  MinusIcon,
  TrashIcon
} from '@heroicons/vue/24/outline'

import type { CartItem } from '@/types'

interface Props {
  item: CartItem
}

interface Emits {
  (e: 'update-quantity', item: CartItem, quantity: number): void
  (e: 'remove-item', item: CartItem): void
  (e: 'add-notes', item: CartItem, notes: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Estado local
const isUpdating = ref(false)
const showNotesInput = ref(false)
const notesInput = ref('')

// Methods
const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value)
}

const increaseQuantity = () => {
  if (isUpdating.value) return
  
  isUpdating.value = true
  emit('update-quantity', props.item, props.item.quantity + 1)
  
  // Simular delay para UX
  setTimeout(() => {
    isUpdating.value = false
  }, 500)
}

const decreaseQuantity = () => {
  if (isUpdating.value || props.item.quantity <= 1) return
  
  isUpdating.value = true
  emit('update-quantity', props.item, props.item.quantity - 1)
  
  // Simular delay para UX
  setTimeout(() => {
    isUpdating.value = false
  }, 500)
}

const removeItem = () => {
  emit('remove-item', props.item)
}

const addNotes = () => {
  notesInput.value = props.item.notes || ''
  showNotesInput.value = true
}

const editNotes = () => {
  notesInput.value = props.item.notes || ''
  showNotesInput.value = true
}

const saveNotes = () => {
  emit('add-notes', props.item, notesInput.value.trim())
  showNotesInput.value = false
  notesInput.value = ''
}

const cancelNotes = () => {
  showNotesInput.value = false
  notesInput.value = ''
}
</script>

<style scoped>
.spinner {
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
