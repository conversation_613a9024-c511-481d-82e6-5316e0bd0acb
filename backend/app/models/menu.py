"""
Modelos relacionados ao card<PERSON> (categorias e itens)
"""
from sqlalchemy import Column, String, Text, Boolean, Numeric, Integer, ForeignKey, Table
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.orm import relationship
from app.models.base import BaseModel, TenantMixin


# Tabela de associação para ingredientes de itens do menu
menu_item_ingredients = Table(
    'menu_item_ingredients',
    BaseModel.metadata,
    Column('menu_item_id', UUID(as_uuid=True), ForeignKey('menuitems.id'), primary_key=True),
    Column('ingredient_id', UUID(as_uuid=True), ForeignKey('ingredients.id'), primary_key=True)
)


class Category(BaseModel, TenantMixin):
    """Modelo de categoria do cardápio"""
    
    __tablename__ = "categories"
    
    name = Column(String(255), nullable=False)
    description = Column(Text)
    image_url = Column(Text)
    
    # Ordenação e exibição
    sort_order = Column(Integer, default=0)
    is_featured = Column(Boolean, default=False)
    
    # Configurações de disponibilidade
    available_all_day = Column(Boolean, default=True)
    available_start_time = Column(String(5))  # HH:MM
    available_end_time = Column(String(5))    # HH:MM
    
    # Relacionamentos
    establishment_id = Column(
        UUID(as_uuid=True),
        ForeignKey("establishments.id"),
        nullable=False,
        index=True
    )
    
    establishment = relationship("Establishment", back_populates="categories")
    menu_items = relationship("MenuItem", back_populates="category")
    
    def __repr__(self):
        return f"<Category(name='{self.name}')>"


class MenuItem(BaseModel, TenantMixin):
    """Modelo de item do cardápio"""
    
    __tablename__ = "menuitems"
    
    # Informações básicas
    name = Column(String(255), nullable=False)
    description = Column(Text)
    price = Column(Numeric(10, 2), nullable=False)
    
    # Imagens
    image_url = Column(Text)
    gallery_urls = Column(ARRAY(Text))  # Array de URLs de imagens
    
    # Configurações de disponibilidade
    is_available = Column(Boolean, default=True)
    stock_quantity = Column(Integer)  # Null = estoque ilimitado
    
    # Informações nutricionais
    calories = Column(Integer)
    protein = Column(Numeric(5, 2))    # gramas
    carbs = Column(Numeric(5, 2))      # gramas
    fat = Column(Numeric(5, 2))        # gramas
    fiber = Column(Numeric(5, 2))      # gramas
    
    # Tempo de preparo
    prep_time_minutes = Column(Integer, default=15)
    
    # Configurações especiais
    is_featured = Column(Boolean, default=False)
    is_spicy = Column(Boolean, default=False)
    is_vegetarian = Column(Boolean, default=False)
    is_vegan = Column(Boolean, default=False)
    is_gluten_free = Column(Boolean, default=False)
    is_dairy_free = Column(Boolean, default=False)
    
    # Ordenação
    sort_order = Column(Integer, default=0)
    
    # Relacionamentos
    category_id = Column(
        UUID(as_uuid=True),
        ForeignKey("categories.id"),
        nullable=False,
        index=True
    )
    
    establishment_id = Column(
        UUID(as_uuid=True),
        ForeignKey("establishments.id"),
        nullable=False,
        index=True
    )
    
    category = relationship("Category", back_populates="menu_items")
    establishment = relationship("Establishment", back_populates="menu_items")
    ingredients = relationship("Ingredient", secondary=menu_item_ingredients, back_populates="menu_items")
    customizations = relationship("MenuItemCustomization", back_populates="menu_item")
    order_items = relationship("OrderItem", back_populates="menu_item")
    cart_items = relationship("CartItem", back_populates="menu_item")
    reviews = relationship("Review", back_populates="menu_item")
    favorites = relationship("Favorite", back_populates="menu_item")
    
    def __repr__(self):
        return f"<MenuItem(name='{self.name}', price={self.price})>"
    
    @property
    def is_in_stock(self) -> bool:
        """Verificar se o item está em estoque"""
        if self.stock_quantity is None:
            return True  # Estoque ilimitado
        return self.stock_quantity > 0


class Ingredient(BaseModel, TenantMixin):
    """Modelo de ingrediente"""
    
    __tablename__ = "ingredients"
    
    name = Column(String(255), nullable=False)
    description = Column(Text)
    
    # Informações nutricionais por 100g
    calories_per_100g = Column(Integer)
    protein_per_100g = Column(Numeric(5, 2))
    carbs_per_100g = Column(Numeric(5, 2))
    fat_per_100g = Column(Numeric(5, 2))
    
    # Alérgenos
    is_allergen = Column(Boolean, default=False)
    allergen_type = Column(String(100))  # gluten, dairy, nuts, etc.
    
    # Relacionamentos
    establishment_id = Column(
        UUID(as_uuid=True),
        ForeignKey("establishments.id"),
        nullable=False,
        index=True
    )
    
    menu_items = relationship("MenuItem", secondary=menu_item_ingredients, back_populates="ingredients")
    
    def __repr__(self):
        return f"<Ingredient(name='{self.name}')>"


class MenuItemCustomization(BaseModel, TenantMixin):
    """Modelo de personalização de item do cardápio"""
    
    __tablename__ = "menu_item_customizations"
    
    name = Column(String(255), nullable=False)  # Ex: "Tamanho", "Adicionais"
    description = Column(Text)
    
    # Tipo de personalização
    customization_type = Column(String(50), nullable=False)  # single, multiple, text
    
    # Configurações
    is_required = Column(Boolean, default=False)
    max_selections = Column(Integer, default=1)
    min_selections = Column(Integer, default=0)
    
    # Relacionamentos
    menu_item_id = Column(
        UUID(as_uuid=True),
        ForeignKey("menuitems.id"),
        nullable=False,
        index=True
    )
    
    establishment_id = Column(
        UUID(as_uuid=True),
        ForeignKey("establishments.id"),
        nullable=False,
        index=True
    )
    
    menu_item = relationship("MenuItem", back_populates="customizations")
    options = relationship("CustomizationOption", back_populates="customization")
    
    def __repr__(self):
        return f"<MenuItemCustomization(name='{self.name}')>"


class CustomizationOption(BaseModel, TenantMixin):
    """Modelo de opção de personalização"""
    
    __tablename__ = "customization_options"
    
    name = Column(String(255), nullable=False)  # Ex: "Grande", "Queijo extra"
    description = Column(Text)
    price_modifier = Column(Numeric(10, 2), default=0.00)  # Valor adicional
    
    # Configurações
    is_default = Column(Boolean, default=False)
    sort_order = Column(Integer, default=0)
    
    # Relacionamentos
    customization_id = Column(
        UUID(as_uuid=True),
        ForeignKey("menu_item_customizations.id"),
        nullable=False,
        index=True
    )
    
    establishment_id = Column(
        UUID(as_uuid=True),
        ForeignKey("establishments.id"),
        nullable=False,
        index=True
    )
    
    customization = relationship("MenuItemCustomization", back_populates="options")
    
    def __repr__(self):
        return f"<CustomizationOption(name='{self.name}', price_modifier={self.price_modifier})>"
