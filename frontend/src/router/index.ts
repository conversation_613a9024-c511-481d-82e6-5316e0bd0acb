import { createRouter, createWebHistory } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

// Layouts
const AuthLayout = () => import('@/layouts/AuthLayout.vue')
const DashboardLayout = () => import('@/layouts/DashboardLayout.vue')
const PublicLayout = () => import('@/layouts/PublicLayout.vue')

// Páginas de autenticação
const LoginPage = () => import('@/views/auth/LoginPage.vue')
const RegisterPage = () => import('@/views/auth/RegisterPage.vue')
const ForgotPasswordPage = () => import('@/views/auth/ForgotPasswordPage.vue')

// Páginas do dashboard
const DashboardHome = () => import('@/views/dashboard/DashboardHome.vue')
const EstablishmentSettings = () => import('@/views/dashboard/EstablishmentSettings.vue')
const MenuManagement = () => import('@/views/dashboard/MenuManagement.vue')
const CategoryManagement = () => import('@/views/dashboard/CategoryManagement.vue')
const OrderManagement = () => import('@/views/dashboard/OrderManagement.vue')
const ReportsPage = () => import('@/views/dashboard/ReportsPage.vue')
const ProfilePage = () => import('@/views/dashboard/ProfilePage.vue')

// Páginas públicas
const HomePage = () => import('@/views/public/HomePage.vue')
const PublicMenuPage = () => import('@/views/public/PublicMenuPage.vue')
const MenuPage = () => import('@/views/public/MenuPage.vue')
const CartPage = () => import('@/views/public/CartPage.vue')
const CheckoutPage = () => import('@/views/public/CheckoutPage.vue')
const OrderTrackingPage = () => import('@/views/public/OrderTrackingPage.vue')
const AboutPage = () => import('@/views/public/AboutPage.vue')

// Página de erro
const NotFoundPage = () => import('@/views/NotFoundPage.vue')

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    // Rotas públicas
    {
      path: '/',
      component: PublicLayout,
      children: [
        {
          path: '',
          name: 'home',
          component: HomePage,
          meta: { title: 'Cardápio Digital' }
        },
        {
          path: '/sobre',
          name: 'about',
          component: AboutPage,
          meta: { title: 'Sobre' }
        },
        {
          path: '/menu/:slug',
          name: 'public-menu',
          component: MenuPage,
          meta: { title: 'Cardápio' }
        },
        {
          path: '/cart/:slug',
          name: 'cart',
          component: CartPage,
          meta: { title: 'Carrinho' }
        },
        {
          path: '/checkout/:slug',
          name: 'checkout',
          component: CheckoutPage,
          meta: { title: 'Finalizar Pedido' }
        },
        {
          path: '/order/:orderId/tracking',
          name: 'order-tracking',
          component: OrderTrackingPage,
          meta: { title: 'Acompanhar Pedido' }
        },
        {
          path: '/:slug',
          name: 'establishment-home',
          component: PublicMenuPage,
          meta: { title: 'Cardápio' }
        }
      ]
    },

    // Rotas de autenticação
    {
      path: '/auth',
      component: AuthLayout,
      meta: { requiresGuest: true },
      children: [
        {
          path: 'login',
          name: 'login',
          component: LoginPage,
          meta: { title: 'Login' }
        },
        {
          path: 'register',
          name: 'register',
          component: RegisterPage,
          meta: { title: 'Cadastro' }
        },
        {
          path: 'forgot-password',
          name: 'forgot-password',
          component: ForgotPasswordPage,
          meta: { title: 'Esqueci a Senha' }
        }
      ]
    },

    // Rotas do dashboard (protegidas)
    {
      path: '/dashboard',
      component: DashboardLayout,
      meta: { requiresAuth: true, requiresStaff: true },
      children: [
        {
          path: '',
          name: 'dashboard',
          component: DashboardHome,
          meta: { title: 'Dashboard' }
        },
        {
          path: 'establishment',
          name: 'establishment-settings',
          component: EstablishmentSettings,
          meta: { 
            title: 'Configurações do Estabelecimento',
            requiresManager: true 
          }
        },
        {
          path: 'menu',
          name: 'menu-management',
          component: MenuManagement,
          meta: { title: 'Gestão do Cardápio' }
        },
        {
          path: 'categories',
          name: 'category-management',
          component: CategoryManagement,
          meta: { title: 'Gestão de Categorias' }
        },
        {
          path: 'orders',
          name: 'order-management',
          component: OrderManagement,
          meta: { title: 'Gestão de Pedidos' }
        },
        {
          path: 'reports',
          name: 'reports',
          component: ReportsPage,
          meta: { 
            title: 'Relatórios',
            requiresManager: true 
          }
        },
        {
          path: 'profile',
          name: 'profile',
          component: ProfilePage,
          meta: { title: 'Perfil' }
        }
      ]
    },

    // Rota de erro 404
    {
      path: '/:pathMatch(.*)*',
      name: 'not-found',
      component: NotFoundPage,
      meta: { title: 'Página não encontrada' }
    }
  ]
})

// Guards de navegação
router.beforeEach(async (to, from, next) => {
  const authStore = useAuthStore()
  
  // Aguardar inicialização da autenticação
  if (!authStore.isInitialized) {
    await new Promise(resolve => {
      const unwatch = authStore.$subscribe(() => {
        if (authStore.isInitialized) {
          unwatch()
          resolve(true)
        }
      })
    })
  }

  // Verificar se a rota requer autenticação
  if (to.meta.requiresAuth && !authStore.isAuthenticated) {
    next({ name: 'login', query: { redirect: to.fullPath } })
    return
  }

  // Verificar se a rota é apenas para visitantes
  if (to.meta.requiresGuest && authStore.isAuthenticated) {
    next({ name: 'dashboard' })
    return
  }

  // Verificar se a rota requer staff
  if (to.meta.requiresStaff && !authStore.isStaff) {
    next({ name: 'home' })
    return
  }

  // Verificar se a rota requer gerente/proprietário
  if (to.meta.requiresManager && !authStore.canManageEstablishment) {
    next({ name: 'dashboard' })
    return
  }

  // Verificar se a rota requer proprietário
  if (to.meta.requiresOwner && !authStore.isOwner) {
    next({ name: 'dashboard' })
    return
  }

  next()
})

// Atualizar título da página
router.afterEach((to) => {
  const title = to.meta.title as string
  if (title) {
    document.title = `${title} | Cardápio Digital`
  } else {
    document.title = 'Cardápio Digital'
  }
})

export default router
