<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Gestão de Categorias</h1>
        <p class="text-gray-600">Organize seu cardápio em categorias</p>
      </div>
      
      <button
        @click="openCreateModal"
        class="btn btn-primary"
      >
        <PlusIcon class="h-5 w-5 mr-2" />
        Nova Categoria
      </button>
    </div>

    <!-- Filtros -->
    <div class="card">
      <div class="card-body">
        <div class="flex flex-col sm:flex-row gap-4">
          <div class="flex-1">
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Buscar categorias..."
              class="input"
            />
          </div>
          
          <div class="flex items-center space-x-4">
            <label class="flex items-center">
              <input
                v-model="showInactive"
                type="checkbox"
                class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
              />
              <span class="ml-2 text-sm text-gray-700">Mostrar inativas</span>
            </label>
          </div>
        </div>
      </div>
    </div>

    <!-- Lista de categorias -->
    <div class="card">
      <div class="card-header">
        <h3 class="text-lg font-medium text-gray-900">
          Categorias ({{ filteredCategories.length }})
        </h3>
      </div>
      
      <div class="card-body p-0">
        <!-- Loading state -->
        <div v-if="isLoading" class="flex justify-center py-12">
          <div class="spinner w-8 h-8"></div>
        </div>

        <!-- Empty state -->
        <div v-else-if="filteredCategories.length === 0" class="text-center py-12">
          <FolderIcon class="mx-auto h-12 w-12 text-gray-400" />
          <h3 class="mt-2 text-sm font-medium text-gray-900">Nenhuma categoria encontrada</h3>
          <p class="mt-1 text-sm text-gray-500">
            {{ searchQuery ? 'Tente ajustar os filtros de busca.' : 'Comece criando sua primeira categoria.' }}
          </p>
          <div v-if="!searchQuery" class="mt-6">
            <button
              @click="openCreateModal"
              class="btn btn-primary"
            >
              <PlusIcon class="h-5 w-5 mr-2" />
              Nova Categoria
            </button>
          </div>
        </div>

        <!-- Lista -->
        <div v-else class="divide-y divide-gray-200">
          <div
            v-for="category in filteredCategories"
            :key="category.id"
            class="p-6 hover:bg-gray-50 transition-colors duration-200"
          >
            <div class="flex items-center justify-between">
              <!-- Informações da categoria -->
              <div class="flex items-center space-x-4">
                <!-- Imagem -->
                <div class="flex-shrink-0">
                  <img
                    v-if="category.image_url"
                    :src="category.image_url"
                    :alt="category.name"
                    class="h-16 w-16 rounded-lg object-cover"
                  />
                  <div
                    v-else
                    class="h-16 w-16 rounded-lg bg-gray-200 flex items-center justify-center"
                  >
                    <PhotoIcon class="h-8 w-8 text-gray-400" />
                  </div>
                </div>

                <!-- Detalhes -->
                <div class="min-w-0 flex-1">
                  <div class="flex items-center space-x-2">
                    <h4 class="text-lg font-medium text-gray-900 truncate">
                      {{ category.name }}
                    </h4>
                    
                    <!-- Badges -->
                    <span
                      v-if="category.is_featured"
                      class="badge badge-primary"
                    >
                      Destaque
                    </span>
                    
                    <span
                      v-if="!category.is_active"
                      class="badge badge-secondary"
                    >
                      Inativa
                    </span>
                  </div>
                  
                  <p v-if="category.description" class="text-sm text-gray-500 mt-1">
                    {{ category.description }}
                  </p>
                  
                  <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                    <span>Ordem: {{ category.sort_order }}</span>
                    
                    <span v-if="!category.available_all_day">
                      {{ category.available_start_time }} - {{ category.available_end_time }}
                    </span>
                    <span v-else>Disponível 24h</span>
                  </div>
                </div>
              </div>

              <!-- Ações -->
              <div class="flex items-center space-x-2">
                <!-- Toggle destaque -->
                <button
                  @click="toggleFeatured(category)"
                  :class="[
                    'p-2 rounded-md transition-colors duration-200',
                    category.is_featured
                      ? 'text-warning-600 hover:bg-warning-50'
                      : 'text-gray-400 hover:bg-gray-100'
                  ]"
                  :title="category.is_featured ? 'Remover destaque' : 'Destacar categoria'"
                >
                  <StarIcon :class="category.is_featured ? 'fill-current' : ''" class="h-5 w-5" />
                </button>

                <!-- Upload de imagem -->
                <button
                  @click="openImageUpload(category)"
                  class="p-2 rounded-md text-gray-400 hover:bg-gray-100 transition-colors duration-200"
                  title="Alterar imagem"
                >
                  <PhotoIcon class="h-5 w-5" />
                </button>

                <!-- Editar -->
                <button
                  @click="openEditModal(category)"
                  class="p-2 rounded-md text-gray-400 hover:bg-gray-100 transition-colors duration-200"
                  title="Editar categoria"
                >
                  <PencilIcon class="h-5 w-5" />
                </button>

                <!-- Excluir -->
                <button
                  @click="confirmDelete(category)"
                  class="p-2 rounded-md text-error-400 hover:bg-error-50 transition-colors duration-200"
                  title="Excluir categoria"
                >
                  <TrashIcon class="h-5 w-5" />
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de criação/edição -->
    <CategoryModal
      v-if="showModal"
      :category="selectedCategory"
      @close="closeModal"
      @saved="handleCategorySaved"
    />

    <!-- Modal de upload de imagem -->
    <ImageUploadModal
      v-if="showImageUpload"
      :title="`Imagem da categoria: ${selectedCategory?.name}`"
      @close="closeImageUpload"
      @uploaded="handleImageUploaded"
    />

    <!-- Modal de confirmação de exclusão -->
    <ConfirmModal
      v-if="showDeleteConfirm"
      title="Excluir categoria"
      :message="`Tem certeza que deseja excluir a categoria '${categoryToDelete?.name}'? Esta ação não pode ser desfeita.`"
      confirm-text="Excluir"
      confirm-class="btn-error"
      @confirm="handleDelete"
      @cancel="closeDeleteConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  PlusIcon,
  FolderIcon,
  PhotoIcon,
  PencilIcon,
  TrashIcon,
  StarIcon
} from '@heroicons/vue/24/outline'
import { useToast } from 'vue-toastification'

import { useMenuStore } from '@/stores/menu'
import type { Category } from '@/types'

// Componentes (serão criados posteriormente)
const CategoryModal = defineAsyncComponent(() => import('@/components/modals/CategoryModal.vue'))
const ImageUploadModal = defineAsyncComponent(() => import('@/components/modals/ImageUploadModal.vue'))
const ConfirmModal = defineAsyncComponent(() => import('@/components/modals/ConfirmModal.vue'))

const toast = useToast()
const menuStore = useMenuStore()

// Estado local
const searchQuery = ref('')
const showInactive = ref(false)
const showModal = ref(false)
const showImageUpload = ref(false)
const showDeleteConfirm = ref(false)
const selectedCategory = ref<Category | null>(null)
const categoryToDelete = ref<Category | null>(null)

// Computed
const isLoading = computed(() => menuStore.isLoading)
const categories = computed(() => menuStore.categories)

const filteredCategories = computed(() => {
  let filtered = categories.value

  // Filtrar por status ativo/inativo
  if (!showInactive.value) {
    filtered = filtered.filter(cat => cat.is_active)
  }

  // Filtrar por busca
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(cat =>
      cat.name.toLowerCase().includes(query) ||
      cat.description?.toLowerCase().includes(query)
    )
  }

  // Ordenar por sort_order e depois por nome
  return filtered.sort((a, b) => {
    if (a.sort_order !== b.sort_order) {
      return a.sort_order - b.sort_order
    }
    return a.name.localeCompare(b.name)
  })
})

// Métodos
const openCreateModal = () => {
  selectedCategory.value = null
  showModal.value = true
}

const openEditModal = (category: Category) => {
  selectedCategory.value = category
  showModal.value = true
}

const closeModal = () => {
  showModal.value = false
  selectedCategory.value = null
}

const openImageUpload = (category: Category) => {
  selectedCategory.value = category
  showImageUpload.value = true
}

const closeImageUpload = () => {
  showImageUpload.value = false
  selectedCategory.value = null
}

const confirmDelete = (category: Category) => {
  categoryToDelete.value = category
  showDeleteConfirm.value = true
}

const closeDeleteConfirm = () => {
  showDeleteConfirm.value = false
  categoryToDelete.value = null
}

const handleCategorySaved = async () => {
  closeModal()
  toast.success('Categoria salva com sucesso!')
  await loadCategories()
}

const handleImageUploaded = async (imageUrl: string) => {
  if (selectedCategory.value) {
    toast.success('Imagem atualizada com sucesso!')
    await loadCategories()
  }
  closeImageUpload()
}

const toggleFeatured = async (category: Category) => {
  try {
    await menuStore.toggleCategoryFeatured(category.id)
    toast.success(
      category.is_featured 
        ? 'Categoria removida dos destaques' 
        : 'Categoria adicionada aos destaques'
    )
  } catch (error) {
    console.error('Erro ao alterar destaque:', error)
    toast.error('Erro ao alterar destaque da categoria')
  }
}

const handleDelete = async () => {
  if (!categoryToDelete.value) return

  try {
    await menuStore.deleteCategory(categoryToDelete.value.id)
    toast.success('Categoria excluída com sucesso!')
    closeDeleteConfirm()
  } catch (error) {
    console.error('Erro ao excluir categoria:', error)
    toast.error('Erro ao excluir categoria')
  }
}

const loadCategories = async () => {
  try {
    await menuStore.fetchCategories(true) // incluir inativas
  } catch (error) {
    console.error('Erro ao carregar categorias:', error)
    toast.error('Erro ao carregar categorias')
  }
}

// Lifecycle
onMounted(() => {
  loadCategories()
})

// Lazy loading de componentes
import { defineAsyncComponent } from 'vue'
</script>
