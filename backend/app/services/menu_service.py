"""
Serviço para gerenciamento do cardápio
"""
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_
from fastapi import HTTPException, status
from typing import List, Optional

from app.models.menu import Category, MenuItem, Ingredient, MenuItemCustomization, CustomizationOption
from app.models.establishment import Establishment
from app.schemas.menu import (
    CategoryCreate, 
    CategoryUpdate,
    MenuItemCreate,
    MenuItemUpdate,
    IngredientCreate,
    MenuItemCustomizationCreate
)


class MenuService:
    """Serviço para operações com cardápio"""
    
    def __init__(self, db: Session):
        self.db = db
    
    # ==================== CATEGORIAS ====================
    
    def create_category(self, category_data: CategoryCreate, establishment_id: str) -> Category:
        """
        Criar nova categoria
        
        Args:
            category_data: Dados da categoria
            establishment_id: ID do estabelecimento
            
        Returns:
            Categoria criada
        """
        category = Category(
            **category_data.dict(),
            establishment_id=establishment_id
        )
        
        self.db.add(category)
        self.db.commit()
        self.db.refresh(category)
        
        return category
    
    def get_categories(self, establishment_id: str, include_inactive: bool = False) -> List[Category]:
        """
        Listar categorias do estabelecimento
        
        Args:
            establishment_id: ID do estabelecimento
            include_inactive: Incluir categorias inativas
            
        Returns:
            Lista de categorias
        """
        query = self.db.query(Category).filter(
            Category.establishment_id == establishment_id
        )
        
        if not include_inactive:
            query = query.filter(Category.is_active == True)
        
        return query.order_by(Category.sort_order, Category.name).all()
    
    def get_category_by_id(self, category_id: str, establishment_id: str) -> Optional[Category]:
        """
        Buscar categoria por ID
        
        Args:
            category_id: ID da categoria
            establishment_id: ID do estabelecimento
            
        Returns:
            Categoria encontrada ou None
        """
        return self.db.query(Category).filter(
            and_(
                Category.id == category_id,
                Category.establishment_id == establishment_id,
                Category.is_active == True
            )
        ).first()
    
    def update_category(
        self, 
        category_id: str, 
        category_data: CategoryUpdate, 
        establishment_id: str
    ) -> Category:
        """
        Atualizar categoria
        
        Args:
            category_id: ID da categoria
            category_data: Dados para atualização
            establishment_id: ID do estabelecimento
            
        Returns:
            Categoria atualizada
            
        Raises:
            HTTPException: Se categoria não encontrada
        """
        category = self.get_category_by_id(category_id, establishment_id)
        
        if not category:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Categoria não encontrada"
            )
        
        # Atualizar campos fornecidos
        update_data = category_data.dict(exclude_unset=True)
        
        for field, value in update_data.items():
            setattr(category, field, value)
        
        self.db.commit()
        self.db.refresh(category)
        
        return category
    
    def delete_category(self, category_id: str, establishment_id: str) -> bool:
        """
        Desativar categoria (soft delete)
        
        Args:
            category_id: ID da categoria
            establishment_id: ID do estabelecimento
            
        Returns:
            True se desativada com sucesso
            
        Raises:
            HTTPException: Se categoria não encontrada ou tem itens ativos
        """
        category = self.get_category_by_id(category_id, establishment_id)
        
        if not category:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Categoria não encontrada"
            )
        
        # Verificar se há itens ativos na categoria
        active_items = self.db.query(MenuItem).filter(
            and_(
                MenuItem.category_id == category_id,
                MenuItem.is_active == True
            )
        ).count()
        
        if active_items > 0:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Não é possível excluir categoria com itens ativos"
            )
        
        category.is_active = False
        self.db.commit()
        
        return True
    
    # ==================== ITENS DO MENU ====================
    
    def create_menu_item(self, item_data: MenuItemCreate, establishment_id: str) -> MenuItem:
        """
        Criar novo item do menu
        
        Args:
            item_data: Dados do item
            establishment_id: ID do estabelecimento
            
        Returns:
            Item criado
            
        Raises:
            HTTPException: Se categoria não encontrada
        """
        # Verificar se categoria existe e pertence ao estabelecimento
        category = self.get_category_by_id(item_data.category_id, establishment_id)
        if not category:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Categoria não encontrada"
            )
        
        # Criar item
        item_dict = item_data.dict()
        item_dict['establishment_id'] = establishment_id
        
        menu_item = MenuItem(**item_dict)
        
        self.db.add(menu_item)
        self.db.commit()
        self.db.refresh(menu_item)
        
        return menu_item
    
    def get_menu_items(
        self, 
        establishment_id: str,
        category_id: Optional[str] = None,
        include_inactive: bool = False,
        search: Optional[str] = None,
        is_featured: Optional[bool] = None,
        is_available: Optional[bool] = None
    ) -> List[MenuItem]:
        """
        Listar itens do menu
        
        Args:
            establishment_id: ID do estabelecimento
            category_id: Filtrar por categoria
            include_inactive: Incluir itens inativos
            search: Buscar por nome
            is_featured: Filtrar por destaque
            is_available: Filtrar por disponibilidade
            
        Returns:
            Lista de itens
        """
        query = self.db.query(MenuItem).filter(
            MenuItem.establishment_id == establishment_id
        )
        
        if not include_inactive:
            query = query.filter(MenuItem.is_active == True)
        
        if category_id:
            query = query.filter(MenuItem.category_id == category_id)
        
        if search:
            query = query.filter(
                or_(
                    MenuItem.name.ilike(f"%{search}%"),
                    MenuItem.description.ilike(f"%{search}%")
                )
            )
        
        if is_featured is not None:
            query = query.filter(MenuItem.is_featured == is_featured)
        
        if is_available is not None:
            query = query.filter(MenuItem.is_available == is_available)
        
        return query.order_by(MenuItem.sort_order, MenuItem.name).all()
    
    def get_menu_item_by_id(self, item_id: str, establishment_id: str) -> Optional[MenuItem]:
        """
        Buscar item do menu por ID
        
        Args:
            item_id: ID do item
            establishment_id: ID do estabelecimento
            
        Returns:
            Item encontrado ou None
        """
        return self.db.query(MenuItem).filter(
            and_(
                MenuItem.id == item_id,
                MenuItem.establishment_id == establishment_id,
                MenuItem.is_active == True
            )
        ).first()
    
    def update_menu_item(
        self, 
        item_id: str, 
        item_data: MenuItemUpdate, 
        establishment_id: str
    ) -> MenuItem:
        """
        Atualizar item do menu
        
        Args:
            item_id: ID do item
            item_data: Dados para atualização
            establishment_id: ID do estabelecimento
            
        Returns:
            Item atualizado
            
        Raises:
            HTTPException: Se item não encontrado
        """
        menu_item = self.get_menu_item_by_id(item_id, establishment_id)
        
        if not menu_item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Item não encontrado"
            )
        
        # Se mudando categoria, verificar se existe
        if item_data.category_id:
            category = self.get_category_by_id(item_data.category_id, establishment_id)
            if not category:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Categoria não encontrada"
                )
        
        # Atualizar campos fornecidos
        update_data = item_data.dict(exclude_unset=True)
        
        for field, value in update_data.items():
            setattr(menu_item, field, value)
        
        self.db.commit()
        self.db.refresh(menu_item)
        
        return menu_item
    
    def delete_menu_item(self, item_id: str, establishment_id: str) -> bool:
        """
        Desativar item do menu (soft delete)
        
        Args:
            item_id: ID do item
            establishment_id: ID do estabelecimento
            
        Returns:
            True se desativado com sucesso
            
        Raises:
            HTTPException: Se item não encontrado
        """
        menu_item = self.get_menu_item_by_id(item_id, establishment_id)
        
        if not menu_item:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Item não encontrado"
            )
        
        menu_item.is_active = False
        self.db.commit()
        
        return True
    
    def get_full_menu(self, establishment_id: str, public: bool = False) -> dict:
        """
        Obter cardápio completo com categorias e itens
        
        Args:
            establishment_id: ID do estabelecimento
            public: Se é para visualização pública (apenas itens disponíveis)
            
        Returns:
            Cardápio completo organizado por categorias
        """
        # Buscar estabelecimento
        establishment = self.db.query(Establishment).filter(
            Establishment.id == establishment_id
        ).first()
        
        if not establishment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Estabelecimento não encontrado"
            )
        
        # Buscar categorias
        categories = self.get_categories(establishment_id)
        
        menu_data = {
            "establishment_id": str(establishment_id),
            "establishment_name": establishment.name,
            "categories": []
        }
        
        for category in categories:
            # Buscar itens da categoria
            items_query = self.db.query(MenuItem).filter(
                and_(
                    MenuItem.category_id == category.id,
                    MenuItem.is_active == True
                )
            )
            
            # Se é público, filtrar apenas disponíveis
            if public:
                items_query = items_query.filter(MenuItem.is_available == True)
            
            items = items_query.order_by(MenuItem.sort_order, MenuItem.name).all()
            
            # Se é público e categoria não tem itens disponíveis, pular
            if public and not items:
                continue
            
            category_data = {
                "id": str(category.id),
                "name": category.name,
                "description": category.description,
                "image_url": category.image_url,
                "sort_order": category.sort_order,
                "is_featured": category.is_featured,
                "items": []
            }
            
            for item in items:
                item_data = {
                    "id": str(item.id),
                    "name": item.name,
                    "description": item.description,
                    "price": float(item.price),
                    "image_url": item.image_url,
                    "gallery_urls": item.gallery_urls or [],
                    "calories": item.calories,
                    "protein": float(item.protein) if item.protein else None,
                    "carbs": float(item.carbs) if item.carbs else None,
                    "fat": float(item.fat) if item.fat else None,
                    "fiber": float(item.fiber) if item.fiber else None,
                    "prep_time_minutes": item.prep_time_minutes,
                    "is_spicy": item.is_spicy,
                    "is_vegetarian": item.is_vegetarian,
                    "is_vegan": item.is_vegan,
                    "is_gluten_free": item.is_gluten_free,
                    "is_dairy_free": item.is_dairy_free,
                    "is_available": item.is_available,
                    "is_in_stock": item.is_in_stock
                }
                
                if not public:
                    # Adicionar campos administrativos
                    item_data.update({
                        "is_featured": item.is_featured,
                        "stock_quantity": item.stock_quantity,
                        "sort_order": item.sort_order,
                        "is_active": item.is_active,
                        "created_at": item.created_at.isoformat(),
                        "updated_at": item.updated_at.isoformat()
                    })
                
                category_data["items"].append(item_data)
            
            menu_data["categories"].append(category_data)
        
        return menu_data
