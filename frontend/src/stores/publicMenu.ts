/**
 * Store para gerenciar o cardápio público (cliente)
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { 
  Category, 
  MenuItem, 
  Establishment,
  MenuFilters 
} from '@/types'
import { ApiService } from '@/services/api'

export const usePublicMenuStore = defineStore('publicMenu', () => {
  // Estado
  const establishment = ref<Establishment | null>(null)
  const categories = ref<Category[]>([])
  const menuItems = ref<MenuItem[]>([])
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  
  // Filtros
  const filters = ref<MenuFilters>({
    search: '',
    categoryId: '',
    isVegetarian: false,
    isVegan: false,
    isGlutenFree: false,
    isSpicy: false,
    minPrice: 0,
    maxPrice: 0,
    sortBy: 'name',
    sortOrder: 'asc'
  })

  // Computed
  const filteredMenuItems = computed(() => {
    let filtered = menuItems.value.filter(item => item.is_available)

    // Filtro por busca
    if (filters.value.search) {
      const search = filters.value.search.toLowerCase()
      filtered = filtered.filter(item =>
        item.name.toLowerCase().includes(search) ||
        item.description?.toLowerCase().includes(search)
      )
    }

    // Filtro por categoria
    if (filters.value.categoryId) {
      filtered = filtered.filter(item => item.category_id === filters.value.categoryId)
    }

    // Filtros dietéticos
    if (filters.value.isVegetarian) {
      filtered = filtered.filter(item => item.is_vegetarian)
    }
    if (filters.value.isVegan) {
      filtered = filtered.filter(item => item.is_vegan)
    }
    if (filters.value.isGlutenFree) {
      filtered = filtered.filter(item => item.is_gluten_free)
    }
    if (filters.value.isSpicy) {
      filtered = filtered.filter(item => item.is_spicy)
    }

    // Filtro por preço
    if (filters.value.minPrice > 0) {
      filtered = filtered.filter(item => item.price >= filters.value.minPrice)
    }
    if (filters.value.maxPrice > 0) {
      filtered = filtered.filter(item => item.price <= filters.value.maxPrice)
    }

    // Ordenação
    filtered.sort((a, b) => {
      let aValue: any, bValue: any

      switch (filters.value.sortBy) {
        case 'price':
          aValue = a.price
          bValue = b.price
          break
        case 'name':
        default:
          aValue = a.name.toLowerCase()
          bValue = b.name.toLowerCase()
          break
      }

      if (filters.value.sortOrder === 'desc') {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0
      } else {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0
      }
    })

    return filtered
  })

  const availableCategories = computed(() => {
    return categories.value.filter(category => 
      category.is_active && 
      menuItems.value.some(item => 
        item.category_id === category.id && item.is_available
      )
    )
  })

  const priceRange = computed(() => {
    const prices = menuItems.value
      .filter(item => item.is_available)
      .map(item => item.price)
    
    return {
      min: Math.min(...prices) || 0,
      max: Math.max(...prices) || 0
    }
  })

  const featuredItems = computed(() => {
    return menuItems.value
      .filter(item => item.is_available && item.is_featured)
      .slice(0, 6)
  })

  const itemsByCategory = computed(() => {
    const grouped: Record<string, MenuItem[]> = {}
    
    availableCategories.value.forEach(category => {
      grouped[category.id] = filteredMenuItems.value.filter(
        item => item.category_id === category.id
      )
    })
    
    return grouped
  })

  // Actions
  const fetchEstablishment = async (slug: string) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await ApiService.get<Establishment>(`/establishments/public/${slug}`)
      establishment.value = response
      return response
    } catch (err: any) {
      error.value = err.response?.data?.detail || 'Estabelecimento não encontrado'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const fetchPublicMenu = async (establishmentSlug: string) => {
    isLoading.value = true
    error.value = null

    try {
      const menu = await ApiService.get<{
        establishment: Establishment
        categories: Category[]
        items: MenuItem[]
      }>(`/menu/public/${establishmentSlug}`)
      
      establishment.value = menu.establishment
      categories.value = menu.categories
      menuItems.value = menu.items

      return menu
    } catch (err: any) {
      error.value = err.response?.data?.detail || 'Cardápio não encontrado'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const getItemById = (itemId: string): MenuItem | undefined => {
    return menuItems.value.find(item => item.id === itemId)
  }

  const getCategoryById = (categoryId: string): Category | undefined => {
    return categories.value.find(category => category.id === categoryId)
  }

  const getItemsByCategory = (categoryId: string): MenuItem[] => {
    return filteredMenuItems.value.filter(item => item.category_id === categoryId)
  }

  const updateFilters = (newFilters: Partial<MenuFilters>) => {
    filters.value = { ...filters.value, ...newFilters }
  }

  const clearFilters = () => {
    filters.value = {
      search: '',
      categoryId: '',
      isVegetarian: false,
      isVegan: false,
      isGlutenFree: false,
      isSpicy: false,
      minPrice: 0,
      maxPrice: 0,
      sortBy: 'name',
      sortOrder: 'asc'
    }
  }

  const searchItems = (query: string) => {
    filters.value.search = query
  }

  const filterByCategory = (categoryId: string) => {
    filters.value.categoryId = categoryId
  }

  const toggleDietaryFilter = (filterType: keyof MenuFilters) => {
    if (filterType in filters.value && typeof filters.value[filterType] === 'boolean') {
      ;(filters.value as any)[filterType] = !(filters.value as any)[filterType]
    }
  }

  const setPriceRange = (min: number, max: number) => {
    filters.value.minPrice = min
    filters.value.maxPrice = max
  }

  const setSorting = (sortBy: string, sortOrder: 'asc' | 'desc' = 'asc') => {
    filters.value.sortBy = sortBy
    filters.value.sortOrder = sortOrder
  }

  // Reset store
  const $reset = () => {
    establishment.value = null
    categories.value = []
    menuItems.value = []
    isLoading.value = false
    error.value = null
    clearFilters()
  }

  return {
    // Estado
    establishment,
    categories,
    menuItems,
    isLoading,
    error,
    filters,

    // Computed
    filteredMenuItems,
    availableCategories,
    priceRange,
    featuredItems,
    itemsByCategory,

    // Actions
    fetchEstablishment,
    fetchPublicMenu,
    getItemById,
    getCategoryById,
    getItemsByCategory,
    updateFilters,
    clearFilters,
    searchItems,
    filterByCategory,
    toggleDietaryFilter,
    setPriceRange,
    setSorting,
    $reset
  }
})
