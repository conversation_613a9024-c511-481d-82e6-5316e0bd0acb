<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Resumo do Pedido</h3>

    <!-- Tipo de pedido -->
    <div class="mb-6">
      <h4 class="text-sm font-medium text-gray-700 mb-3">Tipo de Pedido</h4>
      <div class="space-y-2">
        <label
          v-for="option in orderTypeOptions"
          :key="option.value"
          class="flex items-center cursor-pointer"
        >
          <input
            type="radio"
            :value="option.value"
            :checked="orderType === option.value"
            @change="handleOrderTypeChange(option.value)"
            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
          />
          <div class="ml-3 flex-1">
            <div class="flex items-center">
              <component :is="option.icon" class="h-5 w-5 mr-2 text-gray-500" />
              <span class="text-sm font-medium text-gray-900">{{ option.label }}</span>
            </div>
            <p class="text-xs text-gray-500 mt-1">{{ option.description }}</p>
          </div>
        </label>
      </div>
    </div>

    <!-- Resumo financeiro -->
    <div class="space-y-3 mb-6">
      <div class="flex justify-between text-sm">
        <span class="text-gray-600">Subtotal</span>
        <span class="text-gray-900">{{ formatCurrency(subtotal) }}</span>
      </div>

      <div v-if="deliveryFee > 0" class="flex justify-between text-sm">
        <span class="text-gray-600">Taxa de entrega</span>
        <span class="text-gray-900">{{ formatCurrency(deliveryFee) }}</span>
      </div>

      <div v-if="serviceFee > 0" class="flex justify-between text-sm">
        <span class="text-gray-600">Taxa de serviço</span>
        <span class="text-gray-900">{{ formatCurrency(serviceFee) }}</span>
      </div>

      <div v-if="discount > 0" class="flex justify-between text-sm">
        <span class="text-green-600">Desconto</span>
        <span class="text-green-600">-{{ formatCurrency(discount) }}</span>
      </div>

      <div class="border-t border-gray-200 pt-3">
        <div class="flex justify-between">
          <span class="text-base font-medium text-gray-900">Total</span>
          <span class="text-xl font-bold text-gray-900">{{ formatCurrency(total) }}</span>
        </div>
      </div>
    </div>

    <!-- Tempo estimado -->
    <div v-if="estimatedTime" class="mb-6 p-3 bg-blue-50 rounded-lg">
      <div class="flex items-center">
        <ClockIcon class="h-5 w-5 text-blue-500 mr-2" />
        <div>
          <p class="text-sm font-medium text-blue-900">
            Tempo estimado: {{ estimatedTime }} min
          </p>
          <p class="text-xs text-blue-700">
            {{ orderType === 'delivery' ? 'Para entrega' : 'Para preparo' }}
          </p>
        </div>
      </div>
    </div>

    <!-- Cupom de desconto -->
    <div class="mb-6">
      <div class="flex space-x-2">
        <input
          v-model="couponCode"
          type="text"
          placeholder="Código do cupom"
          class="flex-1 text-sm border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
          :disabled="isApplyingCoupon"
        />
        <button
          @click="applyCoupon"
          :disabled="!couponCode.trim() || isApplyingCoupon"
          class="px-4 py-2 text-sm bg-gray-100 text-gray-700 rounded-md hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed transition-colors duration-200"
        >
          <div v-if="isApplyingCoupon" class="spinner w-4 h-4"></div>
          <span v-else>Aplicar</span>
        </button>
      </div>
      
      <div v-if="appliedCoupon" class="mt-2 flex items-center justify-between text-sm">
        <span class="text-green-600 font-medium">{{ appliedCoupon.code }} aplicado</span>
        <button
          @click="removeCoupon"
          class="text-red-600 hover:text-red-700"
        >
          Remover
        </button>
      </div>
    </div>

    <!-- Botão de checkout -->
    <button
      @click="proceedToCheckout"
      :disabled="!isValid"
      :class="[
        'w-full py-3 px-4 rounded-md font-medium transition-colors duration-200',
        isValid
          ? 'bg-primary-600 hover:bg-primary-700 text-white'
          : 'bg-gray-300 text-gray-500 cursor-not-allowed'
      ]"
    >
      {{ checkoutButtonText }}
    </button>

    <!-- Informações de segurança -->
    <div class="mt-4 flex items-center justify-center text-xs text-gray-500">
      <ShieldCheckIcon class="h-4 w-4 mr-1" />
      <span>Pagamento 100% seguro</span>
    </div>

    <!-- Política de cancelamento -->
    <div class="mt-4 text-xs text-gray-500 text-center">
      <p>
        Você pode cancelar seu pedido gratuitamente até a confirmação pelo restaurante.
      </p>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  TruckIcon,
  BuildingStorefrontIcon,
  UserGroupIcon,
  ClockIcon,
  ShieldCheckIcon
} from '@heroicons/vue/24/outline'
import { useToast } from 'vue-toastification'

interface Props {
  subtotal: number
  deliveryFee: number
  serviceFee: number
  total: number
  orderType: 'delivery' | 'pickup' | 'table_service'
  isValid: boolean
  estimatedTime?: number
  discount?: number
}

interface Emits {
  (e: 'change-order-type', orderType: 'delivery' | 'pickup' | 'table_service'): void
  (e: 'proceed-checkout'): void
  (e: 'apply-coupon', code: string): void
  (e: 'remove-coupon'): void
}

const props = withDefaults(defineProps<Props>(), {
  discount: 0
})

const emit = defineEmits<Emits>()
const toast = useToast()

// Estado local
const couponCode = ref('')
const isApplyingCoupon = ref(false)
const appliedCoupon = ref<{ code: string; discount: number } | null>(null)

// Computed
const orderTypeOptions = [
  {
    value: 'delivery',
    label: 'Delivery',
    description: 'Entrega no seu endereço',
    icon: TruckIcon
  },
  {
    value: 'pickup',
    label: 'Retirada',
    description: 'Retirar no restaurante',
    icon: BuildingStorefrontIcon
  },
  {
    value: 'table_service',
    label: 'Mesa',
    description: 'Atendimento na mesa',
    icon: UserGroupIcon
  }
]

const checkoutButtonText = computed(() => {
  if (!props.isValid) {
    return 'Carrinho inválido'
  }
  
  switch (props.orderType) {
    case 'delivery':
      return 'Finalizar Pedido - Delivery'
    case 'pickup':
      return 'Finalizar Pedido - Retirada'
    case 'table_service':
      return 'Finalizar Pedido - Mesa'
    default:
      return 'Finalizar Pedido'
  }
})

// Methods
const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value)
}

const handleOrderTypeChange = (newOrderType: 'delivery' | 'pickup' | 'table_service') => {
  emit('change-order-type', newOrderType)
}

const proceedToCheckout = () => {
  if (props.isValid) {
    emit('proceed-checkout')
  }
}

const applyCoupon = async () => {
  if (!couponCode.value.trim()) return

  isApplyingCoupon.value = true

  try {
    // Simular aplicação de cupom
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Mock: verificar se é um cupom válido
    const validCoupons = ['DESCONTO10', 'PRIMEIRACOMPRA', 'FRETEGRATIS']
    
    if (validCoupons.includes(couponCode.value.toUpperCase())) {
      appliedCoupon.value = {
        code: couponCode.value.toUpperCase(),
        discount: props.subtotal * 0.1 // 10% de desconto
      }
      
      emit('apply-coupon', couponCode.value.toUpperCase())
      toast.success('Cupom aplicado com sucesso!')
      couponCode.value = ''
    } else {
      toast.error('Cupom inválido ou expirado')
    }
  } catch (error) {
    toast.error('Erro ao aplicar cupom')
  } finally {
    isApplyingCoupon.value = false
  }
}

const removeCoupon = () => {
  appliedCoupon.value = null
  emit('remove-coupon')
  toast.success('Cupom removido')
}
</script>

<style scoped>
.spinner {
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
