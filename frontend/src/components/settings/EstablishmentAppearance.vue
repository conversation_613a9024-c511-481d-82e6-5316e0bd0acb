<template>
  <div class="space-y-6">
    <!-- <PERSON><PERSON> e Banner -->
    <div class="card">
      <div class="card-header">
        <h3 class="text-lg font-medium text-gray-900">Logo e Banner</h3>
        <p class="text-sm text-gray-500">Personalize a identidade visual do seu estabelecimento</p>
      </div>
      
      <div class="card-body">
        <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
          <!-- Logo -->
          <div>
            <h4 class="text-sm font-medium text-gray-900 mb-4">Logo</h4>
            <div class="space-y-4">
              <!-- Preview do logo -->
              <div class="flex items-center justify-center w-32 h-32 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50">
                <img
                  v-if="establishment?.logo_url"
                  :src="establishment.logo_url"
                  :alt="establishment.name"
                  class="w-full h-full object-cover rounded-lg"
                />
                <div v-else class="text-center">
                  <PhotoIcon class="mx-auto h-8 w-8 text-gray-400" />
                  <p class="text-xs text-gray-500 mt-1">Sem logo</p>
                </div>
              </div>
              
              <!-- Botões de ação -->
              <div class="flex space-x-2">
                <button
                  @click="openLogoUpload"
                  class="btn btn-outline btn-sm"
                  :disabled="isUploadingLogo"
                >
                  <div v-if="isUploadingLogo" class="flex items-center">
                    <div class="spinner w-3 h-3 mr-1"></div>
                    Enviando...
                  </div>
                  <span v-else>
                    {{ establishment?.logo_url ? 'Alterar' : 'Enviar' }} Logo
                  </span>
                </button>
                
                <button
                  v-if="establishment?.logo_url"
                  @click="removeLogo"
                  class="btn btn-outline btn-sm text-error-600 hover:text-error-700"
                >
                  Remover
                </button>
              </div>
              
              <p class="text-xs text-gray-500">
                Recomendado: 200x200px, PNG ou JPG, máximo 2MB
              </p>
            </div>
          </div>

          <!-- Banner -->
          <div>
            <h4 class="text-sm font-medium text-gray-900 mb-4">Banner</h4>
            <div class="space-y-4">
              <!-- Preview do banner -->
              <div class="flex items-center justify-center w-full h-32 border-2 border-dashed border-gray-300 rounded-lg bg-gray-50">
                <img
                  v-if="establishment?.banner_url"
                  :src="establishment.banner_url"
                  :alt="establishment.name"
                  class="w-full h-full object-cover rounded-lg"
                />
                <div v-else class="text-center">
                  <PhotoIcon class="mx-auto h-8 w-8 text-gray-400" />
                  <p class="text-xs text-gray-500 mt-1">Sem banner</p>
                </div>
              </div>
              
              <!-- Botões de ação -->
              <div class="flex space-x-2">
                <button
                  @click="openBannerUpload"
                  class="btn btn-outline btn-sm"
                  :disabled="isUploadingBanner"
                >
                  <div v-if="isUploadingBanner" class="flex items-center">
                    <div class="spinner w-3 h-3 mr-1"></div>
                    Enviando...
                  </div>
                  <span v-else>
                    {{ establishment?.banner_url ? 'Alterar' : 'Enviar' }} Banner
                  </span>
                </button>
                
                <button
                  v-if="establishment?.banner_url"
                  @click="removeBanner"
                  class="btn btn-outline btn-sm text-error-600 hover:text-error-700"
                >
                  Remover
                </button>
              </div>
              
              <p class="text-xs text-gray-500">
                Recomendado: 1200x400px, PNG ou JPG, máximo 5MB
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Cores do tema -->
    <div class="card">
      <div class="card-header">
        <h3 class="text-lg font-medium text-gray-900">Cores do Tema</h3>
        <p class="text-sm text-gray-500">Personalize as cores do seu cardápio</p>
      </div>
      
      <div class="card-body">
        <form @submit.prevent="handleSubmitColors" class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
            <!-- Cor primária -->
            <div class="form-group">
              <label for="primary_color" class="form-label">
                Cor Primária
              </label>
              <div class="flex items-center space-x-3">
                <input
                  id="primary_color"
                  v-model="colorForm.primary_color"
                  type="color"
                  class="h-10 w-16 border border-gray-300 rounded-md cursor-pointer"
                  :disabled="isLoadingColors"
                />
                <input
                  v-model="colorForm.primary_color"
                  type="text"
                  class="input flex-1"
                  placeholder="#3B82F6"
                  :disabled="isLoadingColors"
                />
              </div>
              <p class="form-help">
                Cor principal dos botões e destaques
              </p>
            </div>

            <!-- Cor secundária -->
            <div class="form-group">
              <label for="secondary_color" class="form-label">
                Cor Secundária
              </label>
              <div class="flex items-center space-x-3">
                <input
                  id="secondary_color"
                  v-model="colorForm.secondary_color"
                  type="color"
                  class="h-10 w-16 border border-gray-300 rounded-md cursor-pointer"
                  :disabled="isLoadingColors"
                />
                <input
                  v-model="colorForm.secondary_color"
                  type="text"
                  class="input flex-1"
                  placeholder="#6B7280"
                  :disabled="isLoadingColors"
                />
              </div>
              <p class="form-help">
                Cor para elementos secundários
              </p>
            </div>

            <!-- Cor de destaque -->
            <div class="form-group">
              <label for="accent_color" class="form-label">
                Cor de Destaque
              </label>
              <div class="flex items-center space-x-3">
                <input
                  id="accent_color"
                  v-model="colorForm.accent_color"
                  type="color"
                  class="h-10 w-16 border border-gray-300 rounded-md cursor-pointer"
                  :disabled="isLoadingColors"
                />
                <input
                  v-model="colorForm.accent_color"
                  type="text"
                  class="input flex-1"
                  placeholder="#F59E0B"
                  :disabled="isLoadingColors"
                />
              </div>
              <p class="form-help">
                Cor para preços e ofertas especiais
              </p>
            </div>
          </div>

          <!-- Preview das cores -->
          <div class="bg-gray-50 rounded-lg p-6">
            <h4 class="text-sm font-medium text-gray-900 mb-4">Preview</h4>
            <div class="space-y-4">
              <!-- Botão primário -->
              <div class="flex items-center space-x-4">
                <button
                  type="button"
                  :style="{ backgroundColor: colorForm.primary_color }"
                  class="px-4 py-2 text-white rounded-md font-medium"
                >
                  Botão Primário
                </button>
                <span class="text-sm text-gray-600">Botões principais</span>
              </div>

              <!-- Texto secundário -->
              <div class="flex items-center space-x-4">
                <span
                  :style="{ color: colorForm.secondary_color }"
                  class="text-sm font-medium"
                >
                  Texto Secundário
                </span>
                <span class="text-sm text-gray-600">Descrições e informações</span>
              </div>

              <!-- Preço -->
              <div class="flex items-center space-x-4">
                <span
                  :style="{ color: colorForm.accent_color }"
                  class="text-lg font-bold"
                >
                  R$ 29,90
                </span>
                <span class="text-sm text-gray-600">Preços e ofertas</span>
              </div>
            </div>
          </div>

          <!-- Botões -->
          <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              @click="resetColors"
              class="btn btn-outline"
              :disabled="isLoadingColors"
            >
              Restaurar Padrão
            </button>
            
            <button
              type="submit"
              :disabled="isLoadingColors"
              class="btn btn-primary"
            >
              <div v-if="isLoadingColors" class="flex items-center">
                <div class="spinner w-4 h-4 mr-2"></div>
                Salvando...
              </div>
              <span v-else>
                Salvar Cores
              </span>
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Modal de upload -->
    <ImageUploadModal
      v-if="showUploadModal"
      :title="uploadTitle"
      @close="closeUploadModal"
      @uploaded="handleImageUploaded"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { PhotoIcon } from '@heroicons/vue/24/outline'
import { useToast } from 'vue-toastification'

import { useEstablishmentStore } from '@/stores/establishment'

// Componentes
const ImageUploadModal = defineAsyncComponent(() => import('@/components/modals/ImageUploadModal.vue'))

const toast = useToast()
const establishmentStore = useEstablishmentStore()

// Estado
const isUploadingLogo = ref(false)
const isUploadingBanner = ref(false)
const isLoadingColors = ref(false)
const showUploadModal = ref(false)
const uploadType = ref<'logo' | 'banner'>('logo')

const colorForm = ref({
  primary_color: '#3B82F6',
  secondary_color: '#6B7280',
  accent_color: '#F59E0B'
})

// Computed
const establishment = computed(() => establishmentStore.currentEstablishment)

const uploadTitle = computed(() => {
  return uploadType.value === 'logo' ? 'Upload do Logo' : 'Upload do Banner'
})

// Métodos
const openLogoUpload = () => {
  uploadType.value = 'logo'
  showUploadModal.value = true
}

const openBannerUpload = () => {
  uploadType.value = 'banner'
  showUploadModal.value = true
}

const closeUploadModal = () => {
  showUploadModal.value = false
}

const handleImageUploaded = async (imageUrl: string) => {
  try {
    if (uploadType.value === 'logo') {
      isUploadingLogo.value = true
      await establishmentStore.uploadLogo(new File([], 'logo'))
    } else {
      isUploadingBanner.value = true
      await establishmentStore.uploadBanner(new File([], 'banner'))
    }
    
    toast.success(`${uploadType.value === 'logo' ? 'Logo' : 'Banner'} atualizado com sucesso!`)
    closeUploadModal()
  } catch (error) {
    console.error('Erro no upload:', error)
    toast.error(`Erro ao fazer upload do ${uploadType.value === 'logo' ? 'logo' : 'banner'}`)
  } finally {
    isUploadingLogo.value = false
    isUploadingBanner.value = false
  }
}

const removeLogo = async () => {
  if (!confirm('Tem certeza que deseja remover o logo?')) return
  
  try {
    // Aqui seria feita a chamada para remover o logo
    // await establishmentStore.removeLogo()
    toast.success('Logo removido com sucesso!')
  } catch (error) {
    console.error('Erro ao remover logo:', error)
    toast.error('Erro ao remover logo')
  }
}

const removeBanner = async () => {
  if (!confirm('Tem certeza que deseja remover o banner?')) return
  
  try {
    // Aqui seria feita a chamada para remover o banner
    // await establishmentStore.removeBanner()
    toast.success('Banner removido com sucesso!')
  } catch (error) {
    console.error('Erro ao remover banner:', error)
    toast.error('Erro ao remover banner')
  }
}

const handleSubmitColors = async () => {
  isLoadingColors.value = true
  
  try {
    await establishmentStore.updateEstablishment(colorForm.value)
    toast.success('Cores atualizadas com sucesso!')
  } catch (error) {
    console.error('Erro ao atualizar cores:', error)
    toast.error('Erro ao atualizar cores')
  } finally {
    isLoadingColors.value = false
  }
}

const resetColors = () => {
  colorForm.value = {
    primary_color: '#3B82F6',
    secondary_color: '#6B7280',
    accent_color: '#F59E0B'
  }
}

const loadColors = () => {
  if (establishment.value) {
    colorForm.value = {
      primary_color: establishment.value.primary_color,
      secondary_color: establishment.value.secondary_color,
      accent_color: establishment.value.accent_color
    }
  }
}

// Lifecycle
onMounted(() => {
  loadColors()
})

// Lazy loading de componentes
import { defineAsyncComponent } from 'vue'
</script>
