<template>
  <TransitionRoot as="template" :show="true">
    <Dialog as="div" class="relative z-50" @close="$emit('close')">
      <TransitionChild
        as="template"
        enter="ease-out duration-300"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="ease-in duration-200"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
      </TransitionChild>

      <div class="fixed inset-0 z-10 overflow-y-auto">
        <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <TransitionChild
            as="template"
            enter="ease-out duration-300"
            enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            enter-to="opacity-100 translate-y-0 sm:scale-100"
            leave="ease-in duration-200"
            leave-from="opacity-100 translate-y-0 sm:scale-100"
            leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
          >
            <DialogPanel class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
              <!-- Header -->
              <div class="flex items-center justify-between mb-6">
                <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900">
                  {{ title }}
                </DialogTitle>
                <button
                  @click="$emit('close')"
                  class="rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <XMarkIcon class="h-6 w-6" />
                </button>
              </div>

              <!-- Área de upload -->
              <div class="space-y-6">
                <!-- Drag and drop area -->
                <div
                  @drop="handleDrop"
                  @dragover.prevent
                  @dragenter.prevent
                  :class="[
                    'border-2 border-dashed rounded-lg p-6 text-center transition-colors duration-200',
                    isDragging ? 'border-primary-400 bg-primary-50' : 'border-gray-300 hover:border-gray-400'
                  ]"
                >
                  <input
                    ref="fileInput"
                    type="file"
                    accept="image/*"
                    @change="handleFileSelect"
                    class="hidden"
                    :disabled="isUploading"
                  />

                  <!-- Preview da imagem -->
                  <div v-if="previewUrl" class="mb-4">
                    <img
                      :src="previewUrl"
                      alt="Preview"
                      class="mx-auto h-32 w-32 object-cover rounded-lg"
                    />
                  </div>

                  <!-- Ícone e texto -->
                  <div v-else>
                    <PhotoIcon class="mx-auto h-12 w-12 text-gray-400" />
                    <div class="mt-4">
                      <p class="text-sm text-gray-600">
                        <button
                          type="button"
                          @click="$refs.fileInput?.click()"
                          class="font-medium text-primary-600 hover:text-primary-500"
                          :disabled="isUploading"
                        >
                          Clique para selecionar
                        </button>
                        ou arraste uma imagem aqui
                      </p>
                      <p class="text-xs text-gray-500 mt-1">
                        PNG, JPG, JPEG ou WebP até 5MB
                      </p>
                    </div>
                  </div>
                </div>

                <!-- Informações do arquivo -->
                <div v-if="selectedFile" class="bg-gray-50 rounded-lg p-4">
                  <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-3">
                      <DocumentIcon class="h-8 w-8 text-gray-400" />
                      <div>
                        <p class="text-sm font-medium text-gray-900">
                          {{ selectedFile.name }}
                        </p>
                        <p class="text-xs text-gray-500">
                          {{ formatFileSize(selectedFile.size) }}
                        </p>
                      </div>
                    </div>
                    
                    <button
                      v-if="!isUploading"
                      @click="clearFile"
                      class="text-gray-400 hover:text-gray-500"
                    >
                      <XMarkIcon class="h-5 w-5" />
                    </button>
                  </div>

                  <!-- Barra de progresso -->
                  <div v-if="isUploading" class="mt-4">
                    <div class="flex items-center justify-between text-sm">
                      <span class="text-gray-600">Enviando...</span>
                      <span class="text-gray-900 font-medium">{{ uploadProgress }}%</span>
                    </div>
                    <div class="mt-2 bg-gray-200 rounded-full h-2">
                      <div
                        class="bg-primary-600 h-2 rounded-full transition-all duration-300"
                        :style="{ width: `${uploadProgress}%` }"
                      ></div>
                    </div>
                  </div>
                </div>

                <!-- Erro -->
                <div v-if="error" class="rounded-md bg-error-50 p-4">
                  <div class="flex">
                    <ExclamationTriangleIcon class="h-5 w-5 text-error-400" />
                    <div class="ml-3">
                      <h3 class="text-sm font-medium text-error-800">
                        Erro no upload
                      </h3>
                      <div class="mt-2 text-sm text-error-700">
                        {{ error }}
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Botões -->
                <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                  <button
                    type="button"
                    @click="$emit('close')"
                    class="btn btn-outline"
                    :disabled="isUploading"
                  >
                    Cancelar
                  </button>
                  
                  <button
                    type="button"
                    @click="handleUpload"
                    :disabled="!selectedFile || isUploading"
                    class="btn btn-primary"
                  >
                    <div v-if="isUploading" class="flex items-center">
                      <div class="spinner w-4 h-4 mr-2"></div>
                      Enviando...
                    </div>
                    <span v-else>
                      Enviar Imagem
                    </span>
                  </button>
                </div>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue'
import { XMarkIcon, PhotoIcon, DocumentIcon, ExclamationTriangleIcon } from '@heroicons/vue/24/outline'

interface Props {
  title: string
}

interface Emits {
  (e: 'close'): void
  (e: 'uploaded', imageUrl: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Estado
const selectedFile = ref<File | null>(null)
const previewUrl = ref<string | null>(null)
const isUploading = ref(false)
const uploadProgress = ref(0)
const error = ref<string | null>(null)
const isDragging = ref(false)

// Computed
const fileInput = ref<HTMLInputElement>()

// Métodos
const validateFile = (file: File): string | null => {
  // Verificar tipo
  const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/webp']
  if (!allowedTypes.includes(file.type)) {
    return 'Tipo de arquivo não suportado. Use PNG, JPG, JPEG ou WebP.'
  }

  // Verificar tamanho (5MB)
  const maxSize = 5 * 1024 * 1024 // 5MB
  if (file.size > maxSize) {
    return 'Arquivo muito grande. Tamanho máximo: 5MB.'
  }

  return null
}

const setFile = (file: File) => {
  error.value = null
  
  const validationError = validateFile(file)
  if (validationError) {
    error.value = validationError
    return
  }

  selectedFile.value = file
  
  // Criar preview
  const reader = new FileReader()
  reader.onload = (e) => {
    previewUrl.value = e.target?.result as string
  }
  reader.readAsDataURL(file)
}

const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]
  
  if (file) {
    setFile(file)
  }
}

const handleDrop = (event: DragEvent) => {
  event.preventDefault()
  isDragging.value = false
  
  const files = event.dataTransfer?.files
  if (files && files.length > 0) {
    setFile(files[0])
  }
}

const clearFile = () => {
  selectedFile.value = null
  previewUrl.value = null
  error.value = null
  uploadProgress.value = 0
  
  if (fileInput.value) {
    fileInput.value.value = ''
  }
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'
  
  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))
  
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}

const handleUpload = async () => {
  if (!selectedFile.value) return
  
  isUploading.value = true
  error.value = null
  uploadProgress.value = 0
  
  try {
    // Simular upload com progresso
    // Na implementação real, isso seria feito através do store
    const formData = new FormData()
    formData.append('file', selectedFile.value)
    
    // Simular progresso
    const progressInterval = setInterval(() => {
      uploadProgress.value += 10
      if (uploadProgress.value >= 90) {
        clearInterval(progressInterval)
      }
    }, 100)
    
    // Aqui seria feita a chamada real para a API
    // const imageUrl = await uploadService.uploadImage(formData, onProgress)
    
    // Simular resposta após 2 segundos
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    clearInterval(progressInterval)
    uploadProgress.value = 100
    
    // Simular URL da imagem
    const imageUrl = URL.createObjectURL(selectedFile.value)
    
    emit('uploaded', imageUrl)
    
  } catch (err: any) {
    console.error('Erro no upload:', err)
    error.value = err.message || 'Erro inesperado durante o upload'
  } finally {
    isUploading.value = false
  }
}

// Event listeners para drag and drop
const handleDragEnter = () => {
  isDragging.value = true
}

const handleDragLeave = () => {
  isDragging.value = false
}

// Adicionar event listeners
if (typeof window !== 'undefined') {
  window.addEventListener('dragenter', handleDragEnter)
  window.addEventListener('dragleave', handleDragLeave)
  window.addEventListener('drop', (e) => e.preventDefault())
  window.addEventListener('dragover', (e) => e.preventDefault())
}
</script>
