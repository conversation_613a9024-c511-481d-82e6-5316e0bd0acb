# Exemplo de arquivo de configuração de ambiente
# Copie este arquivo para .env e ajuste os valores conforme necessário

# =============================================================================
# CONFIGURAÇÕES DO BANCO DE DADOS
# =============================================================================
DATABASE_URL=postgresql://cardapio_user:cardapio_password@localhost:5432/cardapio
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=cardapio
DATABASE_USER=cardapio_user
DATABASE_PASSWORD=cardapio_password

# =============================================================================
# CONFIGURAÇÕES DO REDIS
# =============================================================================
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=

# =============================================================================
# CONFIGURAÇÕES DO MINIO (ARMAZENAMENTO DE ARQUIVOS)
# =============================================================================
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=cardapio_minio
MINIO_SECRET_KEY=cardapio_minio_password
MINIO_BUCKET_NAME=cardapio-images
MINIO_SECURE=false

# =============================================================================
# CONFIGURAÇÕES DE AUTENTICAÇÃO JWT
# =============================================================================
JWT_SECRET_KEY=your-super-secret-jwt-key-change-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30
JWT_REFRESH_EXPIRE_DAYS=7

# =============================================================================
# CONFIGURAÇÕES DO MERCADO PAGO
# =============================================================================
MERCADO_PAGO_ACCESS_TOKEN=your-mercado-pago-access-token
MERCADO_PAGO_PUBLIC_KEY=your-mercado-pago-public-key
MERCADO_PAGO_WEBHOOK_SECRET=your-webhook-secret

# =============================================================================
# CONFIGURAÇÕES DO STRIPE (ALTERNATIVA AO MERCADO PAGO)
# =============================================================================
STRIPE_SECRET_KEY=sk_test_your-stripe-secret-key
STRIPE_PUBLIC_KEY=pk_test_your-stripe-public-key
STRIPE_WEBHOOK_SECRET=whsec_your-webhook-secret

# =============================================================================
# CONFIGURAÇÕES DE EMAIL
# =============================================================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
SMTP_FROM_EMAIL=<EMAIL>
SMTP_FROM_NAME=Cardápio Digital

# =============================================================================
# CONFIGURAÇÕES DO FIREBASE (PUSH NOTIFICATIONS)
# =============================================================================
FIREBASE_PROJECT_ID=your-firebase-project-id
FIREBASE_PRIVATE_KEY_ID=your-private-key-id
FIREBASE_PRIVATE_KEY="-----BEGIN PRIVATE KEY-----\nyour-private-key\n-----END PRIVATE KEY-----\n"
FIREBASE_CLIENT_EMAIL=<EMAIL>
FIREBASE_CLIENT_ID=your-client-id
FIREBASE_AUTH_URI=https://accounts.google.com/o/oauth2/auth
FIREBASE_TOKEN_URI=https://oauth2.googleapis.com/token

# =============================================================================
# CONFIGURAÇÕES GERAIS DA APLICAÇÃO
# =============================================================================
ENVIRONMENT=development
DEBUG=true
API_HOST=0.0.0.0
API_PORT=8000
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:8000

# =============================================================================
# CONFIGURAÇÕES DE SEGURANÇA
# =============================================================================
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000
RATE_LIMIT_PER_MINUTE=60
MAX_FILE_SIZE_MB=10

# =============================================================================
# CONFIGURAÇÕES DE LOG
# =============================================================================
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE=logs/app.log

# =============================================================================
# CONFIGURAÇÕES ESPECÍFICAS DO FRONTEND
# =============================================================================
VITE_API_URL=http://localhost:8000
VITE_ENVIRONMENT=development
VITE_APP_NAME=Cardápio Digital
VITE_MERCADO_PAGO_PUBLIC_KEY=your-mercado-pago-public-key
VITE_STRIPE_PUBLIC_KEY=pk_test_your-stripe-public-key
