"""
Modelos relacionados a promoções e cupons
"""
from sqlalchemy import Column, String, Text, Boolean, Numeric, Integer, ForeignKey, DateTime, Enum
from sqlalchemy.dialects.postgresql import UUID, ARRAY
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from datetime import datetime
from app.models.base import BaseModel, TenantMixin


class PromotionType(PyEnum):
    """Tipo de promoção"""
    PERCENTAGE = "percentage"     # Desconto percentual
    FIXED_AMOUNT = "fixed_amount" # Valor fixo
    BUY_X_GET_Y = "buy_x_get_y"  # Compre X leve Y
    FREE_DELIVERY = "free_delivery" # Frete grátis


class CouponStatus(PyEnum):
    """Status do cupom"""
    ACTIVE = "active"
    INACTIVE = "inactive"
    EXPIRED = "expired"
    USED_UP = "used_up"


class Promotion(BaseModel, TenantMixin):
    """Modelo de promoção"""
    
    __tablename__ = "promotions"
    
    # Informações básicas
    name = Column(String(255), nullable=False)
    description = Column(Text)
    
    # Tipo e configuração
    promotion_type = Column(Enum(PromotionType), nullable=False)
    discount_percentage = Column(Numeric(5, 2))  # Para tipo percentage
    discount_amount = Column(Numeric(10, 2))     # Para tipo fixed_amount
    
    # Configurações buy_x_get_y
    buy_quantity = Column(Integer)
    get_quantity = Column(Integer)
    
    # Valor mínimo do pedido
    minimum_order_amount = Column(Numeric(10, 2), default=0.00)
    
    # Período de validade
    start_date = Column(DateTime, nullable=False)
    end_date = Column(DateTime, nullable=False)
    
    # Limitações de uso
    max_uses = Column(Integer)  # Null = ilimitado
    max_uses_per_customer = Column(Integer, default=1)
    current_uses = Column(Integer, default=0)
    
    # Configurações de aplicação
    applies_to_all_items = Column(Boolean, default=True)
    applicable_categories = Column(ARRAY(UUID(as_uuid=True)))
    applicable_items = Column(ARRAY(UUID(as_uuid=True)))
    
    # Configurações de exibição
    is_featured = Column(Boolean, default=False)
    banner_image_url = Column(Text)
    
    # Relacionamentos
    establishment_id = Column(
        UUID(as_uuid=True),
        ForeignKey("establishments.id"),
        nullable=False,
        index=True
    )
    
    establishment = relationship("Establishment")
    coupons = relationship("Coupon", back_populates="promotion")
    
    def __repr__(self):
        return f"<Promotion(name='{self.name}', type='{self.promotion_type.value}')>"
    
    @property
    def is_valid(self) -> bool:
        """Verificar se a promoção está válida"""
        now = datetime.utcnow()
        return (
            self.is_active and
            self.start_date <= now <= self.end_date and
            (self.max_uses is None or self.current_uses < self.max_uses)
        )


class Coupon(BaseModel, TenantMixin):
    """Modelo de cupom de desconto"""
    
    __tablename__ = "coupons"
    
    # Código do cupom
    code = Column(String(50), nullable=False, unique=True, index=True)
    
    # Status
    status = Column(Enum(CouponStatus), nullable=False, default=CouponStatus.ACTIVE)
    
    # Limitações de uso
    max_uses = Column(Integer, default=1)
    current_uses = Column(Integer, default=0)
    
    # Período de validade
    valid_from = Column(DateTime, nullable=False, default=datetime.utcnow)
    valid_until = Column(DateTime, nullable=False)
    
    # Relacionamentos
    promotion_id = Column(
        UUID(as_uuid=True),
        ForeignKey("promotions.id"),
        nullable=True,  # Pode ser null para cupons independentes
        index=True
    )
    
    establishment_id = Column(
        UUID(as_uuid=True),
        ForeignKey("establishments.id"),
        nullable=False,
        index=True
    )
    
    promotion = relationship("Promotion", back_populates="coupons")
    establishment = relationship("Establishment")
    uses = relationship("CouponUse", back_populates="coupon")
    
    def __repr__(self):
        return f"<Coupon(code='{self.code}', status='{self.status.value}')>"
    
    @property
    def is_valid(self) -> bool:
        """Verificar se o cupom está válido"""
        now = datetime.utcnow()
        return (
            self.status == CouponStatus.ACTIVE and
            self.valid_from <= now <= self.valid_until and
            self.current_uses < self.max_uses
        )


class CouponUse(BaseModel, TenantMixin):
    """Modelo de uso de cupom"""
    
    __tablename__ = "coupon_uses"
    
    # Valor do desconto aplicado
    discount_amount = Column(Numeric(10, 2), nullable=False)
    
    # Relacionamentos
    coupon_id = Column(
        UUID(as_uuid=True),
        ForeignKey("coupons.id"),
        nullable=False,
        index=True
    )
    
    order_id = Column(
        UUID(as_uuid=True),
        ForeignKey("orders.id"),
        nullable=False,
        index=True
    )
    
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False,
        index=True
    )
    
    establishment_id = Column(
        UUID(as_uuid=True),
        ForeignKey("establishments.id"),
        nullable=False,
        index=True
    )
    
    coupon = relationship("Coupon", back_populates="uses")
    order = relationship("Order")
    user = relationship("User")
    establishment = relationship("Establishment")
    
    def __repr__(self):
        return f"<CouponUse(coupon_id='{self.coupon_id}', discount={self.discount_amount})>"
