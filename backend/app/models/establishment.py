"""
Modelo de estabelecimentos (tenants)
"""
from sqlalchemy import Column, String, Text, Boolean, Numeric, Time
from sqlalchemy.orm import relationship
from app.models.base import BaseModel


class Establishment(BaseModel):
    """Modelo de estabelecimento (tenant principal)"""
    
    __tablename__ = "establishments"
    
    # Informações básicas
    name = Column(String(255), nullable=False)
    slug = Column(String(100), unique=True, nullable=False, index=True)
    email = Column(String(255), unique=True, nullable=False)
    phone = Column(String(20))
    
    # Endereço
    address = Column(Text)
    city = Column(String(100))
    state = Column(String(50))
    zip_code = Column(String(20))
    country = Column(String(50), default="Brasil")
    latitude = Column(Numeric(10, 8))
    longitude = Column(Numeric(11, 8))
    
    # Personalização visual
    logo_url = Column(Text)
    banner_url = Column(Text)
    primary_color = Column(String(7), default="#3B82F6")  # Hex color
    secondary_color = Column(String(7), default="#1F2937")  # Hex color
    accent_color = Column(String(7), default="#10B981")  # Hex color
    
    # Configurações de funcionamento
    accepts_orders = Column(Boolean, default=True)
    delivery_fee = Column(Numeric(10, 2), default=0.00)
    minimum_order = Column(Numeric(10, 2), default=0.00)
    max_delivery_distance = Column(Numeric(5, 2), default=10.0)  # km
    
    # Horários de funcionamento
    monday_open = Column(Time)
    monday_close = Column(Time)
    tuesday_open = Column(Time)
    tuesday_close = Column(Time)
    wednesday_open = Column(Time)
    wednesday_close = Column(Time)
    thursday_open = Column(Time)
    thursday_close = Column(Time)
    friday_open = Column(Time)
    friday_close = Column(Time)
    saturday_open = Column(Time)
    saturday_close = Column(Time)
    sunday_open = Column(Time)
    sunday_close = Column(Time)
    
    # Configurações de pagamento
    accepts_pix = Column(Boolean, default=True)
    accepts_card = Column(Boolean, default=True)
    accepts_cash = Column(Boolean, default=True)
    mercado_pago_token = Column(Text)  # Criptografado
    stripe_account_id = Column(String(255))
    
    # Configurações de notificação
    notification_email = Column(String(255))
    notification_phone = Column(String(20))
    webhook_url = Column(Text)
    
    # Configurações de delivery
    has_delivery = Column(Boolean, default=True)
    has_pickup = Column(Boolean, default=True)
    has_table_service = Column(Boolean, default=False)
    
    # Relacionamentos
    users = relationship("User", back_populates="establishment")
    categories = relationship("Category", back_populates="establishment")
    menu_items = relationship("MenuItem", back_populates="establishment")
    orders = relationship("Order", back_populates="establishment")
    promotions = relationship("Promotion", back_populates="establishment")
    coupons = relationship("Coupon", back_populates="establishment")
    
    def __repr__(self):
        return f"<Establishment(name='{self.name}', slug='{self.slug}')>"
    
    @property
    def public_url(self):
        """URL pública do estabelecimento"""
        return f"/{self.slug}"
    
    def is_open_now(self) -> bool:
        """Verificar se o estabelecimento está aberto agora"""
        from datetime import datetime, time
        
        now = datetime.now()
        weekday = now.weekday()  # 0 = Monday, 6 = Sunday
        current_time = now.time()
        
        # Mapear dia da semana para campos do modelo
        day_mapping = {
            0: (self.monday_open, self.monday_close),
            1: (self.tuesday_open, self.tuesday_close),
            2: (self.wednesday_open, self.wednesday_close),
            3: (self.thursday_open, self.thursday_close),
            4: (self.friday_open, self.friday_close),
            5: (self.saturday_open, self.saturday_close),
            6: (self.sunday_open, self.sunday_close),
        }
        
        open_time, close_time = day_mapping.get(weekday, (None, None))
        
        if not open_time or not close_time:
            return False
        
        return open_time <= current_time <= close_time
