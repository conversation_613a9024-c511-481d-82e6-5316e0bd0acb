# Mobile - Flutter App

## Visão Geral
Aplicativo mobile para clientes fazerem pedidos, acompanharem status e interagirem com estabelecimentos.

## Tecnologias
- **Flutter**: Framework multiplataforma
- **Dart**: Linguagem de programação
- **Provider/Riverpod**: Gerenciamento de estado
- **HTTP**: Comunicação com API
- **Firebase**: Push notifications
- **Google Maps**: Localização
- **Mercado Pago/Stripe**: Pagamentos

## Estrutura
```
mobile/
├── lib/
│   ├── main.dart          # Ponto de entrada
│   ├── app.dart           # Configuração do app
│   ├── models/            # Modelos de dados
│   │   ├── establishment.dart
│   │   ├── user.dart
│   │   ├── menu_item.dart
│   │   ├── order.dart
│   │   └── cart.dart
│   ├── screens/           # Telas do aplicativo
│   │   ├── auth/          # Login e registro
│   │   │   ├── login_screen.dart
│   │   │   ├── register_screen.dart
│   │   │   └── forgot_password_screen.dart
│   │   ├── home/          # Tela inicial
│   │   │   ├── home_screen.dart
│   │   │   └── search_screen.dart
│   │   ├── establishment/ # Estabelecimentos
│   │   │   ├── establishment_list_screen.dart
│   │   │   ├── establishment_detail_screen.dart
│   │   │   └── menu_screen.dart
│   │   ├── cart/          # Carrinho
│   │   │   ├── cart_screen.dart
│   │   │   └── checkout_screen.dart
│   │   ├── orders/        # Pedidos
│   │   │   ├── orders_screen.dart
│   │   │   ├── order_detail_screen.dart
│   │   │   └── order_tracking_screen.dart
│   │   ├── profile/       # Perfil do usuário
│   │   │   ├── profile_screen.dart
│   │   │   ├── favorites_screen.dart
│   │   │   └── settings_screen.dart
│   │   └── payment/       # Pagamentos
│   │       ├── payment_screen.dart
│   │       └── payment_success_screen.dart
│   ├── widgets/           # Widgets reutilizáveis
│   │   ├── common/        # Widgets comuns
│   │   │   ├── custom_button.dart
│   │   │   ├── custom_text_field.dart
│   │   │   ├── loading_widget.dart
│   │   │   └── error_widget.dart
│   │   ├── cards/         # Cards específicos
│   │   │   ├── establishment_card.dart
│   │   │   ├── menu_item_card.dart
│   │   │   └── order_card.dart
│   │   └── forms/         # Formulários
│   │       ├── login_form.dart
│   │       └── address_form.dart
│   ├── services/          # Serviços
│   │   ├── api_service.dart      # Cliente HTTP
│   │   ├── auth_service.dart     # Autenticação
│   │   ├── location_service.dart # Geolocalização
│   │   ├── payment_service.dart  # Pagamentos
│   │   ├── notification_service.dart # Push notifications
│   │   └── storage_service.dart  # Armazenamento local
│   ├── providers/         # Gerenciamento de estado
│   │   ├── auth_provider.dart
│   │   ├── cart_provider.dart
│   │   ├── orders_provider.dart
│   │   └── establishments_provider.dart
│   ├── utils/             # Utilitários
│   │   ├── constants.dart # Constantes
│   │   ├── formatters.dart # Formatadores
│   │   ├── validators.dart # Validadores
│   │   └── theme.dart     # Tema do app
│   └── config/            # Configurações
│       ├── app_config.dart
│       ├── api_config.dart
│       └── firebase_config.dart
├── assets/                # Recursos
│   ├── images/           # Imagens
│   ├── icons/            # Ícones
│   └── fonts/            # Fontes
├── android/              # Configurações Android
├── ios/                  # Configurações iOS
├── test/                 # Testes
├── pubspec.yaml          # Dependências
└── README.md            # Esta documentação
```

## Configuração

### Pré-requisitos
```bash
# Instalar Flutter SDK
# https://flutter.dev/docs/get-started/install

# Verificar instalação
flutter doctor
```

### Instalação
```bash
cd mobile
flutter pub get
```

### Executar
```bash
# Android
flutter run

# iOS
flutter run -d ios

# Web (para testes)
flutter run -d web
```

## Funcionalidades

### Autenticação
- Login com email/senha
- Registro de novos usuários
- Recuperação de senha
- Login social (Google, Facebook)

### Busca de Estabelecimentos
- Localização por GPS
- Busca por nome ou categoria
- Filtros (distância, avaliação, tipo)
- Favoritos

### Cardápio e Pedidos
- Visualização de cardápios
- Carrinho de compras
- Personalização de produtos
- Histórico de pedidos

### Pagamentos
- Integração Mercado Pago
- Integração Stripe
- PIX (Brasil)
- Cartão de crédito/débito

### Acompanhamento
- Status em tempo real
- Push notifications
- Estimativa de entrega
- Avaliação de pedidos

## Principais Widgets

### EstablishmentCard
```dart
EstablishmentCard(
  establishment: establishment,
  onTap: () => Navigator.push(...),
  showDistance: true,
)
```

### MenuItemCard
```dart
MenuItemCard(
  menuItem: item,
  onAddToCart: (item) => cartProvider.addItem(item),
  showPrice: true,
)
```

### CustomButton
```dart
CustomButton(
  text: 'Fazer Pedido',
  onPressed: () => _makeOrder(),
  isLoading: isLoading,
)
```

## Gerenciamento de Estado

### Providers
```dart
// Cart Provider
class CartProvider extends ChangeNotifier {
  List<CartItem> _items = [];
  
  void addItem(MenuItem item) {
    _items.add(CartItem.fromMenuItem(item));
    notifyListeners();
  }
  
  double get total => _items.fold(0, (sum, item) => sum + item.total);
}

// Usage
Consumer<CartProvider>(
  builder: (context, cart, child) {
    return Text('Total: ${cart.total}');
  },
)
```

## Serviços

### API Service
```dart
class ApiService {
  static const String baseUrl = 'http://localhost:8000/api/v1';
  
  Future<List<Establishment>> getEstablishments() async {
    final response = await http.get(Uri.parse('$baseUrl/establishments'));
    // Handle response
  }
}
```

### Location Service
```dart
class LocationService {
  Future<Position> getCurrentPosition() async {
    return await Geolocator.getCurrentPosition();
  }
  
  Future<double> getDistanceBetween(
    double startLat, double startLng,
    double endLat, double endLng
  ) async {
    return Geolocator.distanceBetween(startLat, startLng, endLat, endLng);
  }
}
```

## Configuração Firebase

### Android
```xml
<!-- android/app/src/main/AndroidManifest.xml -->
<application>
  <meta-data
    android:name="com.google.firebase.messaging.default_notification_channel_id"
    android:value="high_importance_channel" />
</application>
```

### iOS
```xml
<!-- ios/Runner/Info.plist -->
<key>FirebaseAppDelegateProxyEnabled</key>
<false/>
```

## Temas e Estilos

### Theme Configuration
```dart
ThemeData(
  primarySwatch: Colors.blue,
  fontFamily: 'Roboto',
  elevatedButtonTheme: ElevatedButtonThemeData(
    style: ElevatedButton.styleFrom(
      backgroundColor: Colors.blue,
      foregroundColor: Colors.white,
    ),
  ),
)
```

## Testes
```bash
# Testes unitários
flutter test

# Testes de integração
flutter test integration_test/

# Testes de widget
flutter test test/widget_test.dart
```

## Build para Produção

### Android
```bash
flutter build apk --release
flutter build appbundle --release
```

### iOS
```bash
flutter build ios --release
```

## Próximos Passos
1. Configurar projeto Flutter
2. Implementar autenticação
3. Criar telas principais
4. Integrar com API
5. Configurar pagamentos
6. Implementar push notifications
