<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm">
      <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-6">
          <div class="text-center">
            <h1 class="text-3xl font-bold text-gray-900">Acompanhe seu Pedido</h1>
            <p v-if="order" class="text-lg text-gray-600 mt-2">
              Pedido #{{ order.order_number }}
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Conteúdo principal -->
    <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Loading state -->
      <div v-if="isLoading" class="flex justify-center py-12">
        <div class="spinner w-8 h-8"></div>
      </div>

      <!-- Error state -->
      <div v-else-if="error" class="text-center py-12">
        <ExclamationTriangleIcon class="mx-auto h-16 w-16 text-red-400" />
        <h2 class="mt-4 text-xl font-medium text-gray-900">Pedido não encontrado</h2>
        <p class="mt-2 text-gray-600">{{ error }}</p>
        <button
          @click="goToMenu"
          class="mt-6 btn btn-primary"
        >
          Ver Cardápio
        </button>
      </div>

      <!-- Tracking content -->
      <div v-else-if="order" class="space-y-8">
        <!-- Status atual -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="text-center">
            <div class="flex justify-center mb-4">
              <div
                :class="[
                  'w-16 h-16 rounded-full flex items-center justify-center',
                  getStatusColor(orderStatus)
                ]"
              >
                <component
                  :is="getStatusIcon(orderStatus)"
                  class="w-8 h-8 text-white"
                />
              </div>
            </div>
            
            <h2 class="text-2xl font-bold text-gray-900 mb-2">
              {{ statusMessage }}
            </h2>
            
            <p class="text-gray-600 mb-4">
              {{ getStatusDescription(orderStatus) }}
            </p>

            <!-- Tempo estimado -->
            <div v-if="estimatedTime && !isCompleted" class="flex items-center justify-center text-sm text-gray-600">
              <ClockIcon class="h-4 w-4 mr-1" />
              <span>Tempo estimado: {{ estimatedTime }} minutos</span>
            </div>

            <!-- Conexão WebSocket -->
            <div class="flex items-center justify-center mt-2">
              <div
                :class="[
                  'w-2 h-2 rounded-full mr-2',
                  isConnected ? 'bg-green-500' : 'bg-red-500'
                ]"
              />
              <span class="text-xs text-gray-500">
                {{ isConnected ? 'Atualizações em tempo real' : 'Reconectando...' }}
              </span>
            </div>
          </div>
        </div>

        <!-- Barra de progresso -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <div class="mb-4">
            <div class="flex justify-between text-sm text-gray-600 mb-2">
              <span>Progresso do pedido</span>
              <span>{{ progressPercentage }}%</span>
            </div>
            <div class="w-full bg-gray-200 rounded-full h-2">
              <div
                :class="[
                  'h-2 rounded-full transition-all duration-500',
                  isCompleted ? 'bg-green-500' : 'bg-primary-500'
                ]"
                :style="{ width: `${progressPercentage}%` }"
              />
            </div>
          </div>
        </div>

        <!-- Timeline do pedido -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-6">Timeline do Pedido</h3>
          
          <div class="space-y-6">
            <div
              v-for="(step, index) in timeline"
              :key="index"
              class="flex items-start"
            >
              <!-- Ícone do step -->
              <div class="flex-shrink-0 mr-4">
                <div
                  :class="[
                    'w-10 h-10 rounded-full flex items-center justify-center border-2',
                    step.is_completed
                      ? 'bg-green-500 border-green-500 text-white'
                      : step.is_current
                      ? 'bg-primary-500 border-primary-500 text-white'
                      : 'bg-gray-100 border-gray-300 text-gray-400'
                  ]"
                >
                  <component
                    :is="getTimelineIcon(step.status)"
                    class="w-5 h-5"
                  />
                </div>
                
                <!-- Linha conectora -->
                <div
                  v-if="index < timeline.length - 1"
                  :class="[
                    'w-0.5 h-8 mx-auto mt-2',
                    step.is_completed ? 'bg-green-500' : 'bg-gray-300'
                  ]"
                />
              </div>

              <!-- Conteúdo do step -->
              <div class="flex-1 min-w-0">
                <div class="flex items-center justify-between">
                  <h4
                    :class="[
                      'text-sm font-medium',
                      step.is_completed || step.is_current
                        ? 'text-gray-900'
                        : 'text-gray-500'
                    ]"
                  >
                    {{ step.description }}
                  </h4>
                  
                  <span
                    v-if="step.timestamp"
                    class="text-xs text-gray-500"
                  >
                    {{ formatTimelineDate(step.timestamp) }}
                  </span>
                </div>
                
                <p
                  v-if="step.is_current && !isCompleted"
                  class="text-xs text-primary-600 mt-1"
                >
                  Em andamento...
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- Informações do pedido -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Detalhes do Pedido</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Informações básicas -->
            <div>
              <h4 class="text-sm font-medium text-gray-700 mb-3">Informações</h4>
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span class="text-gray-600">Número:</span>
                  <span class="font-medium">#{{ order.order_number }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Tipo:</span>
                  <span class="font-medium">{{ getOrderTypeLabel(order.order_type) }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Data:</span>
                  <span class="font-medium">{{ formatDate(order.created_at) }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-600">Total:</span>
                  <span class="font-medium text-lg">{{ formatCurrency(order.total_amount) }}</span>
                </div>
              </div>
            </div>

            <!-- Endereço de entrega -->
            <div v-if="order.order_type === 'delivery' && order.delivery_address">
              <h4 class="text-sm font-medium text-gray-700 mb-3">Endereço de Entrega</h4>
              <div class="text-sm text-gray-600">
                <p>{{ order.delivery_address }}</p>
                <p>{{ order.delivery_city }}, {{ order.delivery_state }}</p>
                <p>{{ order.delivery_zip_code }}</p>
              </div>
            </div>

            <!-- Mesa -->
            <div v-if="order.order_type === 'table_service' && order.table_number">
              <h4 class="text-sm font-medium text-gray-700 mb-3">Mesa</h4>
              <div class="text-sm text-gray-600">
                <p>Mesa {{ order.table_number }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Itens do pedido -->
        <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
          <h3 class="text-lg font-medium text-gray-900 mb-4">Itens do Pedido</h3>
          
          <div class="space-y-3">
            <div
              v-for="item in order.items"
              :key="item.id"
              class="flex items-center justify-between py-2 border-b border-gray-100 last:border-b-0"
            >
              <div class="flex-1">
                <h4 class="text-sm font-medium text-gray-900">
                  {{ item.quantity }}x {{ item.item_name }}
                </h4>
                <p v-if="item.notes" class="text-xs text-gray-500 mt-1">
                  {{ item.notes }}
                </p>
              </div>
              <span class="text-sm font-medium text-gray-900">
                {{ formatCurrency(item.total_price) }}
              </span>
            </div>
          </div>
        </div>

        <!-- Ações -->
        <div class="flex flex-col sm:flex-row gap-4">
          <button
            v-if="order.can_be_cancelled"
            @click="showCancelModal = true"
            class="btn btn-outline btn-red flex-1"
          >
            Cancelar Pedido
          </button>
          
          <button
            @click="refreshTracking"
            class="btn btn-outline flex-1"
            :disabled="isLoading"
          >
            <ArrowPathIcon class="h-4 w-4 mr-2" />
            Atualizar
          </button>
          
          <button
            @click="goToMenu"
            class="btn btn-primary flex-1"
          >
            Fazer Novo Pedido
          </button>
        </div>
      </div>
    </div>

    <!-- Modal de cancelamento -->
    <ConfirmationModal
      v-if="showCancelModal"
      title="Cancelar Pedido"
      message="Tem certeza que deseja cancelar este pedido? Esta ação não pode ser desfeita."
      confirm-text="Sim, Cancelar"
      cancel-text="Não, Manter"
      @confirm="cancelOrder"
      @cancel="showCancelModal = false"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  ExclamationTriangleIcon,
  ClockIcon,
  ArrowPathIcon,
  CheckCircleIcon,
  FireIcon,
  TruckIcon,
  XCircleIcon
} from '@heroicons/vue/24/outline'
import { useToast } from 'vue-toastification'

import { useOrderTrackingStore } from '@/stores/orderTracking'

// Componentes
const ConfirmationModal = defineAsyncComponent(() => import('@/components/modals/ConfirmationModal.vue'))

const route = useRoute()
const router = useRouter()
const toast = useToast()
const trackingStore = useOrderTrackingStore()

// Estado local
const showCancelModal = ref(false)

// Computed
const order = computed(() => trackingStore.currentOrder)
const orderStatus = computed(() => trackingStore.orderStatus)
const estimatedTime = computed(() => trackingStore.estimatedTime)
const timeline = computed(() => trackingStore.timeline)
const progressPercentage = computed(() => trackingStore.progressPercentage)
const isCompleted = computed(() => trackingStore.isCompleted)
const statusMessage = computed(() => trackingStore.statusMessage)
const isLoading = computed(() => trackingStore.isLoading)
const error = computed(() => trackingStore.error)
const isConnected = computed(() => trackingStore.isConnected)

// Methods
const loadTracking = async () => {
  const orderId = route.params.orderId as string
  
  try {
    await trackingStore.startTracking(orderId)
  } catch (error) {
    console.error('Erro ao carregar tracking:', error)
  }
}

const refreshTracking = async () => {
  const orderId = route.params.orderId as string
  
  try {
    await trackingStore.fetchOrderTracking(orderId)
    toast.success('Informações atualizadas')
  } catch (error) {
    toast.error('Erro ao atualizar informações')
  }
}

const cancelOrder = async () => {
  // Implementar cancelamento
  showCancelModal.value = false
  toast.success('Pedido cancelado com sucesso')
}

const goToMenu = () => {
  router.push('/')
}

const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value)
}

const formatDate = (dateString: string): string => {
  return new Intl.DateTimeFormat('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(dateString))
}

const formatTimelineDate = (dateString: string): string => {
  return trackingStore.formatTimelineDate(dateString)
}

const getStatusIcon = (status: string) => {
  const icons: Record<string, any> = {
    pending: ClockIcon,
    confirmed: CheckCircleIcon,
    preparing: FireIcon,
    ready: CheckCircleIcon,
    out_for_delivery: TruckIcon,
    delivered: CheckCircleIcon,
    cancelled: XCircleIcon
  }
  
  return icons[status] || ClockIcon
}

const getTimelineIcon = (status: string) => {
  return trackingStore.getTimelineIcon(status)
}

const getStatusColor = (status: string): string => {
  const colors: Record<string, string> = {
    pending: 'bg-yellow-500',
    confirmed: 'bg-blue-500',
    preparing: 'bg-orange-500',
    ready: 'bg-green-500',
    out_for_delivery: 'bg-purple-500',
    delivered: 'bg-green-600',
    cancelled: 'bg-red-500'
  }
  
  return colors[status] || 'bg-gray-500'
}

const getStatusDescription = (status: string): string => {
  const descriptions: Record<string, string> = {
    pending: 'Aguardando confirmação do restaurante',
    confirmed: 'Seu pedido foi confirmado e está sendo preparado',
    preparing: 'Nossa equipe está preparando seu pedido com carinho',
    ready: 'Seu pedido está pronto!',
    out_for_delivery: 'Seu pedido está a caminho',
    delivered: 'Pedido entregue com sucesso!',
    cancelled: 'Pedido foi cancelado'
  }
  
  return descriptions[status] || ''
}

const getOrderTypeLabel = (type: string): string => {
  const labels: Record<string, string> = {
    delivery: 'Delivery',
    pickup: 'Retirada',
    table_service: 'Mesa'
  }
  
  return labels[type] || type
}

// Lifecycle
onMounted(() => {
  loadTracking()
})

onUnmounted(() => {
  trackingStore.stopTracking()
})

// Lazy loading de componentes
import { defineAsyncComponent } from 'vue'
</script>

<style scoped>
.spinner {
  border: 2px solid #f3f4f6;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
