# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.eslintcache

# Vue.js
dist/
.nuxt/
.output/
.cache/

# Flutter
.dart_tool/
.flutter-plugins
.flutter-plugins-dependencies
.packages
.pub-cache/
.pub/
build/
flutter_*.png
flutter_*.ipa
flutter_*.apk

# Database
*.db
*.sqlite
*.sqlite3

# Logs
logs/
*.log

# Docker
.dockerignore

# Environment variables
.env.local
.env.development
.env.test
.env.production

# Temporary files
tmp/
temp/
*.tmp
*.temp

# MinIO data
minio-data/

# PostgreSQL data
postgres-data/

# Redis data
redis-data/
