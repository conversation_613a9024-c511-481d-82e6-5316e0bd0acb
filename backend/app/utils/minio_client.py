"""
Cliente MinIO para armazenamento de arquivos
"""
from minio import Minio
from minio.error import S3Error
from typing import Optional, BinaryIO
import uuid
import os
from app.core.config import settings

# Cliente MinIO global
minio_client = Minio(
    settings.MINIO_ENDPOINT,
    access_key=settings.MINIO_ACCESS_KEY,
    secret_key=settings.MINIO_SECRET_KEY,
    secure=settings.MINIO_SECURE
)


class MinIOStorage:
    """Classe para operações de armazenamento no MinIO"""

    def __init__(self, client: Minio = minio_client):
        self.client = client
        self.bucket_name = settings.MINIO_BUCKET_NAME
        self._bucket_checked = False
    
    def _ensure_bucket_exists(self):
        """Garantir que o bucket existe"""
        if self._bucket_checked:
            return

        try:
            if not self.client.bucket_exists(self.bucket_name):
                self.client.make_bucket(self.bucket_name)
            self._bucket_checked = True
        except S3Error as e:
            print(f"Erro ao criar bucket: {e}")
        except Exception as e:
            print(f"Erro de conexão com MinIO: {e}")
            # Em ambiente de teste, não falhar
    
    async def upload_file(
        self,
        file_data: BinaryIO,
        file_name: Optional[str] = None,
        content_type: str = "application/octet-stream",
        folder: str = "uploads"
    ) -> Optional[str]:
        """
        Upload de arquivo para o MinIO
        
        Args:
            file_data: Dados do arquivo
            file_name: Nome do arquivo (será gerado se None)
            content_type: Tipo MIME do arquivo
            folder: Pasta de destino
            
        Returns:
            URL do arquivo ou None se erro
        """
        try:
            # Garantir que bucket existe
            self._ensure_bucket_exists()

            if not file_name:
                file_name = f"{uuid.uuid4()}.bin"

            object_name = f"{folder}/{file_name}"

            # Obter tamanho do arquivo
            file_data.seek(0, os.SEEK_END)
            file_size = file_data.tell()
            file_data.seek(0)

            # Upload do arquivo
            self.client.put_object(
                bucket_name=self.bucket_name,
                object_name=object_name,
                data=file_data,
                length=file_size,
                content_type=content_type
            )

            # Retornar URL do arquivo
            return f"http://{settings.MINIO_ENDPOINT}/{self.bucket_name}/{object_name}"

        except S3Error as e:
            print(f"Erro no upload: {e}")
            return None
        except Exception as e:
            print(f"Erro de conexão no upload: {e}")
            return None
    
    async def upload_image(
        self,
        image_data: BinaryIO,
        file_name: Optional[str] = None,
        folder: str = "images"
    ) -> Optional[str]:
        """
        Upload de imagem com otimizações específicas
        
        Args:
            image_data: Dados da imagem
            file_name: Nome do arquivo
            folder: Pasta de destino
            
        Returns:
            URL da imagem ou None se erro
        """
        # Detectar tipo de conteúdo baseado na extensão
        content_type = "image/jpeg"  # padrão
        if file_name:
            ext = file_name.lower().split('.')[-1]
            if ext == 'png':
                content_type = "image/png"
            elif ext == 'gif':
                content_type = "image/gif"
            elif ext == 'webp':
                content_type = "image/webp"
        
        return await self.upload_file(
            file_data=image_data,
            file_name=file_name,
            content_type=content_type,
            folder=folder
        )
    
    async def delete_file(self, object_name: str) -> bool:
        """
        Remover arquivo do MinIO
        
        Args:
            object_name: Nome do objeto (caminho completo)
            
        Returns:
            True se removido com sucesso
        """
        try:
            self.client.remove_object(self.bucket_name, object_name)
            return True
        except S3Error:
            return False
    
    async def get_file_url(self, object_name: str) -> Optional[str]:
        """
        Obter URL pública do arquivo
        
        Args:
            object_name: Nome do objeto
            
        Returns:
            URL do arquivo ou None se não encontrado
        """
        try:
            # Verificar se arquivo existe
            self.client.stat_object(self.bucket_name, object_name)
            return f"http://{settings.MINIO_ENDPOINT}/{self.bucket_name}/{object_name}"
        except S3Error:
            return None
    
    async def list_files(self, prefix: str = "") -> list:
        """
        Listar arquivos no bucket
        
        Args:
            prefix: Prefixo para filtrar arquivos
            
        Returns:
            Lista de nomes de arquivos
        """
        try:
            objects = self.client.list_objects(
                self.bucket_name, 
                prefix=prefix, 
                recursive=True
            )
            return [obj.object_name for obj in objects]
        except S3Error:
            return []


# Instância global do storage
storage = MinIOStorage()
