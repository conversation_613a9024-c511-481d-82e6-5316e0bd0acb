# Configurações do Backend - Exemplo
# Copie este arquivo para .env e ajuste os valores

# Aplicação
ENVIRONMENT=development
DEBUG=true
API_HOST=0.0.0.0
API_PORT=8000

# URLs
FRONTEND_URL=http://localhost:3000
BACKEND_URL=http://localhost:8000

# Banco de dados
DATABASE_URL=postgresql://cardapio_user:cardapio_password@localhost:5432/cardapio
DATABASE_HOST=localhost
DATABASE_PORT=5432
DATABASE_NAME=cardapio
DATABASE_USER=cardapio_user
DATABASE_PASSWORD=cardapio_password

# Redis
REDIS_URL=redis://localhost:6379
REDIS_HOST=localhost
REDIS_PORT=6379

# MinIO
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=cardapio_minio
MINIO_SECRET_KEY=cardapio_minio_password
MINIO_BUCKET_NAME=cardapio-images
MINIO_SECURE=false

# JWT
JWT_SECRET_KEY=dev-jwt-secret-key-change-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30
JWT_REFRESH_EXPIRE_DAYS=7

# CORS
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0
CORS_ORIGINS=http://localhost:3000,http://127.0.0.1:3000

# Segurança
RATE_LIMIT_PER_MINUTE=60
MAX_FILE_SIZE_MB=10

# Logging
LOG_LEVEL=INFO
LOG_FORMAT=json
LOG_FILE=logs/app.log
