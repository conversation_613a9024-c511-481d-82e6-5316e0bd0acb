"""
Modelo base para todos os modelos SQLAlchemy
"""
import uuid
from datetime import datetime
from sqlalchemy import Column, String, DateTime, Boolean
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.ext.declarative import declared_attr
from app.core.database import Base


class BaseModel(Base):
    """Modelo base com campos comuns a todas as tabelas"""
    
    __abstract__ = True
    
    id = Column(
        UUID(as_uuid=True),
        primary_key=True,
        default=uuid.uuid4,
        index=True
    )
    
    created_at = Column(
        DateTime,
        default=datetime.utcnow,
        nullable=False
    )
    
    updated_at = Column(
        DateTime,
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        nullable=False
    )
    
    is_active = Column(
        Boolean,
        default=True,
        nullable=False
    )
    
    @declared_attr
    def __tablename__(cls):
        """Gerar nome da tabela automaticamente"""
        return cls.__name__.lower()


class TenantMixin:
    """Mixin para modelos que pertencem a um estabelecimento (tenant)"""
    
    @declared_attr
    def establishment_id(cls):
        return Column(
            UUID(as_uuid=True),
            nullable=False,
            index=True
        )


class TimestampMixin:
    """Mixin para adicionar timestamps a modelos que não herdam de BaseModel"""
    
    created_at = Column(
        DateTime,
        default=datetime.utcnow,
        nullable=False
    )
    
    updated_at = Column(
        DateTime,
        default=datetime.utcnow,
        onupdate=datetime.utcnow,
        nullable=False
    )
