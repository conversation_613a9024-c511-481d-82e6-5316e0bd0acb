<template>
  <div class="min-h-screen bg-white flex flex-col justify-center items-center px-4 sm:px-6 lg:px-8">
    <div class="max-w-md w-full space-y-8 text-center">
      <!-- Ilustração 404 -->
      <div class="mx-auto">
        <div class="relative">
          <!-- Número 404 grande -->
          <div class="text-9xl font-bold text-gray-200 select-none">
            404
          </div>
          
          <!-- Ícone sobreposto -->
          <div class="absolute inset-0 flex items-center justify-center">
            <div class="bg-primary-100 rounded-full p-4">
              <ExclamationTriangleIcon class="h-16 w-16 text-primary-600" />
            </div>
          </div>
        </div>
      </div>

      <!-- Título e descrição -->
      <div class="space-y-4">
        <h1 class="text-3xl font-bold text-gray-900 sm:text-4xl">
          Página não encontrada
        </h1>
        
        <p class="text-lg text-gray-600">
          Ops! A página que você está procurando não existe ou foi movida.
        </p>
        
        <p class="text-sm text-gray-500">
          Verifique se o endereço está correto ou use os links abaixo para navegar.
        </p>
      </div>

      <!-- Ações -->
      <div class="space-y-4">
        <!-- Botão principal -->
        <div>
          <router-link
            to="/"
            class="btn btn-primary btn-lg w-full sm:w-auto"
          >
            <HomeIcon class="h-5 w-5 mr-2" />
            Voltar ao início
          </router-link>
        </div>

        <!-- Links úteis -->
        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <router-link
            to="/auth/login"
            class="text-primary-600 hover:text-primary-500 font-medium transition-colors duration-200"
          >
            Fazer login
          </router-link>
          
          <router-link
            to="/sobre"
            class="text-primary-600 hover:text-primary-500 font-medium transition-colors duration-200"
          >
            Sobre nós
          </router-link>
          
          <a
            href="mailto:<EMAIL>"
            class="text-primary-600 hover:text-primary-500 font-medium transition-colors duration-200"
          >
            Suporte
          </a>
        </div>
      </div>

      <!-- Sugestões -->
      <div class="bg-gray-50 rounded-lg p-6 text-left">
        <h3 class="text-sm font-medium text-gray-900 mb-3">
          Você pode estar procurando por:
        </h3>
        
        <ul class="space-y-2 text-sm text-gray-600">
          <li class="flex items-center">
            <ChevronRightIcon class="h-4 w-4 text-gray-400 mr-2" />
            <router-link to="/" class="hover:text-primary-600 transition-colors duration-200">
              Página inicial
            </router-link>
          </li>
          
          <li class="flex items-center">
            <ChevronRightIcon class="h-4 w-4 text-gray-400 mr-2" />
            <router-link to="/auth/register" class="hover:text-primary-600 transition-colors duration-200">
              Criar conta gratuita
            </router-link>
          </li>
          
          <li class="flex items-center">
            <ChevronRightIcon class="h-4 w-4 text-gray-400 mr-2" />
            <router-link to="/dashboard" class="hover:text-primary-600 transition-colors duration-200">
              Painel administrativo
            </router-link>
          </li>
          
          <li class="flex items-center">
            <ChevronRightIcon class="h-4 w-4 text-gray-400 mr-2" />
            <router-link to="/sobre" class="hover:text-primary-600 transition-colors duration-200">
              Sobre o Cardápio Digital
            </router-link>
          </li>
        </ul>
      </div>

      <!-- Informações de contato -->
      <div class="border-t border-gray-200 pt-6">
        <p class="text-xs text-gray-500 mb-4">
          Precisa de ajuda? Entre em contato conosco:
        </p>
        
        <div class="flex flex-col sm:flex-row gap-4 justify-center text-sm">
          <a
            href="mailto:<EMAIL>"
            class="flex items-center justify-center text-gray-600 hover:text-primary-600 transition-colors duration-200"
          >
            <EnvelopeIcon class="h-4 w-4 mr-2" />
            <EMAIL>
          </a>
          
          <a
            href="tel:+5511999999999"
            class="flex items-center justify-center text-gray-600 hover:text-primary-600 transition-colors duration-200"
          >
            <PhoneIcon class="h-4 w-4 mr-2" />
            (11) 99999-9999
          </a>
        </div>
      </div>
    </div>

    <!-- Decoração de fundo -->
    <div class="fixed inset-0 -z-10 overflow-hidden pointer-events-none">
      <div class="absolute -top-40 -right-32 w-80 h-80 bg-primary-50 rounded-full opacity-50"></div>
      <div class="absolute -bottom-40 -left-32 w-80 h-80 bg-secondary-50 rounded-full opacity-50"></div>
      <div class="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gray-50 rounded-full opacity-30"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  ExclamationTriangleIcon,
  HomeIcon,
  ChevronRightIcon,
  EnvelopeIcon,
  PhoneIcon
} from '@heroicons/vue/24/outline'
</script>
