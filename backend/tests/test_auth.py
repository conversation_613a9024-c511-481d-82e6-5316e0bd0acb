"""
Testes para autenticação
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.main import app
from app.core.database import get_db, Base
from app.models.user import UserRole

# Configurar banco de dados de teste em memória
SQLALCHEMY_DATABASE_URL = "sqlite:///:memory:"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


app.dependency_overrides[get_db] = override_get_db

client = TestClient(app)


@pytest.fixture(autouse=True)
def setup_database():
    """Configurar banco de dados para cada teste"""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def test_register_customer():
    """Testar registro de cliente"""
    response = client.post(
        "/api/v1/auth/register",
        json={
            "email": "<EMAIL>",
            "password": "senha123",
            "full_name": "Cliente Teste",
            "phone": "(11) 99999-9999",
            "role": "customer"
        }
    )
    
    assert response.status_code == 201
    data = response.json()
    assert data["email"] == "<EMAIL>"
    assert data["full_name"] == "Cliente Teste"
    assert data["role"] == "customer"
    assert data["is_active"] is True


def test_register_duplicate_email():
    """Testar registro com email duplicado"""
    # Primeiro registro
    client.post(
        "/api/v1/auth/register",
        json={
            "email": "<EMAIL>",
            "password": "senha123",
            "full_name": "Teste",
            "role": "customer"
        }
    )
    
    # Segundo registro com mesmo email
    response = client.post(
        "/api/v1/auth/register",
        json={
            "email": "<EMAIL>",
            "password": "outrasenha",
            "full_name": "Outro Teste",
            "role": "customer"
        }
    )
    
    assert response.status_code == 400
    assert "já está em uso" in response.json()["detail"]


def test_login_success():
    """Testar login com sucesso"""
    # Registrar usuário
    client.post(
        "/api/v1/auth/register",
        json={
            "email": "<EMAIL>",
            "password": "senha123",
            "full_name": "Login Teste",
            "role": "customer"
        }
    )
    
    # Fazer login
    response = client.post(
        "/api/v1/auth/login",
        json={
            "email": "<EMAIL>",
            "password": "senha123"
        }
    )
    
    assert response.status_code == 200
    data = response.json()
    assert "access_token" in data
    assert "refresh_token" in data
    assert data["token_type"] == "bearer"
    assert data["user"]["email"] == "<EMAIL>"


def test_login_invalid_credentials():
    """Testar login com credenciais inválidas"""
    response = client.post(
        "/api/v1/auth/login",
        json={
            "email": "<EMAIL>",
            "password": "senhaerrada"
        }
    )
    
    assert response.status_code == 401
    assert "incorretos" in response.json()["detail"]


def test_get_current_user():
    """Testar obtenção de usuário atual"""
    # Registrar e fazer login
    client.post(
        "/api/v1/auth/register",
        json={
            "email": "<EMAIL>",
            "password": "senha123",
            "full_name": "Current User",
            "role": "customer"
        }
    )
    
    login_response = client.post(
        "/api/v1/auth/login",
        json={
            "email": "<EMAIL>",
            "password": "senha123"
        }
    )
    
    token = login_response.json()["access_token"]
    
    # Obter usuário atual
    response = client.get(
        "/api/v1/auth/me",
        headers={"Authorization": f"Bearer {token}"}
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["email"] == "<EMAIL>"
    assert data["full_name"] == "Current User"


def test_get_current_user_invalid_token():
    """Testar obtenção de usuário com token inválido"""
    response = client.get(
        "/api/v1/auth/me",
        headers={"Authorization": "Bearer token_invalido"}
    )
    
    assert response.status_code == 401


def test_update_user_info():
    """Testar atualização de informações do usuário"""
    # Registrar e fazer login
    client.post(
        "/api/v1/auth/register",
        json={
            "email": "<EMAIL>",
            "password": "senha123",
            "full_name": "Update User",
            "role": "customer"
        }
    )
    
    login_response = client.post(
        "/api/v1/auth/login",
        json={
            "email": "<EMAIL>",
            "password": "senha123"
        }
    )
    
    token = login_response.json()["access_token"]
    
    # Atualizar informações
    response = client.put(
        "/api/v1/auth/me",
        headers={"Authorization": f"Bearer {token}"},
        json={
            "full_name": "Nome Atualizado",
            "phone": "(11) 88888-8888",
            "city": "São Paulo"
        }
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["full_name"] == "Nome Atualizado"
    assert data["phone"] == "(11) 88888-8888"


def test_change_password():
    """Testar mudança de senha"""
    # Registrar e fazer login
    client.post(
        "/api/v1/auth/register",
        json={
            "email": "<EMAIL>",
            "password": "senhaantiga",
            "full_name": "Password User",
            "role": "customer"
        }
    )
    
    login_response = client.post(
        "/api/v1/auth/login",
        json={
            "email": "<EMAIL>",
            "password": "senhaantiga"
        }
    )
    
    token = login_response.json()["access_token"]
    
    # Alterar senha
    response = client.post(
        "/api/v1/auth/change-password",
        headers={"Authorization": f"Bearer {token}"},
        json={
            "current_password": "senhaantiga",
            "new_password": "senhanova123"
        }
    )
    
    assert response.status_code == 204
    
    # Testar login com nova senha
    new_login_response = client.post(
        "/api/v1/auth/login",
        json={
            "email": "<EMAIL>",
            "password": "senhanova123"
        }
    )
    
    assert new_login_response.status_code == 200
