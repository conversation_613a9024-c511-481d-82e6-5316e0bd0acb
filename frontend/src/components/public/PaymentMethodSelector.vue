<template>
  <div class="space-y-4">
    <div
      v-for="method in availableMethods"
      :key="method.id"
      class="relative"
    >
      <label
        :class="[
          'flex items-center p-4 border rounded-lg cursor-pointer transition-all duration-200',
          modelValue === method.id
            ? 'border-primary-500 bg-primary-50 ring-2 ring-primary-200'
            : 'border-gray-300 hover:border-gray-400'
        ]"
      >
        <input
          type="radio"
          :value="method.id"
          :checked="modelValue === method.id"
          @change="handleChange(method.id)"
          class="sr-only"
        />
        
        <!-- Ícone do método -->
        <div class="flex-shrink-0 mr-4">
          <div
            :class="[
              'w-12 h-12 rounded-lg flex items-center justify-center',
              getMethodStyles(method.id).bg
            ]"
          >
            <component
              :is="getMethodIcon(method.id)"
              :class="[
                'w-6 h-6',
                getMethodStyles(method.id).icon
              ]"
            />
          </div>
        </div>

        <!-- Informações do método -->
        <div class="flex-1">
          <div class="flex items-center justify-between">
            <h3 class="text-sm font-medium text-gray-900">
              {{ method.name }}
            </h3>
            
            <!-- Badge de desconto ou vantagem -->
            <span
              v-if="getMethodBadge(method.id)"
              :class="[
                'inline-flex items-center px-2 py-1 rounded-full text-xs font-medium',
                getMethodBadge(method.id)?.class
              ]"
            >
              {{ getMethodBadge(method.id)?.text }}
            </span>
          </div>
          
          <p class="text-sm text-gray-600 mt-1">
            {{ getMethodDescription(method.id) }}
          </p>
          
          <!-- Informações específicas do método -->
          <div v-if="getMethodDetails(method.id)" class="mt-2 text-xs text-gray-500">
            {{ getMethodDetails(method.id) }}
          </div>
        </div>

        <!-- Indicador de seleção -->
        <div class="flex-shrink-0 ml-4">
          <div
            :class="[
              'w-5 h-5 rounded-full border-2 flex items-center justify-center',
              modelValue === method.id
                ? 'border-primary-500 bg-primary-500'
                : 'border-gray-300'
            ]"
          >
            <div
              v-if="modelValue === method.id"
              class="w-2 h-2 rounded-full bg-white"
            />
          </div>
        </div>
      </label>
    </div>

    <!-- Erro de validação -->
    <p v-if="errors?.payment_method" class="text-sm text-red-600">
      {{ errors.payment_method }}
    </p>

    <!-- Informações de segurança -->
    <div class="mt-6 p-4 bg-gray-50 rounded-lg">
      <div class="flex items-start space-x-3">
        <ShieldCheckIcon class="w-5 h-5 text-green-500 mt-0.5 flex-shrink-0" />
        <div>
          <h4 class="text-sm font-medium text-gray-900">Pagamento Seguro</h4>
          <p class="text-sm text-gray-600 mt-1">
            Todos os pagamentos são processados de forma segura e criptografada.
            Seus dados financeiros estão protegidos.
          </p>
        </div>
      </div>
    </div>

    <!-- Detalhes específicos do método selecionado -->
    <div v-if="modelValue" class="mt-6">
      <!-- PIX -->
      <div v-if="modelValue === 'pix'" class="p-4 bg-blue-50 rounded-lg">
        <div class="flex items-start space-x-3">
          <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center flex-shrink-0">
            <CreditCardIcon class="w-4 h-4 text-blue-600" />
          </div>
          <div>
            <h4 class="text-sm font-medium text-blue-900">Como funciona o PIX</h4>
            <ul class="text-sm text-blue-800 mt-2 space-y-1">
              <li>• Você receberá um QR Code para pagamento</li>
              <li>• Abra seu app do banco e escaneie o código</li>
              <li>• Confirme o pagamento no seu banco</li>
              <li>• Seu pedido será confirmado automaticamente</li>
            </ul>
          </div>
        </div>
      </div>

      <!-- Cartão -->
      <div v-if="modelValue.includes('card')" class="p-4 bg-green-50 rounded-lg">
        <div class="flex items-start space-x-3">
          <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center flex-shrink-0">
            <CreditCardIcon class="w-4 h-4 text-green-600" />
          </div>
          <div>
            <h4 class="text-sm font-medium text-green-900">Pagamento com Cartão</h4>
            <p class="text-sm text-green-800 mt-1">
              Você será redirecionado para uma página segura para inserir os dados do cartão.
              Aceitamos as principais bandeiras.
            </p>
          </div>
        </div>
      </div>

      <!-- Dinheiro -->
      <div v-if="modelValue === 'cash'" class="p-4 bg-yellow-50 rounded-lg">
        <div class="flex items-start space-x-3">
          <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center flex-shrink-0">
            <BanknotesIcon class="w-4 h-4 text-yellow-600" />
          </div>
          <div>
            <h4 class="text-sm font-medium text-yellow-900">Pagamento em Dinheiro</h4>
            <p class="text-sm text-yellow-800 mt-1">
              Tenha o valor exato ou informe se precisará de troco no campo de observações.
              O pagamento será feito na entrega ou retirada.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import {
  CreditCardIcon,
  BanknotesIcon,
  DevicePhoneMobileIcon,
  ShieldCheckIcon
} from '@heroicons/vue/24/outline'

interface PaymentMethod {
  id: string
  name: string
  icon: string
}

interface Props {
  modelValue: string
  availableMethods: PaymentMethod[]
  errors?: Record<string, string>
}

interface Emits {
  (e: 'update:modelValue', value: string): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Methods
const handleChange = (methodId: string) => {
  emit('update:modelValue', methodId)
}

const getMethodIcon = (methodId: string) => {
  const icons: Record<string, any> = {
    pix: DevicePhoneMobileIcon,
    credit_card: CreditCardIcon,
    debit_card: CreditCardIcon,
    cash: BanknotesIcon
  }
  
  return icons[methodId] || CreditCardIcon
}

const getMethodStyles = (methodId: string) => {
  const styles: Record<string, { bg: string; icon: string }> = {
    pix: {
      bg: 'bg-blue-100',
      icon: 'text-blue-600'
    },
    credit_card: {
      bg: 'bg-green-100',
      icon: 'text-green-600'
    },
    debit_card: {
      bg: 'bg-purple-100',
      icon: 'text-purple-600'
    },
    cash: {
      bg: 'bg-yellow-100',
      icon: 'text-yellow-600'
    }
  }
  
  return styles[methodId] || styles.credit_card
}

const getMethodDescription = (methodId: string): string => {
  const descriptions: Record<string, string> = {
    pix: 'Pagamento instantâneo via QR Code',
    credit_card: 'Parcelamento disponível',
    debit_card: 'Débito à vista',
    cash: 'Pagamento na entrega ou retirada'
  }
  
  return descriptions[methodId] || ''
}

const getMethodDetails = (methodId: string): string => {
  const details: Record<string, string> = {
    pix: 'Aprovação em até 2 minutos',
    credit_card: 'Parcele em até 12x sem juros',
    debit_card: 'Aprovação instantânea',
    cash: 'Tenha o valor exato ou informe se precisa de troco'
  }
  
  return details[methodId] || ''
}

const getMethodBadge = (methodId: string) => {
  const badges: Record<string, { text: string; class: string } | null> = {
    pix: {
      text: 'Mais rápido',
      class: 'bg-blue-100 text-blue-800'
    },
    credit_card: {
      text: 'Parcelado',
      class: 'bg-green-100 text-green-800'
    },
    debit_card: null,
    cash: {
      text: 'Sem taxas',
      class: 'bg-yellow-100 text-yellow-800'
    }
  }
  
  return badges[methodId]
}
</script>
