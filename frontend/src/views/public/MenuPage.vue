<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header do estabelecimento -->
    <div v-if="establishment" class="bg-white shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-6">
          <!-- Banner/Logo -->
          <div class="flex items-center space-x-4 mb-4">
            <img
              v-if="establishment.logo_url"
              :src="establishment.logo_url"
              :alt="establishment.name"
              class="h-16 w-16 rounded-lg object-cover"
            />
            <div>
              <h1 class="text-3xl font-bold text-gray-900">{{ establishment.name }}</h1>
              <p v-if="establishment.description" class="text-gray-600 mt-1">
                {{ establishment.description }}
              </p>
            </div>
          </div>

          <!-- Informações do estabelecimento -->
          <div class="flex flex-wrap items-center gap-4 text-sm text-gray-600">
            <div v-if="establishment.phone" class="flex items-center">
              <PhoneIcon class="h-4 w-4 mr-1" />
              {{ establishment.phone }}
            </div>
            <div v-if="establishment.address" class="flex items-center">
              <MapPinIcon class="h-4 w-4 mr-1" />
              {{ establishment.address }}
            </div>
            <div class="flex items-center">
              <ClockIcon class="h-4 w-4 mr-1" />
              {{ establishment.is_open ? 'Aberto agora' : 'Fechado' }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Conteúdo principal -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div class="lg:grid lg:grid-cols-4 lg:gap-8">
        <!-- Sidebar com filtros -->
        <div class="lg:col-span-1">
          <div class="sticky top-8">
            <MenuFilters />
          </div>
        </div>

        <!-- Lista do cardápio -->
        <div class="lg:col-span-3 mt-8 lg:mt-0">
          <!-- Busca -->
          <div class="mb-6">
            <div class="relative">
              <MagnifyingGlassIcon class="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
              <input
                v-model="searchQuery"
                type="text"
                placeholder="Buscar pratos..."
                class="w-full pl-10 pr-4 py-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                @input="handleSearch"
              />
            </div>
          </div>

          <!-- Filtros rápidos -->
          <div class="flex flex-wrap gap-2 mb-6">
            <button
              v-for="filter in quickFilters"
              :key="filter.key"
              @click="toggleQuickFilter(filter.key)"
              :class="[
                'px-3 py-1 rounded-full text-sm font-medium transition-colors duration-200',
                isFilterActive(filter.key)
                  ? 'bg-primary-100 text-primary-800 border border-primary-200'
                  : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
              ]"
            >
              <component :is="filter.icon" class="h-4 w-4 mr-1 inline" />
              {{ filter.label }}
            </button>
          </div>

          <!-- Ordenação -->
          <div class="flex justify-between items-center mb-6">
            <p class="text-sm text-gray-600">
              {{ filteredItems.length }} {{ filteredItems.length === 1 ? 'item encontrado' : 'itens encontrados' }}
            </p>
            
            <select
              v-model="sortBy"
              @change="handleSort"
              class="text-sm border border-gray-300 rounded-md px-3 py-1 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
            >
              <option value="name">Nome A-Z</option>
              <option value="name-desc">Nome Z-A</option>
              <option value="price">Menor preço</option>
              <option value="price-desc">Maior preço</option>
            </select>
          </div>

          <!-- Loading state -->
          <div v-if="isLoading" class="flex justify-center py-12">
            <div class="spinner w-8 h-8"></div>
          </div>

          <!-- Error state -->
          <div v-else-if="error" class="text-center py-12">
            <ExclamationTriangleIcon class="mx-auto h-12 w-12 text-red-400" />
            <h3 class="mt-2 text-sm font-medium text-gray-900">Erro ao carregar cardápio</h3>
            <p class="mt-1 text-sm text-gray-500">{{ error }}</p>
            <button
              @click="loadMenu"
              class="mt-4 btn btn-primary"
            >
              Tentar novamente
            </button>
          </div>

          <!-- Lista de itens por categoria -->
          <div v-else-if="!selectedCategory" class="space-y-8">
            <div
              v-for="category in categoriesWithItems"
              :key="category.id"
              class="category-section"
            >
              <h2 class="text-2xl font-bold text-gray-900 mb-4 flex items-center">
                <img
                  v-if="category.image_url"
                  :src="category.image_url"
                  :alt="category.name"
                  class="h-8 w-8 rounded-lg object-cover mr-3"
                />
                {{ category.name }}
                <span class="ml-2 text-sm font-normal text-gray-500">
                  ({{ getItemsByCategory(category.id).length }})
                </span>
              </h2>
              
              <p v-if="category.description" class="text-gray-600 mb-4">
                {{ category.description }}
              </p>

              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <MenuItemCard
                  v-for="item in getItemsByCategory(category.id)"
                  :key="item.id"
                  :item="item"
                  @add-to-cart="handleAddToCart"
                />
              </div>
            </div>
          </div>

          <!-- Lista de itens filtrados -->
          <div v-else class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <MenuItemCard
              v-for="item in filteredItems"
              :key="item.id"
              :item="item"
              @add-to-cart="handleAddToCart"
            />
          </div>

          <!-- Empty state -->
          <div v-if="!isLoading && !error && filteredItems.length === 0" class="text-center py-12">
            <MagnifyingGlassIcon class="mx-auto h-12 w-12 text-gray-400" />
            <h3 class="mt-2 text-sm font-medium text-gray-900">Nenhum item encontrado</h3>
            <p class="mt-1 text-sm text-gray-500">
              Tente ajustar os filtros ou buscar por outro termo.
            </p>
            <button
              @click="clearFilters"
              class="mt-4 btn btn-outline"
            >
              Limpar filtros
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Carrinho flutuante -->
    <FloatingCart v-if="cartStore.totalItems > 0" />

    <!-- Modal de item -->
    <MenuItemModal
      v-if="showItemModal"
      :item="selectedItem"
      @close="closeItemModal"
      @add-to-cart="handleAddToCart"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import {
  PhoneIcon,
  MapPinIcon,
  ClockIcon,
  MagnifyingGlassIcon,
  ExclamationTriangleIcon,
  HeartIcon,
  LeafIcon,
  FireIcon,
  ShieldCheckIcon
} from '@heroicons/vue/24/outline'
import { useToast } from 'vue-toastification'

import { usePublicMenuStore } from '@/stores/publicMenu'
import { useCartStore } from '@/stores/cart'
import type { MenuItem } from '@/types'

// Componentes
const MenuFilters = defineAsyncComponent(() => import('@/components/public/MenuFilters.vue'))
const MenuItemCard = defineAsyncComponent(() => import('@/components/public/MenuItemCard.vue'))
const FloatingCart = defineAsyncComponent(() => import('@/components/public/FloatingCart.vue'))
const MenuItemModal = defineAsyncComponent(() => import('@/components/public/MenuItemModal.vue'))

const route = useRoute()
const toast = useToast()
const menuStore = usePublicMenuStore()
const cartStore = useCartStore()

// Estado local
const searchQuery = ref('')
const sortBy = ref('name')
const showItemModal = ref(false)
const selectedItem = ref<MenuItem | null>(null)

// Computed
const establishment = computed(() => menuStore.establishment)
const isLoading = computed(() => menuStore.isLoading)
const error = computed(() => menuStore.error)
const filteredItems = computed(() => menuStore.filteredMenuItems)
const selectedCategory = computed(() => menuStore.filters.categoryId)

const categoriesWithItems = computed(() => {
  return menuStore.availableCategories.filter(category =>
    menuStore.getItemsByCategory(category.id).length > 0
  )
})

const quickFilters = [
  { key: 'isVegetarian', label: 'Vegetariano', icon: LeafIcon },
  { key: 'isVegan', label: 'Vegano', icon: HeartIcon },
  { key: 'isGlutenFree', label: 'Sem Glúten', icon: ShieldCheckIcon },
  { key: 'isSpicy', label: 'Picante', icon: FireIcon }
]

// Methods
const loadMenu = async () => {
  const slug = route.params.slug as string
  
  try {
    await menuStore.fetchPublicMenu(slug)
    
    // Inicializar carrinho
    if (establishment.value) {
      await cartStore.fetchCart(establishment.value.id)
    }
  } catch (error) {
    console.error('Erro ao carregar cardápio:', error)
  }
}

const handleSearch = () => {
  menuStore.searchItems(searchQuery.value)
}

const handleSort = () => {
  const [field, order] = sortBy.value.includes('-desc') 
    ? [sortBy.value.replace('-desc', ''), 'desc']
    : [sortBy.value, 'asc']
  
  menuStore.setSorting(field, order as 'asc' | 'desc')
}

const toggleQuickFilter = (filterKey: string) => {
  menuStore.toggleDietaryFilter(filterKey as any)
}

const isFilterActive = (filterKey: string): boolean => {
  return (menuStore.filters as any)[filterKey] === true
}

const getItemsByCategory = (categoryId: string) => {
  return menuStore.getItemsByCategory(categoryId)
}

const clearFilters = () => {
  searchQuery.value = ''
  sortBy.value = 'name'
  menuStore.clearFilters()
}

const handleAddToCart = async (item: MenuItem, quantity: number = 1, notes?: string) => {
  if (!establishment.value) return

  try {
    await cartStore.addItem(establishment.value.id, item, quantity, notes)
    toast.success(`${item.name} adicionado ao carrinho!`)
    closeItemModal()
  } catch (error: any) {
    toast.error(error.message || 'Erro ao adicionar item ao carrinho')
  }
}

const openItemModal = (item: MenuItem) => {
  selectedItem.value = item
  showItemModal.value = true
}

const closeItemModal = () => {
  showItemModal.value = false
  selectedItem.value = null
}

// Watchers
watch(searchQuery, () => {
  handleSearch()
})

// Lifecycle
onMounted(() => {
  loadMenu()
})

// Lazy loading de componentes
import { defineAsyncComponent } from 'vue'
</script>

<style scoped>
.category-section {
  scroll-margin-top: 100px;
}

.spinner {
  border: 2px solid #f3f4f6;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
