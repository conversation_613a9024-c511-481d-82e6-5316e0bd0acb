"""
Testes para a aplicação principal
"""
import pytest
from fastapi import FastAPI
from fastapi.testclient import TestClient

# Criar uma app simples para testes sem middlewares problemáticos
test_app = FastAPI()

@test_app.get("/")
async def root():
    return {
        "message": "Bem-vindo ao Cardápio Digital API",
        "version": "1.0.0",
        "environment": "test"
    }

@test_app.get("/health")
async def health():
    return {
        "status": "healthy",
        "timestamp": **********,
        "version": "1.0.0"
    }

client = TestClient(test_app)


def test_root_endpoint():
    """Testar endpoint raiz"""
    response = client.get("/")
    print(f"Status: {response.status_code}")
    print(f"Response: {response.text}")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "version" in data
    assert "environment" in data


def test_health_check():
    """Testar endpoint de health check"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert "timestamp" in data
    assert "version" in data


def test_not_found():
    """Testar endpoint não encontrado"""
    response = client.get("/endpoint-inexistente")
    assert response.status_code == 404
    data = response.json()
    assert "detail" in data
    assert "path" in data
