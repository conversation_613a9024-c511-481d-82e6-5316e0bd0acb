"""
Testes para APIs de estabelecimentos
"""
import pytest
from fastapi.testclient import TestClient
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.pool import StaticPool

from app.core.database import get_db, Base
from app.models.user import UserRole
from app.services.establishment_service import EstablishmentService
from app.schemas.establishment import EstablishmentCreate

# Configurar banco de dados de teste em memória
SQLALCHEMY_DATABASE_URL = "sqlite:///:memory:"

engine = create_engine(
    SQLALCHEMY_DATABASE_URL,
    connect_args={"check_same_thread": False},
    poolclass=StaticPool,
)
TestingSessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)


def override_get_db():
    try:
        db = TestingSessionLocal()
        yield db
    finally:
        db.close()


# Criar uma app simples para testes sem MinIO
from fastapi import FastAPI
test_app = FastAPI()

test_app.dependency_overrides[get_db] = override_get_db

client = TestClient(test_app)


@pytest.fixture(autouse=True)
def setup_database():
    """Configurar banco de dados para cada teste"""
    Base.metadata.create_all(bind=engine)
    yield
    Base.metadata.drop_all(bind=engine)


def create_test_user_and_login(role: str = "owner", establishment_slug: str = None):
    """Criar usuário de teste e fazer login"""
    user_data = {
        "email": f"test_{role}@teste.com",
        "password": "senha123",
        "full_name": f"Test {role.title()}",
        "role": role
    }
    
    if establishment_slug:
        user_data["establishment_slug"] = establishment_slug
    
    # Registrar usuário
    register_response = client.post("/api/v1/auth/register", json=user_data)
    
    # Fazer login
    login_response = client.post(
        "/api/v1/auth/login",
        json={
            "email": user_data["email"],
            "password": user_data["password"]
        }
    )
    
    return login_response.json()["access_token"]


def test_create_establishment_service():
    """Testar criação de estabelecimento via serviço"""
    db = TestingSessionLocal()
    service = EstablishmentService(db)

    establishment_data = EstablishmentCreate(
        name="Restaurante Teste",
        slug="restaurante-teste",
        email="<EMAIL>",
        phone="(11) 99999-9999",
        address="Rua Teste, 123",
        city="São Paulo",
        state="SP",
        primary_color="#E53E3E",
        delivery_fee=5.00,
        minimum_order=20.00
    )

    establishment = service.create_establishment(establishment_data)

    assert establishment.name == "Restaurante Teste"
    assert establishment.slug == "restaurante-teste"
    assert establishment.email == "<EMAIL>"
    assert establishment.primary_color == "#E53E3E"
    assert establishment.delivery_fee == 5.00
    assert establishment.is_active is True

    db.close()


def test_create_establishment_duplicate_slug():
    """Testar criação com slug duplicado"""
    establishment_data = {
        "name": "Restaurante 1",
        "slug": "mesmo-slug",
        "email": "<EMAIL>"
    }
    
    # Primeiro estabelecimento
    client.post("/api/v1/establishments/", json=establishment_data)
    
    # Segundo com mesmo slug
    establishment_data["name"] = "Restaurante 2"
    establishment_data["email"] = "<EMAIL>"
    
    response = client.post("/api/v1/establishments/", json=establishment_data)
    
    assert response.status_code == 400
    assert "já está em uso" in response.json()["detail"]


def test_list_establishments():
    """Testar listagem de estabelecimentos"""
    # Criar alguns estabelecimentos
    establishments = [
        {
            "name": "Restaurante A",
            "slug": "restaurante-a",
            "email": "<EMAIL>",
            "city": "São Paulo"
        },
        {
            "name": "Restaurante B",
            "slug": "restaurante-b", 
            "email": "<EMAIL>",
            "city": "Rio de Janeiro"
        }
    ]
    
    for est in establishments:
        client.post("/api/v1/establishments/", json=est)
    
    # Listar todos
    response = client.get("/api/v1/establishments/")
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 2
    
    # Filtrar por cidade
    response = client.get("/api/v1/establishments/?city=São Paulo")
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 1
    assert data[0]["city"] == "São Paulo"
    
    # Buscar por nome
    response = client.get("/api/v1/establishments/?search=Restaurante A")
    assert response.status_code == 200
    data = response.json()
    assert len(data) == 1
    assert data[0]["name"] == "Restaurante A"


def test_get_establishment_public():
    """Testar obtenção de dados públicos do estabelecimento"""
    # Criar estabelecimento
    establishment_data = {
        "name": "Restaurante Público",
        "slug": "restaurante-publico",
        "email": "<EMAIL>",
        "phone": "(11) 88888-8888",
        "primary_color": "#FF5722"
    }
    
    client.post("/api/v1/establishments/", json=establishment_data)
    
    # Obter dados públicos
    response = client.get("/api/v1/establishments/restaurante-publico")
    
    assert response.status_code == 200
    data = response.json()
    assert data["name"] == "Restaurante Público"
    assert data["slug"] == "restaurante-publico"
    assert data["phone"] == "(11) 88888-8888"
    assert data["primary_color"] == "#FF5722"
    # Email não deve aparecer nos dados públicos
    assert "email" not in data or data.get("email") is None


def test_get_establishment_public_not_found():
    """Testar obtenção de estabelecimento inexistente"""
    response = client.get("/api/v1/establishments/inexistente")
    
    assert response.status_code == 404
    assert "não encontrado" in response.json()["detail"]


def test_get_my_establishment_without_auth():
    """Testar obtenção do estabelecimento sem autenticação"""
    response = client.get("/api/v1/establishments/me")
    
    assert response.status_code == 401


def test_update_establishment_colors():
    """Testar atualização de cores do estabelecimento"""
    # Criar estabelecimento
    establishment_data = {
        "name": "Restaurante Cores",
        "slug": "restaurante-cores",
        "email": "<EMAIL>"
    }
    
    client.post("/api/v1/establishments/", json=establishment_data)
    
    # Criar usuário proprietário
    token = create_test_user_and_login("owner", "restaurante-cores")
    
    # Atualizar cores
    update_data = {
        "primary_color": "#9C27B0",
        "secondary_color": "#673AB7",
        "accent_color": "#FF9800"
    }
    
    response = client.put(
        "/api/v1/establishments/me",
        headers={"Authorization": f"Bearer {token}"},
        json=update_data
    )
    
    assert response.status_code == 200
    data = response.json()
    assert data["primary_color"] == "#9C27B0"
    assert data["secondary_color"] == "#673AB7"
    assert data["accent_color"] == "#FF9800"


def test_update_establishment_invalid_color():
    """Testar atualização com cor inválida"""
    # Criar estabelecimento
    establishment_data = {
        "name": "Restaurante Cor Inválida",
        "slug": "restaurante-cor-invalida",
        "email": "<EMAIL>"
    }
    
    client.post("/api/v1/establishments/", json=establishment_data)
    
    # Criar usuário proprietário
    token = create_test_user_and_login("owner", "restaurante-cor-invalida")
    
    # Tentar atualizar com cor inválida
    update_data = {
        "primary_color": "cor-invalida"
    }
    
    response = client.put(
        "/api/v1/establishments/me",
        headers={"Authorization": f"Bearer {token}"},
        json=update_data
    )
    
    assert response.status_code == 422  # Validation error


def test_customer_cannot_access_establishment_management():
    """Testar que cliente não pode acessar gerenciamento"""
    # Criar usuário cliente
    token = create_test_user_and_login("customer")
    
    # Tentar acessar dados do estabelecimento
    response = client.get(
        "/api/v1/establishments/me",
        headers={"Authorization": f"Bearer {token}"}
    )
    
    assert response.status_code == 403
    assert "restrito" in response.json()["detail"]
