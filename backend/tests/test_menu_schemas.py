"""
Testes para schemas de cardápio
"""
import pytest
from decimal import Decimal
from pydantic import ValidationError
from app.schemas.menu import (
    CategoryCreate, 
    CategoryUpdate, 
    MenuItemCreate, 
    MenuItemUpdate,
    IngredientCreate,
    CustomizationOptionCreate,
    MenuItemCustomizationCreate
)


def test_category_create_valid():
    """Testar criação de categoria válida"""
    data = {
        "name": "Entradas",
        "description": "Pratos para começar bem a refeição",
        "sort_order": 1,
        "is_featured": True,
        "available_start_time": "09:00",
        "available_end_time": "22:00"
    }
    
    category = CategoryCreate(**data)
    
    assert category.name == "Entradas"
    assert category.description == "Pratos para começar bem a refeição"
    assert category.sort_order == 1
    assert category.is_featured is True
    assert category.available_start_time == "09:00"
    assert category.available_end_time == "22:00"
    assert category.available_all_day is True  # valor padrão


def test_category_create_invalid_time():
    """Testar horário inválido"""
    data = {
        "name": "Categoria Teste",
        "available_start_time": "25:00"  # Horário inválido
    }
    
    with pytest.raises(ValidationError) as exc_info:
        CategoryCreate(**data)
    
    assert "formato HH:MM" in str(exc_info.value)


def test_category_create_short_name():
    """Testar nome muito curto"""
    data = {
        "name": "A"  # Muito curto
    }
    
    with pytest.raises(ValidationError) as exc_info:
        CategoryCreate(**data)
    
    assert "at least 2 characters" in str(exc_info.value)


def test_category_update_partial():
    """Testar atualização parcial de categoria"""
    data = {
        "name": "Novo Nome",
        "is_featured": True
    }
    
    category_update = CategoryUpdate(**data)
    
    assert category_update.name == "Novo Nome"
    assert category_update.is_featured is True
    assert category_update.description is None  # Não fornecido


def test_menu_item_create_valid():
    """Testar criação de item válido"""
    data = {
        "name": "Hambúrguer Artesanal",
        "description": "Hambúrguer com carne 180g, queijo e salada",
        "price": Decimal("25.90"),
        "category_id": "123e4567-e89b-12d3-a456-426614174000",
        "prep_time_minutes": 20,
        "is_vegetarian": False,
        "is_spicy": True,
        "calories": 650
    }
    
    item = MenuItemCreate(**data)
    
    assert item.name == "Hambúrguer Artesanal"
    assert item.price == Decimal("25.90")
    assert item.category_id == "123e4567-e89b-12d3-a456-426614174000"
    assert item.prep_time_minutes == 20
    assert item.is_vegetarian is False
    assert item.is_spicy is True
    assert item.calories == 650
    assert item.is_available is True  # valor padrão


def test_menu_item_create_invalid_price():
    """Testar preço inválido"""
    data = {
        "name": "Item Teste",
        "price": Decimal("0.00"),  # Preço zero
        "category_id": "123e4567-e89b-12d3-a456-426614174000"
    }
    
    with pytest.raises(ValidationError) as exc_info:
        MenuItemCreate(**data)
    
    assert "greater than 0" in str(exc_info.value)


def test_menu_item_create_negative_stock():
    """Testar estoque negativo"""
    data = {
        "name": "Item Teste",
        "price": Decimal("10.00"),
        "category_id": "123e4567-e89b-12d3-a456-426614174000",
        "stock_quantity": -5  # Estoque negativo
    }
    
    with pytest.raises(ValidationError) as exc_info:
        MenuItemCreate(**data)
    
    assert "não pode ser negativo" in str(exc_info.value)


def test_menu_item_create_negative_prep_time():
    """Testar tempo de preparo negativo"""
    data = {
        "name": "Item Teste",
        "price": Decimal("10.00"),
        "category_id": "123e4567-e89b-12d3-a456-426614174000",
        "prep_time_minutes": -10  # Tempo negativo
    }
    
    with pytest.raises(ValidationError) as exc_info:
        MenuItemCreate(**data)
    
    assert "não pode ser negativo" in str(exc_info.value)


def test_menu_item_update_valid():
    """Testar atualização de item válida"""
    data = {
        "name": "Nome Atualizado",
        "price": Decimal("30.00"),
        "is_available": False,
        "is_featured": True
    }
    
    item_update = MenuItemUpdate(**data)
    
    assert item_update.name == "Nome Atualizado"
    assert item_update.price == Decimal("30.00")
    assert item_update.is_available is False
    assert item_update.is_featured is True
    assert item_update.description is None  # Não fornecido


def test_menu_item_create_default_values():
    """Testar valores padrão do item"""
    data = {
        "name": "Item Padrão",
        "price": Decimal("15.00"),
        "category_id": "123e4567-e89b-12d3-a456-426614174000"
    }
    
    item = MenuItemCreate(**data)
    
    # Verificar valores padrão
    assert item.is_available is True
    assert item.stock_quantity is None
    assert item.prep_time_minutes == 15
    assert item.is_featured is False
    assert item.is_spicy is False
    assert item.is_vegetarian is False
    assert item.is_vegan is False
    assert item.is_gluten_free is False
    assert item.is_dairy_free is False
    assert item.sort_order == 0


def test_ingredient_create_valid():
    """Testar criação de ingrediente válido"""
    data = {
        "name": "Tomate",
        "description": "Tomate fresco orgânico",
        "calories_per_100g": 18,
        "protein_per_100g": Decimal("0.9"),
        "carbs_per_100g": Decimal("3.9"),
        "fat_per_100g": Decimal("0.2"),
        "is_allergen": False
    }
    
    ingredient = IngredientCreate(**data)
    
    assert ingredient.name == "Tomate"
    assert ingredient.description == "Tomate fresco orgânico"
    assert ingredient.calories_per_100g == 18
    assert ingredient.protein_per_100g == Decimal("0.9")
    assert ingredient.is_allergen is False


def test_ingredient_create_allergen():
    """Testar criação de ingrediente alérgeno"""
    data = {
        "name": "Amendoim",
        "is_allergen": True,
        "allergen_type": "nuts"
    }
    
    ingredient = IngredientCreate(**data)
    
    assert ingredient.name == "Amendoim"
    assert ingredient.is_allergen is True
    assert ingredient.allergen_type == "nuts"


def test_customization_option_create_valid():
    """Testar criação de opção de personalização válida"""
    data = {
        "name": "Queijo Extra",
        "description": "Porção adicional de queijo",
        "price_modifier": Decimal("3.00"),
        "is_default": False,
        "sort_order": 1
    }
    
    option = CustomizationOptionCreate(**data)
    
    assert option.name == "Queijo Extra"
    assert option.description == "Porção adicional de queijo"
    assert option.price_modifier == Decimal("3.00")
    assert option.is_default is False
    assert option.sort_order == 1


def test_customization_option_create_default_values():
    """Testar valores padrão da opção de personalização"""
    data = {
        "name": "Opção Padrão"
    }
    
    option = CustomizationOptionCreate(**data)
    
    assert option.name == "Opção Padrão"
    assert option.price_modifier == Decimal("0.00")
    assert option.is_default is False
    assert option.sort_order == 0


def test_menu_item_customization_create_valid():
    """Testar criação de personalização de item válida"""
    data = {
        "name": "Tamanho",
        "description": "Escolha o tamanho do seu lanche",
        "customization_type": "single",
        "is_required": True,
        "max_selections": 1,
        "min_selections": 1,
        "menu_item_id": "123e4567-e89b-12d3-a456-426614174000",
        "options": [
            {
                "name": "Pequeno",
                "price_modifier": Decimal("0.00"),
                "is_default": True
            },
            {
                "name": "Grande",
                "price_modifier": Decimal("5.00")
            }
        ]
    }
    
    customization = MenuItemCustomizationCreate(**data)
    
    assert customization.name == "Tamanho"
    assert customization.customization_type == "single"
    assert customization.is_required is True
    assert customization.max_selections == 1
    assert customization.min_selections == 1
    assert len(customization.options) == 2
    assert customization.options[0].name == "Pequeno"
    assert customization.options[0].is_default is True
    assert customization.options[1].name == "Grande"
    assert customization.options[1].price_modifier == Decimal("5.00")


def test_menu_item_customization_invalid_type():
    """Testar tipo de personalização inválido"""
    data = {
        "name": "Personalização",
        "customization_type": "invalid_type",  # Tipo inválido
        "menu_item_id": "123e4567-e89b-12d3-a456-426614174000"
    }
    
    with pytest.raises(ValidationError) as exc_info:
        MenuItemCustomizationCreate(**data)
    
    # Deve falhar na validação do regex
    assert "match pattern" in str(exc_info.value)


def test_name_strip_whitespace():
    """Testar remoção de espaços em branco dos nomes"""
    data = {
        "name": "  Nome com Espaços  ",
        "price": Decimal("10.00"),
        "category_id": "123e4567-e89b-12d3-a456-426614174000"
    }
    
    item = MenuItemCreate(**data)
    assert item.name == "Nome com Espaços"  # Espaços removidos
