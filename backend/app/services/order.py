"""
Serviço de pedidos
"""
from sqlalchemy.orm import Session
from typing import List, Optional
from datetime import datetime, timedelta

from app.database import get_db
from app.models.order import Order, OrderItem, OrderStatus
from app.models.menu import MenuItem
from app.schemas.order import OrderFilters, OrderStats


class OrderService:
    """Serviço para gerenciamento de pedidos"""
    
    def __init__(self):
        pass
    
    async def get_orders(
        self,
        establishment_id: str,
        filters: OrderFilters,
        db: Session
    ) -> List[Order]:
        """Buscar pedidos com filtros"""
        
        query = db.query(Order).filter(Order.establishment_id == establishment_id)
        
        # Aplicar filtros
        if filters.status:
            query = query.filter(Order.status == filters.status)
        
        if filters.order_type:
            query = query.filter(Order.order_type == filters.order_type)
        
        if filters.customer_name:
            query = query.filter(Order.customer_name.ilike(f"%{filters.customer_name}%"))
        
        if filters.customer_phone:
            query = query.filter(Order.customer_phone.ilike(f"%{filters.customer_phone}%"))
        
        if filters.date_from:
            query = query.filter(Order.created_at >= filters.date_from)
        
        if filters.date_to:
            query = query.filter(Order.created_at <= filters.date_to)
        
        # Ordenar por data de criação (mais recentes primeiro)
        query = query.order_by(Order.created_at.desc())
        
        # Aplicar paginação
        query = query.offset(filters.offset).limit(filters.limit)
        
        return query.all()
    
    async def get_order_stats(
        self,
        establishment_id: str,
        db: Session
    ) -> OrderStats:
        """Obter estatísticas de pedidos"""
        
        # Buscar todos os pedidos do estabelecimento
        all_orders = db.query(Order).filter(Order.establishment_id == establishment_id).all()
        
        # Pedidos de hoje
        today = datetime.utcnow().date()
        today_orders = [o for o in all_orders if o.created_at.date() == today]
        
        # Contar por status
        stats = OrderStats(
            total_orders=len(all_orders),
            pending_orders=len([o for o in all_orders if o.status == OrderStatus.PENDING]),
            confirmed_orders=len([o for o in all_orders if o.status == OrderStatus.CONFIRMED]),
            preparing_orders=len([o for o in all_orders if o.status == OrderStatus.PREPARING]),
            ready_orders=len([o for o in all_orders if o.status == OrderStatus.READY]),
            out_for_delivery_orders=len([o for o in all_orders if o.status == OrderStatus.OUT_FOR_DELIVERY]),
            delivered_orders=len([o for o in all_orders if o.status == OrderStatus.DELIVERED]),
            cancelled_orders=len([o for o in all_orders if o.status == OrderStatus.CANCELLED]),
            total_revenue=sum(o.total_amount for o in all_orders if o.status != OrderStatus.CANCELLED),
            today_revenue=sum(o.total_amount for o in today_orders if o.status != OrderStatus.CANCELLED),
            average_order_value=0
        )
        
        # Calcular ticket médio
        completed_orders = [o for o in all_orders if o.status not in [OrderStatus.CANCELLED, OrderStatus.PENDING]]
        if completed_orders:
            stats.average_order_value = sum(o.total_amount for o in completed_orders) / len(completed_orders)
        
        return stats
    
    async def update_order_status(
        self,
        order_id: str,
        new_status: OrderStatus,
        notes: Optional[str] = None,
        db: Session = None
    ) -> Order:
        """Atualizar status do pedido"""
        
        if not db:
            db = next(get_db())
        
        order = db.query(Order).filter(Order.id == order_id).first()
        
        if not order:
            raise ValueError("Pedido não encontrado")
        
        # Verificar se a transição é válida
        if not order.can_transition_to(new_status):
            raise ValueError(f"Não é possível alterar status de {order.status} para {new_status}")
        
        # Atualizar status
        old_status = order.status
        order.update_status(new_status)
        
        # Adicionar notas se fornecidas
        if notes:
            if order.notes:
                order.notes += f"\n{datetime.utcnow().strftime('%d/%m/%Y %H:%M')} - {notes}"
            else:
                order.notes = f"{datetime.utcnow().strftime('%d/%m/%Y %H:%M')} - {notes}"
        
        db.commit()
        db.refresh(order)
        
        # Enviar notificações em background
        from app.services.notification import NotificationService
        notification_service = NotificationService()
        
        # Não aguardar a notificação para não bloquear a resposta
        import asyncio
        asyncio.create_task(
            notification_service.send_order_status_update(order.id, new_status.value)
        )
        
        return order
    
    async def cancel_order(
        self,
        order_id: str,
        reason: Optional[str] = None,
        db: Session = None
    ) -> Order:
        """Cancelar pedido"""
        
        if not db:
            db = next(get_db())
        
        order = db.query(Order).filter(Order.id == order_id).first()
        
        if not order:
            raise ValueError("Pedido não encontrado")
        
        if not order.can_be_cancelled:
            raise ValueError("Pedido não pode ser cancelado")
        
        # Cancelar pedido
        order.update_status(OrderStatus.CANCELLED)
        
        # Adicionar motivo do cancelamento
        if reason:
            if order.notes:
                order.notes += f"\n{datetime.utcnow().strftime('%d/%m/%Y %H:%M')} - Cancelado: {reason}"
            else:
                order.notes = f"{datetime.utcnow().strftime('%d/%m/%Y %H:%M')} - Cancelado: {reason}"
        
        # Restaurar estoque
        await self.restore_stock(order_id, db)
        
        db.commit()
        db.refresh(order)
        
        # Processar estorno se necessário
        if order.payment_status.value in ["completed", "processing"]:
            from app.services.payment import PaymentService
            payment_service = PaymentService()
            
            # Buscar transação de pagamento
            for transaction in order.payment_transactions:
                if transaction.status.value == "completed":
                    await payment_service.refund_payment(transaction.transaction_id)
                    break
        
        return order
    
    async def restore_stock(self, order_id: str, db: Session = None):
        """Restaurar estoque dos itens do pedido"""
        
        if not db:
            db = next(get_db())
        
        order = db.query(Order).filter(Order.id == order_id).first()
        
        if not order:
            return
        
        # Restaurar estoque de cada item
        for order_item in order.items:
            menu_item = db.query(MenuItem).filter(
                MenuItem.id == order_item.menu_item_id
            ).first()
            
            if menu_item and menu_item.stock_quantity is not None:
                menu_item.stock_quantity += order_item.quantity
        
        db.commit()
    
    async def get_active_orders(self, establishment_id: str, db: Session) -> List[Order]:
        """Buscar pedidos ativos (não entregues nem cancelados)"""
        
        return db.query(Order).filter(
            Order.establishment_id == establishment_id,
            Order.status.in_([
                OrderStatus.PENDING,
                OrderStatus.CONFIRMED,
                OrderStatus.PREPARING,
                OrderStatus.READY,
                OrderStatus.OUT_FOR_DELIVERY
            ])
        ).order_by(Order.created_at.asc()).all()
    
    async def get_orders_by_status(
        self,
        establishment_id: str,
        status: OrderStatus,
        db: Session
    ) -> List[Order]:
        """Buscar pedidos por status específico"""
        
        return db.query(Order).filter(
            Order.establishment_id == establishment_id,
            Order.status == status
        ).order_by(Order.created_at.asc()).all()
    
    async def get_delivery_orders_in_progress(self, establishment_id: str, db: Session) -> List[Order]:
        """Buscar pedidos de delivery em andamento"""
        
        return db.query(Order).filter(
            Order.establishment_id == establishment_id,
            Order.order_type == "delivery",
            Order.status.in_([
                OrderStatus.CONFIRMED,
                OrderStatus.PREPARING,
                OrderStatus.READY,
                OrderStatus.OUT_FOR_DELIVERY
            ])
        ).order_by(Order.created_at.asc()).all()
    
    async def calculate_estimated_times(self, establishment_id: str, db: Session) -> dict:
        """Calcular tempos estimados baseados no histórico"""
        
        # Buscar pedidos entregues dos últimos 30 dias
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)
        
        delivered_orders = db.query(Order).filter(
            Order.establishment_id == establishment_id,
            Order.status == OrderStatus.DELIVERED,
            Order.created_at >= thirty_days_ago,
            Order.delivered_at.isnot(None)
        ).all()
        
        if not delivered_orders:
            return {
                "average_prep_time": 30,
                "average_delivery_time": 45
            }
        
        # Calcular tempos médios
        prep_times = []
        delivery_times = []
        
        for order in delivered_orders:
            if order.confirmed_at:
                # Tempo de preparo (confirmação até pronto)
                if order.prepared_at:
                    prep_time = (order.prepared_at - order.confirmed_at).total_seconds() / 60
                    prep_times.append(prep_time)
                
                # Tempo total (confirmação até entrega)
                total_time = (order.delivered_at - order.confirmed_at).total_seconds() / 60
                if order.order_type == "delivery":
                    delivery_times.append(total_time)
        
        avg_prep_time = sum(prep_times) / len(prep_times) if prep_times else 30
        avg_delivery_time = sum(delivery_times) / len(delivery_times) if delivery_times else 45
        
        return {
            "average_prep_time": int(avg_prep_time),
            "average_delivery_time": int(avg_delivery_time)
        }
