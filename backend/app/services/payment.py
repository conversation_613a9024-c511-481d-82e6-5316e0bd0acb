"""
Serviço de pagamentos
"""
import uuid
import qrcode
import base64
from io import BytesIO
from datetime import datetime, timed<PERSON>ta
from typing import Optional
import mercadopago
import stripe

from app.core.config import settings
from app.models.order import Order, PaymentMethod, PaymentStatus
from app.schemas.order import PaymentRequest, PaymentResponse


class PaymentService:
    """Serviço para processamento de pagamentos"""
    
    def __init__(self):
        self.provider_name = "mercadopago"  # ou "stripe"
        
        # Configurar Mercado Pago
        if settings.MERCADOPAGO_ACCESS_TOKEN:
            self.mp = mercadopago.SDK(settings.MERCADOPAGO_ACCESS_TOKEN)
        
        # Configurar Stripe
        if settings.STRIPE_SECRET_KEY:
            stripe.api_key = settings.STRIPE_SECRET_KEY
    
    async def process_payment(
        self,
        order: Order,
        payment_request: PaymentRequest
    ) -> PaymentResponse:
        """Processar pagamento baseado no método escolhido"""
        
        if payment_request.payment_method == PaymentMethod.PIX:
            return await self._process_pix_payment(order, payment_request)
        elif payment_request.payment_method in [PaymentMethod.CREDIT_CARD, PaymentMethod.DEBIT_CARD]:
            return await self._process_card_payment(order, payment_request)
        else:
            raise ValueError(f"Método de pagamento não suportado: {payment_request.payment_method}")
    
    async def _process_pix_payment(
        self,
        order: Order,
        payment_request: PaymentRequest
    ) -> PaymentResponse:
        """Processar pagamento PIX"""
        
        transaction_id = str(uuid.uuid4())
        
        if self.provider_name == "mercadopago" and hasattr(self, 'mp'):
            return await self._process_mercadopago_pix(order, payment_request, transaction_id)
        else:
            # Implementação mock para desenvolvimento
            return await self._process_mock_pix(order, payment_request, transaction_id)
    
    async def _process_mercadopago_pix(
        self,
        order: Order,
        payment_request: PaymentRequest,
        transaction_id: str
    ) -> PaymentResponse:
        """Processar PIX via Mercado Pago"""
        
        try:
            # Criar preferência de pagamento
            preference_data = {
                "items": [
                    {
                        "title": f"Pedido #{order.order_number}",
                        "quantity": 1,
                        "unit_price": float(order.total_amount),
                        "currency_id": "BRL"
                    }
                ],
                "payment_methods": {
                    "excluded_payment_types": [
                        {"id": "credit_card"},
                        {"id": "debit_card"},
                        {"id": "ticket"}
                    ]
                },
                "external_reference": order.id,
                "notification_url": f"{settings.API_URL}/api/v1/checkout/webhook/payment",
                "expires": True,
                "expiration_date_from": datetime.utcnow().isoformat(),
                "expiration_date_to": (datetime.utcnow() + timedelta(minutes=payment_request.pix_expiration_minutes)).isoformat()
            }
            
            preference_response = self.mp.preference().create(preference_data)
            
            if preference_response["status"] == 201:
                preference = preference_response["response"]
                
                # Gerar QR Code
                qr_code_data = preference["point_of_interaction"]["transaction_data"]["qr_code"]
                qr_code_base64 = self._generate_qr_code_base64(qr_code_data)
                
                return PaymentResponse(
                    transaction_id=transaction_id,
                    status=PaymentStatus.PENDING,
                    payment_method=PaymentMethod.PIX,
                    amount=order.total_amount,
                    pix_qr_code=qr_code_data,
                    pix_qr_code_base64=qr_code_base64,
                    pix_expiration=datetime.utcnow() + timedelta(minutes=payment_request.pix_expiration_minutes),
                    payment_url=preference["init_point"]
                )
            else:
                raise Exception(f"Erro ao criar preferência: {preference_response}")
                
        except Exception as e:
            raise Exception(f"Erro no processamento PIX: {str(e)}")
    
    async def _process_mock_pix(
        self,
        order: Order,
        payment_request: PaymentRequest,
        transaction_id: str
    ) -> PaymentResponse:
        """Implementação mock do PIX para desenvolvimento"""
        
        # Gerar código PIX mock
        pix_code = f"00020126580014BR.GOV.BCB.PIX0136{transaction_id}5204000053039865802BR5925{order.establishment.name[:25]}6009SAO PAULO62070503***6304"
        
        # Gerar QR Code
        qr_code_base64 = self._generate_qr_code_base64(pix_code)
        
        return PaymentResponse(
            transaction_id=transaction_id,
            status=PaymentStatus.PENDING,
            payment_method=PaymentMethod.PIX,
            amount=order.total_amount,
            pix_qr_code=pix_code,
            pix_qr_code_base64=qr_code_base64,
            pix_expiration=datetime.utcnow() + timedelta(minutes=payment_request.pix_expiration_minutes)
        )
    
    async def _process_card_payment(
        self,
        order: Order,
        payment_request: PaymentRequest
    ) -> PaymentResponse:
        """Processar pagamento com cartão"""
        
        transaction_id = str(uuid.uuid4())
        
        if self.provider_name == "stripe" and hasattr(self, 'stripe'):
            return await self._process_stripe_card(order, payment_request, transaction_id)
        elif self.provider_name == "mercadopago" and hasattr(self, 'mp'):
            return await self._process_mercadopago_card(order, payment_request, transaction_id)
        else:
            # Implementação mock para desenvolvimento
            return await self._process_mock_card(order, payment_request, transaction_id)
    
    async def _process_stripe_card(
        self,
        order: Order,
        payment_request: PaymentRequest,
        transaction_id: str
    ) -> PaymentResponse:
        """Processar cartão via Stripe"""
        
        try:
            # Criar Payment Intent
            intent = stripe.PaymentIntent.create(
                amount=int(order.total_amount * 100),  # Stripe usa centavos
                currency='brl',
                payment_method_types=['card'],
                metadata={
                    'order_id': order.id,
                    'order_number': order.order_number
                }
            )
            
            return PaymentResponse(
                transaction_id=intent.id,
                status=PaymentStatus.PENDING,
                payment_method=payment_request.payment_method,
                amount=order.total_amount,
                payment_url=f"https://checkout.stripe.com/pay/{intent.client_secret}"
            )
            
        except Exception as e:
            raise Exception(f"Erro no processamento Stripe: {str(e)}")
    
    async def _process_mock_card(
        self,
        order: Order,
        payment_request: PaymentRequest,
        transaction_id: str
    ) -> PaymentResponse:
        """Implementação mock do cartão para desenvolvimento"""
        
        return PaymentResponse(
            transaction_id=transaction_id,
            status=PaymentStatus.PENDING,
            payment_method=payment_request.payment_method,
            amount=order.total_amount,
            card_last_four="1234",
            card_brand="visa",
            payment_url=f"{settings.FRONTEND_URL}/payment/{transaction_id}"
        )
    
    def _generate_qr_code_base64(self, data: str) -> str:
        """Gerar QR Code em base64"""
        
        qr = qrcode.QRCode(
            version=1,
            error_correction=qrcode.constants.ERROR_CORRECT_L,
            box_size=10,
            border=4,
        )
        qr.add_data(data)
        qr.make(fit=True)
        
        img = qr.make_image(fill_color="black", back_color="white")
        
        # Converter para base64
        buffer = BytesIO()
        img.save(buffer, format='PNG')
        img_str = base64.b64encode(buffer.getvalue()).decode()
        
        return f"data:image/png;base64,{img_str}"
    
    async def verify_payment(self, transaction_id: str) -> PaymentStatus:
        """Verificar status do pagamento"""
        
        if self.provider_name == "mercadopago" and hasattr(self, 'mp'):
            try:
                payment = self.mp.payment().get(transaction_id)
                if payment["status"] == 200:
                    status = payment["response"]["status"]
                    if status == "approved":
                        return PaymentStatus.COMPLETED
                    elif status == "rejected":
                        return PaymentStatus.FAILED
                    else:
                        return PaymentStatus.PROCESSING
            except:
                pass
        
        elif self.provider_name == "stripe":
            try:
                intent = stripe.PaymentIntent.retrieve(transaction_id)
                if intent.status == "succeeded":
                    return PaymentStatus.COMPLETED
                elif intent.status == "payment_failed":
                    return PaymentStatus.FAILED
                else:
                    return PaymentStatus.PROCESSING
            except:
                pass
        
        return PaymentStatus.PENDING
    
    async def refund_payment(self, transaction_id: str, amount: Optional[float] = None) -> bool:
        """Estornar pagamento"""
        
        try:
            if self.provider_name == "mercadopago" and hasattr(self, 'mp'):
                refund_data = {}
                if amount:
                    refund_data["amount"] = amount
                
                refund = self.mp.refund().create(transaction_id, refund_data)
                return refund["status"] == 201
            
            elif self.provider_name == "stripe":
                refund_data = {"payment_intent": transaction_id}
                if amount:
                    refund_data["amount"] = int(amount * 100)
                
                refund = stripe.Refund.create(**refund_data)
                return refund.status == "succeeded"
            
        except Exception as e:
            print(f"Erro no estorno: {str(e)}")
            return False
        
        return False
