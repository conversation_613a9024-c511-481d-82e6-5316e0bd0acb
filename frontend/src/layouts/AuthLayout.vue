<template>
  <div class="min-h-screen bg-gray-50 flex flex-col justify-center py-12 sm:px-6 lg:px-8">
    <!-- Header -->
    <div class="sm:mx-auto sm:w-full sm:max-w-md">
      <router-link to="/" class="flex justify-center">
        <div class="flex items-center space-x-2">
          <div class="w-10 h-10 bg-primary-600 rounded-lg flex items-center justify-center">
            <svg class="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
            </svg>
          </div>
          <span class="text-2xl font-bold text-gray-900">Cardápio</span>
        </div>
      </router-link>
      
      <h2 class="mt-6 text-center text-3xl font-extrabold text-gray-900">
        {{ pageTitle }}
      </h2>
      
      <p v-if="pageSubtitle" class="mt-2 text-center text-sm text-gray-600">
        {{ pageSubtitle }}
      </p>
    </div>

    <!-- Conteúdo principal -->
    <div class="mt-8 sm:mx-auto sm:w-full sm:max-w-md">
      <div class="bg-white py-8 px-4 shadow-soft sm:rounded-lg sm:px-10">
        <router-view />
      </div>
      
      <!-- Links de navegação -->
      <div class="mt-6">
        <div class="relative">
          <div class="absolute inset-0 flex items-center">
            <div class="w-full border-t border-gray-300" />
          </div>
          <div class="relative flex justify-center text-sm">
            <span class="px-2 bg-gray-50 text-gray-500">
              {{ linkText }}
            </span>
          </div>
        </div>

        <div class="mt-6 text-center">
          <router-link 
            :to="linkRoute" 
            class="font-medium text-primary-600 hover:text-primary-500 transition-colors duration-200"
          >
            {{ linkLabel }}
          </router-link>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <footer class="mt-8 text-center text-sm text-gray-500">
      <p>&copy; 2024 Cardápio Digital. Todos os direitos reservados.</p>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { useRoute } from 'vue-router'

const route = useRoute()

const pageTitle = computed(() => {
  switch (route.name) {
    case 'login':
      return 'Entre na sua conta'
    case 'register':
      return 'Crie sua conta'
    case 'forgot-password':
      return 'Recuperar senha'
    default:
      return 'Autenticação'
  }
})

const pageSubtitle = computed(() => {
  switch (route.name) {
    case 'login':
      return 'Acesse o painel administrativo do seu estabelecimento'
    case 'register':
      return 'Comece a gerenciar seu cardápio digital hoje mesmo'
    case 'forgot-password':
      return 'Digite seu email para receber as instruções'
    default:
      return null
  }
})

const linkText = computed(() => {
  switch (route.name) {
    case 'login':
      return 'Não tem uma conta?'
    case 'register':
      return 'Já tem uma conta?'
    case 'forgot-password':
      return 'Lembrou da senha?'
    default:
      return ''
  }
})

const linkLabel = computed(() => {
  switch (route.name) {
    case 'login':
      return 'Cadastre-se gratuitamente'
    case 'register':
      return 'Faça login'
    case 'forgot-password':
      return 'Voltar ao login'
    default:
      return ''
  }
})

const linkRoute = computed(() => {
  switch (route.name) {
    case 'login':
      return { name: 'register' }
    case 'register':
      return { name: 'login' }
    case 'forgot-password':
      return { name: 'login' }
    default:
      return { name: 'login' }
  }
})
</script>
