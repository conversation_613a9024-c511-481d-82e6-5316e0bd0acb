<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden hover:shadow-md transition-shadow duration-200">
    <!-- Imagem do item -->
    <div class="relative">
      <img
        v-if="item.image_url"
        :src="item.image_url"
        :alt="item.name"
        class="w-full h-48 object-cover"
        @click="openModal"
      />
      <div
        v-else
        class="w-full h-48 bg-gray-100 flex items-center justify-center cursor-pointer"
        @click="openModal"
      >
        <PhotoIcon class="h-12 w-12 text-gray-400" />
      </div>

      <!-- Badges -->
      <div class="absolute top-2 left-2 flex flex-wrap gap-1">
        <span
          v-if="item.is_featured"
          class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800"
        >
          <StarIcon class="h-3 w-3 mr-1" />
          Destaque
        </span>
        
        <span
          v-if="item.is_vegetarian"
          class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800"
        >
          <LeafIcon class="h-3 w-3 mr-1" />
          Vegetariano
        </span>
        
        <span
          v-if="item.is_vegan"
          class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 text-green-800"
        >
          <HeartIcon class="h-3 w-3 mr-1" />
          Vegano
        </span>
        
        <span
          v-if="item.is_spicy"
          class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-red-100 text-red-800"
        >
          <FireIcon class="h-3 w-3 mr-1" />
          Picante
        </span>
      </div>

      <!-- Estoque baixo -->
      <div
        v-if="isLowStock"
        class="absolute top-2 right-2"
      >
        <span class="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
          <ExclamationTriangleIcon class="h-3 w-3 mr-1" />
          Últimas unidades
        </span>
      </div>
    </div>

    <!-- Conteúdo -->
    <div class="p-4">
      <!-- Nome e descrição -->
      <div class="mb-3">
        <h3 
          class="text-lg font-medium text-gray-900 cursor-pointer hover:text-primary-600 transition-colors duration-200"
          @click="openModal"
        >
          {{ item.name }}
        </h3>
        
        <p 
          v-if="item.description" 
          class="text-sm text-gray-600 mt-1 line-clamp-2"
        >
          {{ item.description }}
        </p>
      </div>

      <!-- Informações nutricionais -->
      <div v-if="hasNutritionalInfo" class="flex flex-wrap gap-2 mb-3">
        <span
          v-if="item.is_gluten_free"
          class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-blue-100 text-blue-800"
        >
          <ShieldCheckIcon class="h-3 w-3 mr-1" />
          Sem Glúten
        </span>
        
        <span
          v-if="item.calories"
          class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-gray-100 text-gray-800"
        >
          {{ item.calories }} cal
        </span>
      </div>

      <!-- Preço e ações -->
      <div class="flex items-center justify-between">
        <div class="flex items-center space-x-2">
          <span class="text-2xl font-bold text-gray-900">
            {{ formatCurrency(item.price) }}
          </span>
          
          <span
            v-if="item.original_price && item.original_price > item.price"
            class="text-sm text-gray-500 line-through"
          >
            {{ formatCurrency(item.original_price) }}
          </span>
        </div>

        <!-- Controles de quantidade -->
        <div class="flex items-center space-x-2">
          <!-- Botão de diminuir -->
          <button
            v-if="cartQuantity > 0"
            @click="decreaseQuantity"
            class="w-8 h-8 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors duration-200"
            :disabled="isUpdating"
          >
            <MinusIcon class="h-4 w-4 text-gray-600" />
          </button>

          <!-- Quantidade atual -->
          <span
            v-if="cartQuantity > 0"
            class="min-w-[2rem] text-center font-medium text-gray-900"
          >
            {{ cartQuantity }}
          </span>

          <!-- Botão de adicionar/aumentar -->
          <button
            @click="increaseQuantity"
            :disabled="!item.is_available || isOutOfStock || isUpdating"
            :class="[
              'w-8 h-8 rounded-full flex items-center justify-center transition-colors duration-200',
              item.is_available && !isOutOfStock
                ? 'bg-primary-600 hover:bg-primary-700 text-white'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            ]"
          >
            <div v-if="isUpdating" class="spinner w-4 h-4"></div>
            <PlusIcon v-else class="h-4 w-4" />
          </button>
        </div>
      </div>

      <!-- Status de disponibilidade -->
      <div v-if="!item.is_available || isOutOfStock" class="mt-2">
        <span class="text-sm text-red-600 font-medium">
          {{ !item.is_available ? 'Indisponível' : 'Fora de estoque' }}
        </span>
      </div>

      <!-- Botão de ver detalhes -->
      <button
        @click="openModal"
        class="w-full mt-3 text-sm text-primary-600 hover:text-primary-700 font-medium transition-colors duration-200"
      >
        Ver detalhes
      </button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  PhotoIcon,
  StarIcon,
  LeafIcon,
  HeartIcon,
  FireIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon,
  PlusIcon,
  MinusIcon
} from '@heroicons/vue/24/outline'
import { useToast } from 'vue-toastification'

import { useCartStore } from '@/stores/cart'
import type { MenuItem } from '@/types'

interface Props {
  item: MenuItem
}

interface Emits {
  (e: 'add-to-cart', item: MenuItem, quantity: number): void
  (e: 'open-modal', item: MenuItem): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const toast = useToast()
const cartStore = useCartStore()

// Estado local
const isUpdating = ref(false)

// Computed
const cartQuantity = computed(() => cartStore.getItemQuantity(props.item.id))

const isOutOfStock = computed(() => {
  return props.item.stock_quantity !== null && props.item.stock_quantity <= 0
})

const isLowStock = computed(() => {
  return props.item.stock_quantity !== null && 
         props.item.stock_quantity > 0 && 
         props.item.stock_quantity <= 5
})

const hasNutritionalInfo = computed(() => {
  return props.item.is_gluten_free || props.item.calories
})

// Methods
const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value)
}

const increaseQuantity = async () => {
  if (!props.item.is_available || isOutOfStock.value || isUpdating.value) {
    return
  }

  // Verificar estoque
  if (props.item.stock_quantity !== null) {
    const newQuantity = cartQuantity.value + 1
    if (newQuantity > props.item.stock_quantity) {
      toast.error('Quantidade não disponível em estoque')
      return
    }
  }

  isUpdating.value = true

  try {
    emit('add-to-cart', props.item, 1)
  } catch (error: any) {
    toast.error(error.message || 'Erro ao adicionar item')
  } finally {
    isUpdating.value = false
  }
}

const decreaseQuantity = async () => {
  if (cartQuantity.value <= 0 || isUpdating.value) {
    return
  }

  isUpdating.value = true

  try {
    const cartItem = cartStore.items.find(item => item.menu_item_id === props.item.id)
    if (cartItem) {
      const newQuantity = cartItem.quantity - 1
      
      if (newQuantity <= 0) {
        await cartStore.removeItem(cartStore.cart!.establishment_id, cartItem.id)
        toast.success('Item removido do carrinho')
      } else {
        await cartStore.updateItem(
          cartStore.cart!.establishment_id,
          cartItem.id,
          newQuantity,
          cartItem.notes
        )
      }
    }
  } catch (error: any) {
    toast.error(error.message || 'Erro ao atualizar item')
  } finally {
    isUpdating.value = false
  }
}

const openModal = () => {
  emit('open-modal', props.item)
}
</script>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.spinner {
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
