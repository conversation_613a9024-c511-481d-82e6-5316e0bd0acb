<template>
  <TransitionRoot as="template" :show="true">
    <Dialog as="div" class="relative z-50" @close="$emit('close')">
      <TransitionChild
        as="template"
        enter="ease-out duration-300"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="ease-in duration-200"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
      </TransitionChild>

      <div class="fixed inset-0 z-10 overflow-y-auto">
        <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <TransitionChild
            as="template"
            enter="ease-out duration-300"
            enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            enter-to="opacity-100 translate-y-0 sm:scale-100"
            leave="ease-in duration-200"
            leave-from="opacity-100 translate-y-0 sm:scale-100"
            leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
          >
            <DialogPanel class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-2xl sm:p-6">
              <!-- Header -->
              <div class="flex items-center justify-between mb-6">
                <div class="flex items-center space-x-3">
                  <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900">
                    Pedido #{{ order.order_number }}
                  </DialogTitle>
                  <span :class="[
                    'badge',
                    getStatusBadgeClass(order.status)
                  ]">
                    {{ getStatusLabel(order.status) }}
                  </span>
                </div>
                
                <button
                  @click="$emit('close')"
                  class="rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <XMarkIcon class="h-6 w-6" />
                </button>
              </div>

              <!-- Informações do cliente -->
              <div class="bg-gray-50 rounded-lg p-4 mb-6">
                <h4 class="text-sm font-medium text-gray-900 mb-3">Informações do Cliente</h4>
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <p class="text-sm text-gray-500">Nome</p>
                    <p class="font-medium text-gray-900">{{ order.customer_name }}</p>
                  </div>
                  <div>
                    <p class="text-sm text-gray-500">Telefone</p>
                    <p class="font-medium text-gray-900">{{ order.customer_phone }}</p>
                  </div>
                  <div v-if="order.customer_email" class="md:col-span-2">
                    <p class="text-sm text-gray-500">Email</p>
                    <p class="font-medium text-gray-900">{{ order.customer_email }}</p>
                  </div>
                </div>
              </div>

              <!-- Endereço de entrega -->
              <div v-if="order.order_type === 'delivery'" class="bg-blue-50 rounded-lg p-4 mb-6">
                <h4 class="text-sm font-medium text-gray-900 mb-3 flex items-center">
                  <MapPinIcon class="h-4 w-4 mr-2" />
                  Endereço de Entrega
                </h4>
                <div class="text-sm text-gray-700">
                  <p>{{ order.delivery_address }}</p>
                  <p v-if="order.delivery_city">
                    {{ order.delivery_city }}, {{ order.delivery_state }} - {{ order.delivery_zip_code }}
                  </p>
                </div>
              </div>

              <!-- Itens do pedido -->
              <div class="mb-6">
                <h4 class="text-sm font-medium text-gray-900 mb-3">Itens do Pedido</h4>
                <div class="space-y-3">
                  <div
                    v-for="item in order.items"
                    :key="item.id"
                    class="flex items-center justify-between p-3 bg-gray-50 rounded-lg"
                  >
                    <div class="flex-1">
                      <h5 class="font-medium text-gray-900">{{ item.item_name }}</h5>
                      <p v-if="item.item_description" class="text-sm text-gray-500 mt-1">
                        {{ item.item_description }}
                      </p>
                      <p v-if="item.notes" class="text-sm text-primary-600 mt-1">
                        Obs: {{ item.notes }}
                      </p>
                    </div>
                    <div class="text-right ml-4">
                      <p class="font-medium text-gray-900">
                        {{ item.quantity }}x {{ formatCurrency(item.unit_price) }}
                      </p>
                      <p class="text-sm text-gray-500">
                        {{ formatCurrency(item.total_price) }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Resumo financeiro -->
              <div class="bg-gray-50 rounded-lg p-4 mb-6">
                <h4 class="text-sm font-medium text-gray-900 mb-3">Resumo do Pedido</h4>
                <div class="space-y-2">
                  <div class="flex justify-between text-sm">
                    <span class="text-gray-500">Subtotal</span>
                    <span class="text-gray-900">{{ formatCurrency(order.subtotal) }}</span>
                  </div>
                  
                  <div v-if="order.delivery_fee > 0" class="flex justify-between text-sm">
                    <span class="text-gray-500">Taxa de entrega</span>
                    <span class="text-gray-900">{{ formatCurrency(order.delivery_fee) }}</span>
                  </div>
                  
                  <div v-if="order.service_fee > 0" class="flex justify-between text-sm">
                    <span class="text-gray-500">Taxa de serviço</span>
                    <span class="text-gray-900">{{ formatCurrency(order.service_fee) }}</span>
                  </div>
                  
                  <div v-if="order.discount_amount > 0" class="flex justify-between text-sm">
                    <span class="text-gray-500">Desconto</span>
                    <span class="text-success-600">-{{ formatCurrency(order.discount_amount) }}</span>
                  </div>
                  
                  <div class="border-t border-gray-200 pt-2 mt-2">
                    <div class="flex justify-between">
                      <span class="font-medium text-gray-900">Total</span>
                      <span class="font-bold text-lg text-gray-900">{{ formatCurrency(order.total_amount) }}</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Observações -->
              <div v-if="order.notes" class="bg-yellow-50 rounded-lg p-4 mb-6">
                <h4 class="text-sm font-medium text-gray-900 mb-2 flex items-center">
                  <ChatBubbleLeftIcon class="h-4 w-4 mr-2" />
                  Observações
                </h4>
                <p class="text-sm text-gray-700">{{ order.notes }}</p>
              </div>

              <!-- Timeline do pedido -->
              <div class="mb-6">
                <h4 class="text-sm font-medium text-gray-900 mb-3">Status do Pedido</h4>
                <div class="flow-root">
                  <ul class="-mb-8">
                    <li v-for="(status, index) in orderTimeline" :key="status.status" class="relative">
                      <div v-if="index !== orderTimeline.length - 1" class="absolute top-4 left-4 -ml-px h-full w-0.5 bg-gray-200"></div>
                      <div class="relative flex space-x-3">
                        <div>
                          <span :class="[
                            'h-8 w-8 rounded-full flex items-center justify-center ring-8 ring-white',
                            status.completed ? 'bg-success-500' : status.current ? 'bg-primary-500' : 'bg-gray-300'
                          ]">
                            <CheckIcon v-if="status.completed" class="h-5 w-5 text-white" />
                            <ClockIcon v-else-if="status.current" class="h-5 w-5 text-white" />
                            <div v-else class="h-2 w-2 bg-white rounded-full"></div>
                          </span>
                        </div>
                        <div class="min-w-0 flex-1 pt-1.5 flex justify-between space-x-4">
                          <div>
                            <p :class="[
                              'text-sm',
                              status.completed || status.current ? 'text-gray-900 font-medium' : 'text-gray-500'
                            ]">
                              {{ status.label }}
                            </p>
                          </div>
                          <div class="text-right text-sm whitespace-nowrap text-gray-500">
                            <time v-if="status.timestamp">{{ formatTime(status.timestamp) }}</time>
                          </div>
                        </div>
                      </div>
                    </li>
                  </ul>
                </div>
              </div>

              <!-- Ações -->
              <div class="flex justify-between pt-6 border-t border-gray-200">
                <div class="flex space-x-3">
                  <button
                    v-if="canCancelOrder(order.status)"
                    @click="confirmCancel"
                    class="btn btn-error"
                    :disabled="isUpdating"
                  >
                    Cancelar Pedido
                  </button>
                </div>
                
                <div class="flex space-x-3">
                  <button
                    @click="$emit('close')"
                    class="btn btn-outline"
                    :disabled="isUpdating"
                  >
                    Fechar
                  </button>
                  
                  <button
                    v-if="canAdvanceStatus(order.status)"
                    @click="advanceStatus"
                    :class="[
                      'btn',
                      getNextActionClass(order.status)
                    ]"
                    :disabled="isUpdating"
                  >
                    <div v-if="isUpdating" class="flex items-center">
                      <div class="spinner w-4 h-4 mr-2"></div>
                      Atualizando...
                    </div>
                    <span v-else>
                      {{ getNextActionLabel(order.status) }}
                    </span>
                  </button>
                </div>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue'
import {
  XMarkIcon,
  MapPinIcon,
  ChatBubbleLeftIcon,
  CheckIcon,
  ClockIcon
} from '@heroicons/vue/24/outline'
import { useToast } from 'vue-toastification'

import type { Order } from '@/types'

interface Props {
  order: Order
}

interface Emits {
  (e: 'close'): void
  (e: 'status-updated'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const toast = useToast()
const isUpdating = ref(false)

// Computed
const orderTimeline = computed(() => {
  const statuses = [
    { status: 'pending', label: 'Pedido recebido', completed: false, current: false, timestamp: props.order.created_at },
    { status: 'confirmed', label: 'Pedido confirmado', completed: false, current: false, timestamp: null },
    { status: 'preparing', label: 'Preparando', completed: false, current: false, timestamp: null },
    { status: 'ready', label: 'Pronto', completed: false, current: false, timestamp: null },
  ]

  // Adicionar status específico baseado no tipo de pedido
  if (props.order.order_type === 'delivery') {
    statuses.push(
      { status: 'out_for_delivery', label: 'Saiu para entrega', completed: false, current: false, timestamp: null },
      { status: 'delivered', label: 'Entregue', completed: false, current: false, timestamp: null }
    )
  } else if (props.order.order_type === 'pickup') {
    statuses.push(
      { status: 'delivered', label: 'Retirado', completed: false, current: false, timestamp: null }
    )
  } else {
    statuses.push(
      { status: 'delivered', label: 'Servido', completed: false, current: false, timestamp: null }
    )
  }

  // Marcar status como completados ou atual
  const currentStatusIndex = statuses.findIndex(s => s.status === props.order.status)
  
  statuses.forEach((status, index) => {
    if (index < currentStatusIndex) {
      status.completed = true
    } else if (index === currentStatusIndex) {
      status.current = true
    }
  })

  return statuses
})

// Métodos
const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value)
}

const formatTime = (dateString: string): string => {
  return new Intl.DateTimeFormat('pt-BR', {
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(dateString))
}

const getStatusBadgeClass = (status: string): string => {
  switch (status) {
    case 'pending':
      return 'badge-warning'
    case 'confirmed':
    case 'preparing':
      return 'badge-primary'
    case 'ready':
    case 'out_for_delivery':
      return 'badge-success'
    case 'delivered':
      return 'badge-secondary'
    case 'cancelled':
      return 'badge-error'
    default:
      return 'badge-secondary'
  }
}

const getStatusLabel = (status: string): string => {
  const labels = {
    pending: 'Pendente',
    confirmed: 'Confirmado',
    preparing: 'Preparando',
    ready: 'Pronto',
    out_for_delivery: 'Saindo',
    delivered: 'Entregue',
    cancelled: 'Cancelado'
  }
  return labels[status as keyof typeof labels] || status
}

const canAdvanceStatus = (status: string): boolean => {
  return ['pending', 'confirmed', 'preparing', 'ready'].includes(status)
}

const canCancelOrder = (status: string): boolean => {
  return ['pending', 'confirmed', 'preparing'].includes(status)
}

const getNextActionClass = (status: string): string => {
  switch (status) {
    case 'pending':
      return 'btn-success'
    case 'confirmed':
    case 'preparing':
      return 'btn-primary'
    case 'ready':
      return 'btn-warning'
    default:
      return 'btn-primary'
  }
}

const getNextActionLabel = (status: string): string => {
  switch (status) {
    case 'pending':
      return 'Confirmar Pedido'
    case 'confirmed':
      return 'Iniciar Preparo'
    case 'preparing':
      return 'Marcar como Pronto'
    case 'ready':
      return props.order.order_type === 'delivery' ? 'Saiu para Entrega' : 'Marcar como Entregue'
    default:
      return 'Avançar Status'
  }
}

const advanceStatus = async () => {
  isUpdating.value = true
  
  try {
    // Aqui seria feita a chamada para a API
    // await orderStore.updateOrderStatus(props.order.id, nextStatus)
    
    toast.success('Status do pedido atualizado!')
    emit('status-updated')
  } catch (error) {
    console.error('Erro ao atualizar status:', error)
    toast.error('Erro ao atualizar status do pedido')
  } finally {
    isUpdating.value = false
  }
}

const confirmCancel = () => {
  // Aqui poderia abrir um modal de confirmação ou fazer diretamente
  if (confirm('Tem certeza que deseja cancelar este pedido?')) {
    cancelOrder()
  }
}

const cancelOrder = async () => {
  isUpdating.value = true
  
  try {
    // Aqui seria feita a chamada para a API
    // await orderStore.cancelOrder(props.order.id)
    
    toast.success('Pedido cancelado com sucesso!')
    emit('status-updated')
  } catch (error) {
    console.error('Erro ao cancelar pedido:', error)
    toast.error('Erro ao cancelar pedido')
  } finally {
    isUpdating.value = false
  }
}
</script>
