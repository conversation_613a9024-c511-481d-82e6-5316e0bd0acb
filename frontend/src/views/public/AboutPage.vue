<template>
  <div class="bg-white">
    <!-- Hero Section -->
    <div class="relative bg-gray-50 py-16 sm:py-24">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="text-center">
          <h1 class="text-4xl font-bold tracking-tight text-gray-900 sm:text-5xl md:text-6xl">
            Sobre o Cardápio Digital
          </h1>
          <p class="mx-auto mt-3 max-w-md text-base text-gray-500 sm:text-lg md:mt-5 md:max-w-3xl md:text-xl">
            Transformando a experiência gastronômica através da tecnologia
          </p>
        </div>
      </div>
    </div>

    <!-- <PERSON><PERSON> História -->
    <div class="py-16 bg-white">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="lg:text-center">
          <h2 class="text-base text-primary-600 font-semibold tracking-wide uppercase"><PERSON>ssa História</h2>
          <p class="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
            Como tudo começou
          </p>
        </div>

        <div class="mt-10">
          <div class="space-y-10 md:space-y-0 md:grid md:grid-cols-2 md:gap-x-8 md:gap-y-10">
            <div>
              <h3 class="text-lg leading-6 font-medium text-gray-900">O Problema</h3>
              <p class="mt-2 text-base text-gray-500">
                Percebemos que muitos restaurantes ainda dependiam de cardápios físicos, 
                dificultando atualizações de preços, disponibilidade de itens e a experiência 
                do cliente. A pandemia acelerou a necessidade de soluções digitais.
              </p>
            </div>

            <div>
              <h3 class="text-lg leading-6 font-medium text-gray-900">Nossa Solução</h3>
              <p class="mt-2 text-base text-gray-500">
                Criamos uma plataforma completa que permite aos restaurantes digitalizar 
                seus cardápios, gerenciar pedidos online e oferecer uma experiência moderna 
                aos seus clientes, tudo de forma simples e acessível.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Missão, Visão e Valores -->
    <div class="py-16 bg-gray-50">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="lg:text-center mb-12">
          <h2 class="text-base text-primary-600 font-semibold tracking-wide uppercase">Nossos Princípios</h2>
          <p class="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
            Missão, Visão e Valores
          </p>
        </div>

        <div class="space-y-10 md:space-y-0 md:grid md:grid-cols-3 md:gap-x-8 md:gap-y-10">
          <!-- Missão -->
          <div class="text-center">
            <div class="flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white mx-auto">
              <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
            </div>
            <h3 class="mt-4 text-lg leading-6 font-medium text-gray-900">Missão</h3>
            <p class="mt-2 text-base text-gray-500">
              Democratizar a tecnologia para restaurantes de todos os tamanhos, 
              oferecendo ferramentas simples e poderosas para modernizar seus negócios.
            </p>
          </div>

          <!-- Visão -->
          <div class="text-center">
            <div class="flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white mx-auto">
              <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
            </div>
            <h3 class="mt-4 text-lg leading-6 font-medium text-gray-900">Visão</h3>
            <p class="mt-2 text-base text-gray-500">
              Ser a principal plataforma de cardápios digitais do Brasil, 
              conectando restaurantes e clientes através de experiências excepcionais.
            </p>
          </div>

          <!-- Valores -->
          <div class="text-center">
            <div class="flex items-center justify-center h-12 w-12 rounded-md bg-primary-500 text-white mx-auto">
              <svg class="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
              </svg>
            </div>
            <h3 class="mt-4 text-lg leading-6 font-medium text-gray-900">Valores</h3>
            <p class="mt-2 text-base text-gray-500">
              Simplicidade, inovação, transparência e foco no cliente. 
              Acreditamos que a tecnologia deve facilitar, não complicar.
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Estatísticas -->
    <div class="py-16 bg-white">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="lg:text-center mb-12">
          <h2 class="text-base text-primary-600 font-semibold tracking-wide uppercase">Nosso Impacto</h2>
          <p class="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
            Números que nos orgulham
          </p>
        </div>

        <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">
                      Restaurantes Ativos
                    </dt>
                    <dd class="text-lg font-medium text-gray-900">
                      500+
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
                  </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">
                      Clientes Atendidos
                    </dt>
                    <dd class="text-lg font-medium text-gray-900">
                      50.000+
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">
                      Pedidos Processados
                    </dt>
                    <dd class="text-lg font-medium text-gray-900">
                      1M+
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>

          <div class="bg-white overflow-hidden shadow rounded-lg">
            <div class="p-5">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <svg class="h-6 w-6 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                </div>
                <div class="ml-5 w-0 flex-1">
                  <dl>
                    <dt class="text-sm font-medium text-gray-500 truncate">
                      Crescimento Mensal
                    </dt>
                    <dd class="text-lg font-medium text-gray-900">
                      25%
                    </dd>
                  </dl>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Equipe -->
    <div class="py-16 bg-gray-50">
      <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div class="lg:text-center mb-12">
          <h2 class="text-base text-primary-600 font-semibold tracking-wide uppercase">Nossa Equipe</h2>
          <p class="mt-2 text-3xl leading-8 font-extrabold tracking-tight text-gray-900 sm:text-4xl">
            Pessoas apaixonadas por tecnologia e gastronomia
          </p>
        </div>

        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3">
          <!-- Membro da equipe 1 -->
          <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="h-16 w-16 rounded-full bg-primary-100 flex items-center justify-center">
                    <span class="text-xl font-bold text-primary-600">JD</span>
                  </div>
                </div>
                <div class="ml-4">
                  <h3 class="text-lg font-medium text-gray-900">João Silva</h3>
                  <p class="text-sm text-gray-500">CEO & Fundador</p>
                </div>
              </div>
              <p class="mt-4 text-sm text-gray-600">
                Ex-chef de cozinha que decidiu usar a tecnologia para revolucionar 
                a experiência gastronômica.
              </p>
            </div>
          </div>

          <!-- Membro da equipe 2 -->
          <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="h-16 w-16 rounded-full bg-primary-100 flex items-center justify-center">
                    <span class="text-xl font-bold text-primary-600">MS</span>
                  </div>
                </div>
                <div class="ml-4">
                  <h3 class="text-lg font-medium text-gray-900">Maria Santos</h3>
                  <p class="text-sm text-gray-500">CTO</p>
                </div>
              </div>
              <p class="mt-4 text-sm text-gray-600">
                Engenheira de software com mais de 10 anos de experiência 
                em desenvolvimento de plataformas escaláveis.
              </p>
            </div>
          </div>

          <!-- Membro da equipe 3 -->
          <div class="bg-white rounded-lg shadow-lg overflow-hidden">
            <div class="p-6">
              <div class="flex items-center">
                <div class="flex-shrink-0">
                  <div class="h-16 w-16 rounded-full bg-primary-100 flex items-center justify-center">
                    <span class="text-xl font-bold text-primary-600">PC</span>
                  </div>
                </div>
                <div class="ml-4">
                  <h3 class="text-lg font-medium text-gray-900">Pedro Costa</h3>
                  <p class="text-sm text-gray-500">Head de Produto</p>
                </div>
              </div>
              <p class="mt-4 text-sm text-gray-600">
                Designer de UX/UI especializado em criar experiências 
                intuitivas para o setor alimentício.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- CTA -->
    <div class="bg-primary-700">
      <div class="mx-auto max-w-2xl py-16 px-4 text-center sm:py-20 sm:px-6 lg:px-8">
        <h2 class="text-3xl font-bold tracking-tight text-white sm:text-4xl">
          <span class="block">Pronto para digitalizar</span>
          <span class="block">seu restaurante?</span>
        </h2>
        <p class="mt-4 text-lg leading-6 text-primary-200">
          Junte-se a centenas de restaurantes que já transformaram seus negócios.
        </p>
        <router-link
          to="/auth/register"
          class="mt-8 inline-flex items-center justify-center rounded-md border border-transparent bg-white px-5 py-3 text-base font-medium text-primary-600 hover:bg-primary-50 sm:w-auto"
        >
          Começar agora
        </router-link>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// Esta página é estática, então não precisa de lógica complexa
</script>
