"""
Gerenciador de WebSockets para tempo real
"""
import json
import asyncio
from typing import Dict, List, Set, Optional
from fastapi import WebSocket, WebSocketDisconnect
from datetime import datetime

from app.models.user import User
from app.models.order import Order


class ConnectionManager:
    """Gerenciador de conexões WebSocket"""
    
    def __init__(self):
        # Conexões ativas por estabelecimento
        self.establishment_connections: Dict[str, Set[WebSocket]] = {}
        
        # Conexões ativas por usuário
        self.user_connections: Dict[str, Set[WebSocket]] = {}
        
        # Conexões ativas por pedido (para tracking)
        self.order_connections: Dict[str, Set[WebSocket]] = {}
        
        # Metadados das conexões
        self.connection_metadata: Dict[WebSocket, dict] = {}
    
    async def connect(
        self,
        websocket: WebSocket,
        user: Optional[User] = None,
        establishment_id: Optional[str] = None,
        order_id: Optional[str] = None,
        connection_type: str = "general"
    ):
        """Aceitar nova conexão WebSocket"""
        
        await websocket.accept()
        
        # Armazenar metadados da conexão
        self.connection_metadata[websocket] = {
            "user_id": user.id if user else None,
            "establishment_id": establishment_id,
            "order_id": order_id,
            "connection_type": connection_type,
            "connected_at": datetime.utcnow()
        }
        
        # Adicionar às listas apropriadas
        if establishment_id:
            if establishment_id not in self.establishment_connections:
                self.establishment_connections[establishment_id] = set()
            self.establishment_connections[establishment_id].add(websocket)
        
        if user:
            if user.id not in self.user_connections:
                self.user_connections[user.id] = set()
            self.user_connections[user.id].add(websocket)
        
        if order_id:
            if order_id not in self.order_connections:
                self.order_connections[order_id] = set()
            self.order_connections[order_id].add(websocket)
        
        # Enviar mensagem de confirmação
        await self.send_personal_message(websocket, {
            "type": "connection_established",
            "message": "Conectado com sucesso",
            "timestamp": datetime.utcnow().isoformat()
        })
    
    def disconnect(self, websocket: WebSocket):
        """Remover conexão WebSocket"""
        
        metadata = self.connection_metadata.get(websocket, {})
        
        # Remover das listas
        establishment_id = metadata.get("establishment_id")
        if establishment_id and establishment_id in self.establishment_connections:
            self.establishment_connections[establishment_id].discard(websocket)
            if not self.establishment_connections[establishment_id]:
                del self.establishment_connections[establishment_id]
        
        user_id = metadata.get("user_id")
        if user_id and user_id in self.user_connections:
            self.user_connections[user_id].discard(websocket)
            if not self.user_connections[user_id]:
                del self.user_connections[user_id]
        
        order_id = metadata.get("order_id")
        if order_id and order_id in self.order_connections:
            self.order_connections[order_id].discard(websocket)
            if not self.order_connections[order_id]:
                del self.order_connections[order_id]
        
        # Remover metadados
        if websocket in self.connection_metadata:
            del self.connection_metadata[websocket]
    
    async def send_personal_message(self, websocket: WebSocket, message: dict):
        """Enviar mensagem para uma conexão específica"""
        
        try:
            await websocket.send_text(json.dumps(message))
        except:
            # Conexão foi fechada, remover
            self.disconnect(websocket)
    
    async def broadcast_to_establishment(self, establishment_id: str, message: dict):
        """Enviar mensagem para todas as conexões de um estabelecimento"""
        
        if establishment_id not in self.establishment_connections:
            return
        
        # Criar cópia da lista para evitar modificação durante iteração
        connections = list(self.establishment_connections[establishment_id])
        
        for websocket in connections:
            await self.send_personal_message(websocket, message)
    
    async def broadcast_to_user(self, user_id: str, message: dict):
        """Enviar mensagem para todas as conexões de um usuário"""
        
        if user_id not in self.user_connections:
            return
        
        connections = list(self.user_connections[user_id])
        
        for websocket in connections:
            await self.send_personal_message(websocket, message)
    
    async def broadcast_to_order(self, order_id: str, message: dict):
        """Enviar mensagem para todas as conexões acompanhando um pedido"""
        
        if order_id not in self.order_connections:
            return
        
        connections = list(self.order_connections[order_id])
        
        for websocket in connections:
            await self.send_personal_message(websocket, message)
    
    async def notify_new_order(self, order: Order):
        """Notificar sobre novo pedido"""
        
        message = {
            "type": "new_order",
            "data": {
                "order_id": order.id,
                "order_number": order.order_number,
                "customer_name": order.customer_name,
                "order_type": order.order_type.value,
                "total_amount": float(order.total_amount),
                "status": order.status.value,
                "created_at": order.created_at.isoformat()
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await self.broadcast_to_establishment(order.establishment_id, message)
    
    async def notify_order_status_change(self, order: Order, old_status: str):
        """Notificar sobre mudança de status do pedido"""
        
        message = {
            "type": "order_status_changed",
            "data": {
                "order_id": order.id,
                "order_number": order.order_number,
                "old_status": old_status,
                "new_status": order.status.value,
                "customer_name": order.customer_name,
                "updated_at": order.updated_at.isoformat()
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Notificar estabelecimento
        await self.broadcast_to_establishment(order.establishment_id, message)
        
        # Notificar cliente se estiver conectado
        if order.user_id:
            await self.broadcast_to_user(order.user_id, message)
        
        # Notificar quem está acompanhando o pedido
        await self.broadcast_to_order(order.id, message)
    
    async def notify_payment_status_change(self, order: Order, payment_status: str):
        """Notificar sobre mudança de status do pagamento"""
        
        message = {
            "type": "payment_status_changed",
            "data": {
                "order_id": order.id,
                "order_number": order.order_number,
                "payment_status": payment_status,
                "customer_name": order.customer_name
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Notificar estabelecimento
        await self.broadcast_to_establishment(order.establishment_id, message)
        
        # Notificar cliente
        if order.user_id:
            await self.broadcast_to_user(order.user_id, message)
    
    async def send_order_update(self, order: Order):
        """Enviar atualização completa do pedido"""
        
        message = {
            "type": "order_updated",
            "data": {
                "order_id": order.id,
                "order_number": order.order_number,
                "status": order.status.value,
                "customer_name": order.customer_name,
                "customer_phone": order.customer_phone,
                "order_type": order.order_type.value,
                "total_amount": float(order.total_amount),
                "estimated_prep_time": order.estimated_prep_time,
                "estimated_delivery_time": order.estimated_delivery_time,
                "created_at": order.created_at.isoformat(),
                "updated_at": order.updated_at.isoformat()
            },
            "timestamp": datetime.utcnow().isoformat()
        }
        
        # Notificar estabelecimento
        await self.broadcast_to_establishment(order.establishment_id, message)
        
        # Notificar quem está acompanhando o pedido
        await self.broadcast_to_order(order.id, message)
    
    async def send_establishment_stats(self, establishment_id: str, stats: dict):
        """Enviar estatísticas atualizadas do estabelecimento"""
        
        message = {
            "type": "establishment_stats",
            "data": stats,
            "timestamp": datetime.utcnow().isoformat()
        }
        
        await self.broadcast_to_establishment(establishment_id, message)
    
    def get_connection_count(self, establishment_id: str = None, user_id: str = None) -> int:
        """Obter número de conexões ativas"""
        
        if establishment_id:
            return len(self.establishment_connections.get(establishment_id, set()))
        elif user_id:
            return len(self.user_connections.get(user_id, set()))
        else:
            return sum(len(connections) for connections in self.establishment_connections.values())
    
    def get_active_establishments(self) -> List[str]:
        """Obter lista de estabelecimentos com conexões ativas"""
        
        return list(self.establishment_connections.keys())
    
    async def ping_all_connections(self):
        """Enviar ping para todas as conexões para manter vivas"""
        
        ping_message = {
            "type": "ping",
            "timestamp": datetime.utcnow().isoformat()
        }
        
        all_connections = set()
        
        # Coletar todas as conexões
        for connections in self.establishment_connections.values():
            all_connections.update(connections)
        
        for connections in self.user_connections.values():
            all_connections.update(connections)
        
        for connections in self.order_connections.values():
            all_connections.update(connections)
        
        # Enviar ping
        for websocket in all_connections:
            await self.send_personal_message(websocket, ping_message)


# Instância global do gerenciador
manager = ConnectionManager()


async def start_ping_task():
    """Iniciar task de ping periódico"""
    
    while True:
        await asyncio.sleep(30)  # Ping a cada 30 segundos
        await manager.ping_all_connections()
