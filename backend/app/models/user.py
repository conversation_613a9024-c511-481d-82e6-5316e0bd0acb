"""
Modelo de usuários (proprietários, funcionários, clientes)
"""
from sqlalchemy import Column, String, Text, Boolean, Enum, ForeignKey, Numeric, DateTime
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum
from app.models.base import BaseModel, TenantMixin


class UserRole(PyEnum):
    """Tipos de usuário no sistema"""
    OWNER = "owner"          # Proprietário do estabelecimento
    MANAGER = "manager"      # Gerente
    EMPLOYEE = "employee"    # Funcionário
    CUSTOMER = "customer"    # Cliente


class User(BaseModel, TenantMixin):
    """Modelo de usuário"""
    
    __tablename__ = "users"
    
    # Informações básicas
    email = Column(String(255), unique=True, nullable=False, index=True)
    password_hash = Column(String(255), nullable=False)
    full_name = Column(String(255), nullable=False)
    phone = Column(String(20))
    
    # Tipo de usuário
    role = Column(Enum(UserRole), nullable=False, default=UserRole.CUSTOMER)
    
    # Informações do cliente
    cpf = Column(String(14))  # Formato: 000.000.000-00
    birth_date = Column(DateTime)
    
    # Endereço do cliente
    address = Column(Text)
    city = Column(String(100))
    state = Column(String(50))
    zip_code = Column(String(20))
    address_complement = Column(String(255))
    address_number = Column(String(20))
    latitude = Column(Numeric(10, 8))
    longitude = Column(Numeric(11, 8))
    
    # Configurações de conta
    email_verified = Column(Boolean, default=False)
    phone_verified = Column(Boolean, default=False)
    is_blocked = Column(Boolean, default=False)
    
    # Configurações de notificação
    accepts_marketing = Column(Boolean, default=True)
    accepts_notifications = Column(Boolean, default=True)
    
    # Avatar
    avatar_url = Column(Text)
    
    # Programa de fidelidade
    loyalty_points = Column(Numeric(10, 2), default=0.00)
    total_orders = Column(Numeric(10, 0), default=0)
    total_spent = Column(Numeric(12, 2), default=0.00)
    
    # Relacionamentos
    establishment_id = Column(
        UUID(as_uuid=True),
        ForeignKey("establishments.id"),
        nullable=True,  # Null para clientes que não pertencem a um estabelecimento específico
        index=True
    )
    
    establishment = relationship("Establishment", back_populates="users")
    orders = relationship("Order", back_populates="customer")
    reviews = relationship("Review", back_populates="user")
    favorites = relationship("Favorite", back_populates="user")
    
    def __repr__(self):
        return f"<User(email='{self.email}', role='{self.role.value}')>"
    
    @property
    def is_staff(self) -> bool:
        """Verificar se o usuário é da equipe do estabelecimento"""
        return self.role in [UserRole.OWNER, UserRole.MANAGER, UserRole.EMPLOYEE]
    
    @property
    def is_customer(self) -> bool:
        """Verificar se o usuário é cliente"""
        return self.role == UserRole.CUSTOMER
    
    @property
    def can_manage_establishment(self) -> bool:
        """Verificar se pode gerenciar o estabelecimento"""
        return self.role in [UserRole.OWNER, UserRole.MANAGER]
    
    @property
    def can_manage_orders(self) -> bool:
        """Verificar se pode gerenciar pedidos"""
        return self.role in [UserRole.OWNER, UserRole.MANAGER, UserRole.EMPLOYEE]


class UserSession(BaseModel):
    """Modelo para sessões de usuário (refresh tokens)"""
    
    __tablename__ = "user_sessions"
    
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False,
        index=True
    )
    
    refresh_token = Column(String(500), nullable=False, unique=True)
    expires_at = Column(DateTime, nullable=False)
    device_info = Column(Text)  # JSON com informações do dispositivo
    ip_address = Column(String(45))  # IPv4 ou IPv6
    user_agent = Column(Text)
    
    # Relacionamentos
    user = relationship("User")
    
    def __repr__(self):
        return f"<UserSession(user_id='{self.user_id}')>"


class UserPreferences(BaseModel, TenantMixin):
    """Preferências do usuário"""
    
    __tablename__ = "user_preferences"
    
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False,
        unique=True,
        index=True
    )
    
    # Preferências de notificação
    email_notifications = Column(Boolean, default=True)
    push_notifications = Column(Boolean, default=True)
    sms_notifications = Column(Boolean, default=False)
    
    # Preferências de pedido
    default_address_id = Column(UUID(as_uuid=True))
    preferred_payment_method = Column(String(50))
    
    # Preferências alimentares
    dietary_restrictions = Column(Text)  # JSON array
    allergies = Column(Text)  # JSON array
    favorite_categories = Column(Text)  # JSON array
    
    # Relacionamentos
    user = relationship("User")
    
    def __repr__(self):
        return f"<UserPreferences(user_id='{self.user_id}')>"
