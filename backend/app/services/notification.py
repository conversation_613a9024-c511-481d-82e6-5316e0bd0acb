"""
Serviço de notificações
"""
import smtplib
import requests
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON>ultip<PERSON>
from typing import Optional, List
from datetime import datetime

from app.core.config import settings
from app.models.order import Order
from app.models.user import User
from app.database import get_db


class NotificationService:
    """Serviço para envio de notificações"""
    
    def __init__(self):
        self.smtp_server = settings.SMTP_SERVER
        self.smtp_port = settings.SMTP_PORT
        self.smtp_username = settings.SMTP_USERNAME
        self.smtp_password = settings.SMTP_PASSWORD
        self.from_email = settings.FROM_EMAIL
        
        # Configurações para push notifications (Firebase, OneSignal, etc.)
        self.push_api_key = settings.PUSH_API_KEY
        self.push_app_id = settings.PUSH_APP_ID
    
    async def send_order_confirmation(self, order_id: str):
        """Enviar confirmação de pedido"""
        
        db = next(get_db())
        order = db.query(Order).filter(Order.id == order_id).first()
        
        if not order:
            return
        
        # Enviar email para o cliente
        if order.customer_email:
            await self._send_order_confirmation_email(order)
        
        # Enviar notificação para o estabelecimento
        await self._send_order_notification_to_establishment(order)
        
        # Enviar SMS se configurado
        if settings.SMS_ENABLED and order.customer_phone:
            await self._send_order_confirmation_sms(order)
        
        db.close()
    
    async def send_payment_confirmation(self, order_id: str):
        """Enviar confirmação de pagamento"""
        
        db = next(get_db())
        order = db.query(Order).filter(Order.id == order_id).first()
        
        if not order:
            return
        
        # Enviar email para o cliente
        if order.customer_email:
            await self._send_payment_confirmation_email(order)
        
        # Notificar estabelecimento
        await self._send_payment_notification_to_establishment(order)
        
        db.close()
    
    async def send_order_status_update(self, order_id: str, new_status: str):
        """Enviar atualização de status do pedido"""
        
        db = next(get_db())
        order = db.query(Order).filter(Order.id == order_id).first()
        
        if not order:
            return
        
        # Enviar email para o cliente
        if order.customer_email:
            await self._send_status_update_email(order, new_status)
        
        # Enviar push notification se o cliente tem o app
        if order.user_id:
            await self._send_status_update_push(order, new_status)
        
        # Enviar SMS para status importantes
        if new_status in ["ready", "out_for_delivery", "delivered"] and order.customer_phone:
            await self._send_status_update_sms(order, new_status)
        
        db.close()
    
    async def _send_order_confirmation_email(self, order: Order):
        """Enviar email de confirmação do pedido"""
        
        subject = f"Pedido #{order.order_number} confirmado - {order.establishment.name}"
        
        # Template HTML do email
        html_content = f"""
        <html>
        <body>
            <h2>Pedido Confirmado!</h2>
            <p>Olá {order.customer_name},</p>
            <p>Seu pedido foi confirmado e está sendo preparado.</p>
            
            <h3>Detalhes do Pedido</h3>
            <p><strong>Número:</strong> #{order.order_number}</p>
            <p><strong>Estabelecimento:</strong> {order.establishment.name}</p>
            <p><strong>Tipo:</strong> {"Delivery" if order.order_type == "delivery" else "Retirada" if order.order_type == "pickup" else "Mesa"}</p>
            <p><strong>Total:</strong> R$ {order.total_amount:.2f}</p>
            
            <h3>Itens</h3>
            <ul>
        """
        
        for item in order.items:
            html_content += f"<li>{item.quantity}x {item.item_name} - R$ {item.total_price:.2f}</li>"
        
        html_content += f"""
            </ul>
            
            <p><strong>Tempo estimado de preparo:</strong> {order.estimated_prep_time} minutos</p>
            
            <p>Obrigado por escolher {order.establishment.name}!</p>
        </body>
        </html>
        """
        
        await self._send_email(order.customer_email, subject, html_content)
    
    async def _send_payment_confirmation_email(self, order: Order):
        """Enviar email de confirmação do pagamento"""
        
        subject = f"Pagamento confirmado - Pedido #{order.order_number}"
        
        html_content = f"""
        <html>
        <body>
            <h2>Pagamento Confirmado!</h2>
            <p>Olá {order.customer_name},</p>
            <p>Confirmamos o recebimento do seu pagamento.</p>
            
            <p><strong>Pedido:</strong> #{order.order_number}</p>
            <p><strong>Valor:</strong> R$ {order.total_amount:.2f}</p>
            
            <p>Seu pedido já está sendo preparado!</p>
        </body>
        </html>
        """
        
        await self._send_email(order.customer_email, subject, html_content)
    
    async def _send_status_update_email(self, order: Order, new_status: str):
        """Enviar email de atualização de status"""
        
        status_messages = {
            "confirmed": "confirmado",
            "preparing": "sendo preparado",
            "ready": "pronto",
            "out_for_delivery": "saiu para entrega",
            "delivered": "entregue",
            "cancelled": "cancelado"
        }
        
        status_message = status_messages.get(new_status, new_status)
        subject = f"Pedido #{order.order_number} {status_message}"
        
        html_content = f"""
        <html>
        <body>
            <h2>Atualização do Pedido</h2>
            <p>Olá {order.customer_name},</p>
            <p>Seu pedido #{order.order_number} está <strong>{status_message}</strong>.</p>
        """
        
        if new_status == "ready" and order.order_type == "pickup":
            html_content += "<p>Você já pode retirar seu pedido!</p>"
        elif new_status == "out_for_delivery":
            html_content += "<p>Seu pedido está a caminho!</p>"
        elif new_status == "delivered":
            html_content += "<p>Esperamos que tenha gostado! Obrigado pela preferência.</p>"
        
        html_content += """
        </body>
        </html>
        """
        
        await self._send_email(order.customer_email, subject, html_content)
    
    async def _send_order_confirmation_sms(self, order: Order):
        """Enviar SMS de confirmação do pedido"""
        
        message = f"Pedido #{order.order_number} confirmado em {order.establishment.name}. Tempo estimado: {order.estimated_prep_time}min. Total: R$ {order.total_amount:.2f}"
        
        await self._send_sms(order.customer_phone, message)
    
    async def _send_status_update_sms(self, order: Order, new_status: str):
        """Enviar SMS de atualização de status"""
        
        messages = {
            "ready": f"Seu pedido #{order.order_number} está pronto para retirada!",
            "out_for_delivery": f"Seu pedido #{order.order_number} saiu para entrega!",
            "delivered": f"Pedido #{order.order_number} entregue. Obrigado!"
        }
        
        message = messages.get(new_status)
        if message:
            await self._send_sms(order.customer_phone, message)
    
    async def _send_order_notification_to_establishment(self, order: Order):
        """Enviar notificação de novo pedido para o estabelecimento"""
        
        # Buscar usuários do estabelecimento
        db = next(get_db())
        establishment_users = db.query(User).filter(
            User.establishment_id == order.establishment_id,
            User.role.in_(["owner", "manager", "employee"])
        ).all()
        
        for user in establishment_users:
            if user.email:
                subject = f"Novo pedido #{order.order_number}"
                html_content = f"""
                <html>
                <body>
                    <h2>Novo Pedido Recebido!</h2>
                    <p><strong>Número:</strong> #{order.order_number}</p>
                    <p><strong>Cliente:</strong> {order.customer_name}</p>
                    <p><strong>Telefone:</strong> {order.customer_phone}</p>
                    <p><strong>Tipo:</strong> {"Delivery" if order.order_type == "delivery" else "Retirada" if order.order_type == "pickup" else "Mesa"}</p>
                    <p><strong>Total:</strong> R$ {order.total_amount:.2f}</p>
                    
                    <p>Acesse o painel para gerenciar o pedido.</p>
                </body>
                </html>
                """
                
                await self._send_email(user.email, subject, html_content)
        
        db.close()
    
    async def _send_payment_notification_to_establishment(self, order: Order):
        """Enviar notificação de pagamento confirmado para o estabelecimento"""
        
        # Implementar notificação para o estabelecimento
        pass
    
    async def _send_status_update_push(self, order: Order, new_status: str):
        """Enviar push notification de atualização de status"""
        
        if not self.push_api_key:
            return
        
        # Implementar push notification (Firebase, OneSignal, etc.)
        # Exemplo com OneSignal:
        
        headers = {
            "Content-Type": "application/json; charset=utf-8",
            "Authorization": f"Basic {self.push_api_key}"
        }
        
        payload = {
            "app_id": self.push_app_id,
            "include_external_user_ids": [order.user_id],
            "contents": {"en": f"Pedido #{order.order_number} atualizado"},
            "headings": {"en": "Atualização do Pedido"},
            "data": {
                "order_id": order.id,
                "order_number": order.order_number,
                "status": new_status
            }
        }
        
        try:
            response = requests.post(
                "https://onesignal.com/api/v1/notifications",
                headers=headers,
                json=payload
            )
            return response.status_code == 200
        except:
            return False
    
    async def _send_email(self, to_email: str, subject: str, html_content: str):
        """Enviar email"""
        
        if not self.smtp_server or not self.from_email:
            return False
        
        try:
            msg = MIMEMultipart('alternative')
            msg['Subject'] = subject
            msg['From'] = self.from_email
            msg['To'] = to_email
            
            html_part = MIMEText(html_content, 'html')
            msg.attach(html_part)
            
            with smtplib.SMTP(self.smtp_server, self.smtp_port) as server:
                if self.smtp_username and self.smtp_password:
                    server.starttls()
                    server.login(self.smtp_username, self.smtp_password)
                
                server.send_message(msg)
            
            return True
            
        except Exception as e:
            print(f"Erro ao enviar email: {str(e)}")
            return False
    
    async def _send_sms(self, phone: str, message: str):
        """Enviar SMS"""
        
        # Implementar integração com provedor de SMS (Twilio, etc.)
        # Por enquanto apenas log
        print(f"SMS para {phone}: {message}")
        return True
