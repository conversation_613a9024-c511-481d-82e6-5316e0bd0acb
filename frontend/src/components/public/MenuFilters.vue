<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Filtros</h3>

    <!-- Categorias -->
    <div class="mb-6">
      <h4 class="text-sm font-medium text-gray-700 mb-3">Categorias</h4>
      <div class="space-y-2">
        <label class="flex items-center">
          <input
            type="radio"
            :value="''"
            v-model="selectedCategory"
            @change="handleCategoryChange"
            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
          />
          <span class="ml-3 text-sm text-gray-700">
            Todas as categorias
            <span class="text-gray-500">({{ totalItems }})</span>
          </span>
        </label>
        
        <label
          v-for="category in availableCategories"
          :key="category.id"
          class="flex items-center"
        >
          <input
            type="radio"
            :value="category.id"
            v-model="selectedCategory"
            @change="handleCategoryChange"
            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300"
          />
          <span class="ml-3 text-sm text-gray-700 flex items-center">
            <img
              v-if="category.image_url"
              :src="category.image_url"
              :alt="category.name"
              class="h-4 w-4 rounded object-cover mr-2"
            />
            {{ category.name }}
            <span class="text-gray-500 ml-1">({{ getItemsCountByCategory(category.id) }})</span>
          </span>
        </label>
      </div>
    </div>

    <!-- Filtros dietéticos -->
    <div class="mb-6">
      <h4 class="text-sm font-medium text-gray-700 mb-3">Restrições Alimentares</h4>
      <div class="space-y-2">
        <label
          v-for="filter in dietaryFilters"
          :key="filter.key"
          class="flex items-center"
        >
          <input
            type="checkbox"
            :checked="isFilterActive(filter.key)"
            @change="toggleFilter(filter.key)"
            class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
          />
          <span class="ml-3 text-sm text-gray-700 flex items-center">
            <component :is="filter.icon" class="h-4 w-4 mr-2" :class="filter.color" />
            {{ filter.label }}
          </span>
        </label>
      </div>
    </div>

    <!-- Faixa de preço -->
    <div class="mb-6">
      <h4 class="text-sm font-medium text-gray-700 mb-3">Faixa de Preço</h4>
      
      <div class="space-y-4">
        <!-- Inputs de preço -->
        <div class="grid grid-cols-2 gap-3">
          <div>
            <label class="block text-xs text-gray-500 mb-1">Mínimo</label>
            <div class="relative">
              <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">R$</span>
              <input
                v-model.number="minPrice"
                type="number"
                min="0"
                :max="maxPrice || priceRange.max"
                step="0.01"
                placeholder="0,00"
                class="w-full pl-8 pr-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                @input="handlePriceChange"
              />
            </div>
          </div>
          
          <div>
            <label class="block text-xs text-gray-500 mb-1">Máximo</label>
            <div class="relative">
              <span class="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-500 text-sm">R$</span>
              <input
                v-model.number="maxPrice"
                type="number"
                :min="minPrice || priceRange.min"
                step="0.01"
                placeholder="0,00"
                class="w-full pl-8 pr-3 py-2 text-sm border border-gray-300 rounded-md focus:ring-2 focus:ring-primary-500 focus:border-transparent"
                @input="handlePriceChange"
              />
            </div>
          </div>
        </div>

        <!-- Range slider -->
        <div class="px-1">
          <div class="relative">
            <input
              v-model.number="minPrice"
              type="range"
              :min="priceRange.min"
              :max="priceRange.max"
              step="0.01"
              class="absolute w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider-thumb"
              @input="handlePriceChange"
            />
            <input
              v-model.number="maxPrice"
              type="range"
              :min="priceRange.min"
              :max="priceRange.max"
              step="0.01"
              class="absolute w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer slider-thumb"
              @input="handlePriceChange"
            />
          </div>
          
          <div class="flex justify-between text-xs text-gray-500 mt-2">
            <span>{{ formatCurrency(priceRange.min) }}</span>
            <span>{{ formatCurrency(priceRange.max) }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Botão limpar filtros -->
    <div class="pt-4 border-t border-gray-200">
      <button
        @click="clearAllFilters"
        class="w-full btn btn-outline btn-sm"
        :disabled="!hasActiveFilters"
      >
        <XMarkIcon class="h-4 w-4 mr-2" />
        Limpar Filtros
      </button>
    </div>

    <!-- Resumo dos filtros ativos -->
    <div v-if="hasActiveFilters" class="mt-4 pt-4 border-t border-gray-200">
      <h5 class="text-xs font-medium text-gray-700 mb-2">Filtros Ativos:</h5>
      <div class="flex flex-wrap gap-1">
        <span
          v-for="filter in activeFiltersLabels"
          :key="filter"
          class="inline-flex items-center px-2 py-1 rounded-full text-xs bg-primary-100 text-primary-800"
        >
          {{ filter }}
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import {
  XMarkIcon,
  LeafIcon,
  HeartIcon,
  ShieldCheckIcon,
  FireIcon
} from '@heroicons/vue/24/outline'

import { usePublicMenuStore } from '@/stores/publicMenu'

const menuStore = usePublicMenuStore()

// Estado local
const selectedCategory = ref('')
const minPrice = ref(0)
const maxPrice = ref(0)

// Computed
const availableCategories = computed(() => menuStore.availableCategories)
const priceRange = computed(() => menuStore.priceRange)
const totalItems = computed(() => menuStore.menuItems.length)

const dietaryFilters = [
  {
    key: 'isVegetarian',
    label: 'Vegetariano',
    icon: LeafIcon,
    color: 'text-green-500'
  },
  {
    key: 'isVegan',
    label: 'Vegano',
    icon: HeartIcon,
    color: 'text-green-600'
  },
  {
    key: 'isGlutenFree',
    label: 'Sem Glúten',
    icon: ShieldCheckIcon,
    color: 'text-blue-500'
  },
  {
    key: 'isSpicy',
    label: 'Picante',
    icon: FireIcon,
    color: 'text-red-500'
  }
]

const hasActiveFilters = computed(() => {
  const filters = menuStore.filters
  return filters.categoryId ||
         filters.isVegetarian ||
         filters.isVegan ||
         filters.isGlutenFree ||
         filters.isSpicy ||
         filters.minPrice > 0 ||
         filters.maxPrice > 0
})

const activeFiltersLabels = computed(() => {
  const labels: string[] = []
  const filters = menuStore.filters

  if (filters.categoryId) {
    const category = availableCategories.value.find(c => c.id === filters.categoryId)
    if (category) {
      labels.push(category.name)
    }
  }

  dietaryFilters.forEach(filter => {
    if ((filters as any)[filter.key]) {
      labels.push(filter.label)
    }
  })

  if (filters.minPrice > 0) {
    labels.push(`Min: ${formatCurrency(filters.minPrice)}`)
  }

  if (filters.maxPrice > 0) {
    labels.push(`Max: ${formatCurrency(filters.maxPrice)}`)
  }

  return labels
})

// Methods
const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value)
}

const getItemsCountByCategory = (categoryId: string): number => {
  return menuStore.getItemsByCategory(categoryId).length
}

const isFilterActive = (filterKey: string): boolean => {
  return (menuStore.filters as any)[filterKey] === true
}

const toggleFilter = (filterKey: string) => {
  menuStore.toggleDietaryFilter(filterKey as any)
}

const handleCategoryChange = () => {
  menuStore.filterByCategory(selectedCategory.value)
}

const handlePriceChange = () => {
  // Garantir que min não seja maior que max
  if (minPrice.value > maxPrice.value && maxPrice.value > 0) {
    minPrice.value = maxPrice.value
  }
  
  menuStore.setPriceRange(minPrice.value, maxPrice.value)
}

const clearAllFilters = () => {
  selectedCategory.value = ''
  minPrice.value = 0
  maxPrice.value = 0
  menuStore.clearFilters()
}

// Watchers
watch(() => menuStore.filters.categoryId, (newValue) => {
  selectedCategory.value = newValue
})

watch(() => priceRange.value, (newRange) => {
  if (minPrice.value === 0 && maxPrice.value === 0) {
    minPrice.value = newRange.min
    maxPrice.value = newRange.max
  }
}, { immediate: true })
</script>

<style scoped>
/* Custom range slider styles */
.slider-thumb::-webkit-slider-thumb {
  appearance: none;
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.slider-thumb::-moz-range-thumb {
  height: 16px;
  width: 16px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: 2px solid #ffffff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}
</style>
