"""
Schemas para pedidos
"""
from pydantic import BaseModel, Field, validator
from typing import Optional, List
from datetime import datetime
from decimal import Decimal
from app.models.order import OrderStatus, OrderType, PaymentStatus, PaymentMethod


class OrderItemResponse(BaseModel):
    """Schema de resposta para item do pedido"""
    id: str
    menu_item_id: str
    item_name: str
    item_description: Optional[str]
    quantity: int
    unit_price: Decimal
    total_price: Decimal
    notes: Optional[str]
    created_at: datetime
    
    class Config:
        from_attributes = True


class PaymentTransactionResponse(BaseModel):
    """Schema de resposta para transação de pagamento"""
    id: str
    transaction_id: str
    payment_method: PaymentMethod
    status: PaymentStatus
    amount: Decimal
    fee: Decimal
    net_amount: Decimal
    gateway_provider: Optional[str]
    pix_qr_code: Optional[str]
    pix_qr_code_base64: Optional[str]
    pix_expiration: Optional[datetime]
    card_last_four: Optional[str]
    card_brand: Optional[str]
    created_at: datetime
    processed_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class OrderResponse(BaseModel):
    """Schema de resposta para pedido"""
    id: str
    order_number: str
    establishment_id: str
    user_id: Optional[str]
    status: OrderStatus
    order_type: OrderType
    
    # Informações do cliente
    customer_name: str
    customer_phone: str
    customer_email: Optional[str]
    
    # Endereço de entrega
    delivery_address: Optional[str]
    delivery_city: Optional[str]
    delivery_state: Optional[str]
    delivery_zip_code: Optional[str]
    delivery_latitude: Optional[float]
    delivery_longitude: Optional[float]
    
    # Mesa
    table_number: Optional[int]
    
    # Valores
    subtotal: Decimal
    delivery_fee: Decimal
    service_fee: Decimal
    discount_amount: Decimal
    total_amount: Decimal
    
    # Observações e tempos
    notes: Optional[str]
    estimated_prep_time: Optional[int]
    estimated_delivery_time: Optional[int]
    
    # Timestamps
    created_at: datetime
    updated_at: datetime
    confirmed_at: Optional[datetime]
    prepared_at: Optional[datetime]
    delivered_at: Optional[datetime]
    cancelled_at: Optional[datetime]
    
    # Relacionamentos
    items: List[OrderItemResponse] = []
    payment_transactions: List[PaymentTransactionResponse] = []
    
    class Config:
        from_attributes = True


class OrderListResponse(BaseModel):
    """Schema de resposta para lista de pedidos"""
    id: str
    order_number: str
    status: OrderStatus
    order_type: OrderType
    customer_name: str
    customer_phone: str
    total_amount: Decimal
    created_at: datetime
    estimated_prep_time: Optional[int]
    
    class Config:
        from_attributes = True


class OrderStatusUpdate(BaseModel):
    """Schema para atualização de status do pedido"""
    status: OrderStatus = Field(..., description="Novo status do pedido")
    notes: Optional[str] = Field(None, description="Observações sobre a mudança")


class OrderFilters(BaseModel):
    """Schema para filtros de pedidos"""
    status: Optional[OrderStatus] = Field(None, description="Filtrar por status")
    order_type: Optional[OrderType] = Field(None, description="Filtrar por tipo")
    customer_name: Optional[str] = Field(None, description="Filtrar por nome do cliente")
    customer_phone: Optional[str] = Field(None, description="Filtrar por telefone")
    date_from: Optional[datetime] = Field(None, description="Data inicial")
    date_to: Optional[datetime] = Field(None, description="Data final")
    limit: int = Field(50, ge=1, le=100, description="Limite de resultados")
    offset: int = Field(0, ge=0, description="Offset para paginação")


class OrderStats(BaseModel):
    """Schema para estatísticas de pedidos"""
    total_orders: int
    pending_orders: int
    confirmed_orders: int
    preparing_orders: int
    ready_orders: int
    out_for_delivery_orders: int
    delivered_orders: int
    cancelled_orders: int
    total_revenue: Decimal
    today_revenue: Decimal
    average_order_value: Decimal


class PaymentRequest(BaseModel):
    """Schema para solicitação de pagamento"""
    payment_method: PaymentMethod = Field(..., description="Método de pagamento")
    
    # Para cartão de crédito/débito
    card_token: Optional[str] = Field(None, description="Token do cartão")
    installments: Optional[int] = Field(1, ge=1, le=12, description="Número de parcelas")
    
    # Para PIX
    pix_expiration_minutes: Optional[int] = Field(30, ge=5, le=1440, description="Expiração do PIX em minutos")


class PaymentResponse(BaseModel):
    """Schema de resposta para pagamento"""
    transaction_id: str
    status: PaymentStatus
    payment_method: PaymentMethod
    amount: Decimal
    
    # Para PIX
    pix_qr_code: Optional[str]
    pix_qr_code_base64: Optional[str]
    pix_expiration: Optional[datetime]
    
    # Para cartão
    card_last_four: Optional[str]
    card_brand: Optional[str]
    
    # URLs
    payment_url: Optional[str]
    success_url: Optional[str]
    failure_url: Optional[str]


class WebhookPayment(BaseModel):
    """Schema para webhook de pagamento"""
    transaction_id: str
    status: PaymentStatus
    amount: Decimal
    gateway_response: dict
    processed_at: datetime


class OrderNotification(BaseModel):
    """Schema para notificação de pedido"""
    order_id: str
    order_number: str
    status: OrderStatus
    customer_name: str
    establishment_id: str
    message: str
    timestamp: datetime


class OrderTimeline(BaseModel):
    """Schema para timeline do pedido"""
    status: OrderStatus
    timestamp: Optional[datetime]
    description: str
    is_current: bool = False
    is_completed: bool = False


class OrderTrackingResponse(BaseModel):
    """Schema para acompanhamento do pedido"""
    order_id: str
    order_number: str
    status: OrderStatus
    estimated_prep_time: Optional[int]
    estimated_delivery_time: Optional[int]
    timeline: List[OrderTimeline]
    current_step: int
    total_steps: int
    
    class Config:
        from_attributes = True
