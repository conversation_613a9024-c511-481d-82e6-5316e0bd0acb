<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-4">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4">
              <button
                @click="goBack"
                class="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
              >
                <ArrowLeftIcon class="h-6 w-6" />
              </button>
              <h1 class="text-2xl font-bold text-gray-900">Seu Carr<PERSON><PERSON></h1>
            </div>
            
            <div v-if="establishment" class="text-right">
              <p class="text-sm text-gray-600">Pedido em</p>
              <p class="font-medium text-gray-900">{{ establishment.name }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Con<PERSON><PERSON>do principal -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Loading state -->
      <div v-if="isLoading" class="flex justify-center py-12">
        <div class="spinner w-8 h-8"></div>
      </div>

      <!-- Carrinho vazio -->
      <div v-else-if="isEmpty" class="text-center py-12">
        <ShoppingCartIcon class="mx-auto h-24 w-24 text-gray-400" />
        <h2 class="mt-4 text-xl font-medium text-gray-900">Seu carrinho está vazio</h2>
        <p class="mt-2 text-gray-600">Adicione alguns itens deliciosos para começar!</p>
        <button
          @click="goToMenu"
          class="mt-6 btn btn-primary"
        >
          Ver Cardápio
        </button>
      </div>

      <!-- Carrinho com itens -->
      <div v-else class="lg:grid lg:grid-cols-3 lg:gap-8">
        <!-- Lista de itens -->
        <div class="lg:col-span-2">
          <div class="bg-white rounded-lg shadow-sm border border-gray-200">
            <div class="px-6 py-4 border-b border-gray-200">
              <h2 class="text-lg font-medium text-gray-900">
                Itens do Pedido ({{ totalItems }})
              </h2>
            </div>

            <div class="divide-y divide-gray-200">
              <CartItemRow
                v-for="item in items"
                :key="item.id"
                :item="item"
                @update-quantity="handleUpdateQuantity"
                @remove-item="handleRemoveItem"
                @add-notes="handleAddNotes"
              />
            </div>
          </div>

          <!-- Observações gerais -->
          <div class="mt-6 bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Observações do Pedido</h3>
            <textarea
              v-model="generalNotes"
              rows="3"
              class="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-primary-500 focus:border-transparent"
              placeholder="Alguma observação especial para o seu pedido?"
              @blur="updateGeneralNotes"
            />
          </div>
        </div>

        <!-- Resumo do pedido -->
        <div class="lg:col-span-1 mt-8 lg:mt-0">
          <div class="sticky top-8">
            <OrderSummary
              :subtotal="subtotal"
              :delivery-fee="deliveryFee"
              :service-fee="serviceFee"
              :total="total"
              :order-type="orderType"
              @change-order-type="handleOrderTypeChange"
              @proceed-checkout="goToCheckout"
              :is-valid="isValidForCheckout"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de confirmação de remoção -->
    <ConfirmationModal
      v-if="showRemoveModal"
      title="Remover item"
      :message="`Tem certeza que deseja remover '${itemToRemove?.item_name}' do carrinho?`"
      confirm-text="Remover"
      cancel-text="Cancelar"
      @confirm="confirmRemoveItem"
      @cancel="cancelRemoveItem"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import {
  ArrowLeftIcon,
  ShoppingCartIcon
} from '@heroicons/vue/24/outline'
import { useToast } from 'vue-toastification'

import { useCartStore } from '@/stores/cart'
import { usePublicMenuStore } from '@/stores/publicMenu'
import type { CartItem } from '@/types'

// Componentes
const CartItemRow = defineAsyncComponent(() => import('@/components/public/CartItemRow.vue'))
const OrderSummary = defineAsyncComponent(() => import('@/components/public/OrderSummary.vue'))
const ConfirmationModal = defineAsyncComponent(() => import('@/components/modals/ConfirmationModal.vue'))

const router = useRouter()
const route = useRoute()
const toast = useToast()
const cartStore = useCartStore()
const menuStore = usePublicMenuStore()

// Estado local
const generalNotes = ref('')
const orderType = ref<'delivery' | 'pickup' | 'table_service'>('delivery')
const showRemoveModal = ref(false)
const itemToRemove = ref<CartItem | null>(null)

// Computed
const establishment = computed(() => menuStore.establishment)
const isLoading = computed(() => cartStore.isLoading)
const items = computed(() => cartStore.items)
const totalItems = computed(() => cartStore.totalItems)
const subtotal = computed(() => cartStore.subtotal)
const deliveryFee = computed(() => cartStore.deliveryFee)
const serviceFee = computed(() => cartStore.serviceFee)
const total = computed(() => cartStore.total)
const isEmpty = computed(() => cartStore.isEmpty)
const isValidForCheckout = computed(() => cartStore.isValidForCheckout)

// Methods
const loadCart = async () => {
  const slug = route.params.slug as string
  
  try {
    // Carregar estabelecimento se não estiver carregado
    if (!establishment.value) {
      await menuStore.fetchEstablishment(slug)
    }
    
    // Carregar carrinho
    if (establishment.value) {
      await cartStore.fetchCart(establishment.value.id)
      
      // Carregar dados do carrinho
      if (cartStore.cart) {
        generalNotes.value = cartStore.cart.notes || ''
        orderType.value = cartStore.cart.order_type || 'delivery'
      }
    }
  } catch (error) {
    console.error('Erro ao carregar carrinho:', error)
    toast.error('Erro ao carregar carrinho')
  }
}

const handleUpdateQuantity = async (item: CartItem, newQuantity: number) => {
  if (!establishment.value) return

  try {
    await cartStore.updateItem(
      establishment.value.id,
      item.id,
      newQuantity,
      item.notes
    )
  } catch (error: any) {
    toast.error(error.message || 'Erro ao atualizar quantidade')
  }
}

const handleRemoveItem = (item: CartItem) => {
  itemToRemove.value = item
  showRemoveModal.value = true
}

const confirmRemoveItem = async () => {
  if (!itemToRemove.value || !establishment.value) return

  try {
    await cartStore.removeItem(establishment.value.id, itemToRemove.value.id)
    toast.success('Item removido do carrinho')
  } catch (error: any) {
    toast.error(error.message || 'Erro ao remover item')
  } finally {
    showRemoveModal.value = false
    itemToRemove.value = null
  }
}

const cancelRemoveItem = () => {
  showRemoveModal.value = false
  itemToRemove.value = null
}

const handleAddNotes = async (item: CartItem, notes: string) => {
  if (!establishment.value) return

  try {
    await cartStore.updateItem(
      establishment.value.id,
      item.id,
      item.quantity,
      notes
    )
  } catch (error: any) {
    toast.error(error.message || 'Erro ao atualizar observações')
  }
}

const updateGeneralNotes = async () => {
  if (!establishment.value) return

  try {
    await cartStore.updateCartInfo(establishment.value.id, {
      notes: generalNotes.value
    })
  } catch (error: any) {
    console.error('Erro ao atualizar observações:', error)
  }
}

const handleOrderTypeChange = async (newOrderType: 'delivery' | 'pickup' | 'table_service') => {
  if (!establishment.value) return

  orderType.value = newOrderType

  try {
    await cartStore.updateCartInfo(establishment.value.id, {
      order_type: newOrderType
    })
  } catch (error: any) {
    toast.error(error.message || 'Erro ao atualizar tipo de pedido')
  }
}

const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    goToMenu()
  }
}

const goToMenu = () => {
  const slug = route.params.slug as string
  router.push(`/menu/${slug}`)
}

const goToCheckout = () => {
  const slug = route.params.slug as string
  router.push(`/checkout/${slug}`)
}

// Lifecycle
onMounted(() => {
  loadCart()
})

// Lazy loading de componentes
import { defineAsyncComponent } from 'vue'
</script>

<style scoped>
.spinner {
  border: 2px solid #f3f4f6;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
