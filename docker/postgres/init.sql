-- Inicialização do banco de dados PostgreSQL para o sistema de cardápio digital

-- <PERSON><PERSON>r extensões necessárias
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Criar schema principal
CREATE SCHEMA IF NOT EXISTS public;

-- Função para criar timestamps automáticos
CREATE OR REPLACE FUNCTION trigger_set_timestamp()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- <PERSON><PERSON><PERSON> de estabelecimentos (tenants)
CREATE TABLE IF NOT EXISTS establishments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    phone VARCHAR(20),
    address TEXT,
    city VARCHAR(100),
    state VARCHAR(50),
    zip_code VARCHAR(20),
    country VARCHAR(50) DEFAULT 'Brasil',
    
    -- Configurações de personalização
    logo_url TEXT,
    primary_color VARCHAR(7) DEFAULT '#3B82F6',
    secondary_color VARCHAR(7) DEFAULT '#1F2937',
    
    -- Configurações de funcionamento
    is_active BOOLEAN DEFAULT true,
    accepts_orders BOOLEAN DEFAULT true,
    delivery_fee DECIMAL(10,2) DEFAULT 0.00,
    minimum_order DECIMAL(10,2) DEFAULT 0.00,
    
    -- Configurações de pagamento
    accepts_pix BOOLEAN DEFAULT true,
    accepts_card BOOLEAN DEFAULT true,
    mercado_pago_token TEXT,
    
    -- Timestamps
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Trigger para updated_at na tabela establishments
CREATE TRIGGER set_timestamp_establishments
    BEFORE UPDATE ON establishments
    FOR EACH ROW
    EXECUTE PROCEDURE trigger_set_timestamp();

-- Índices para performance
CREATE INDEX IF NOT EXISTS idx_establishments_slug ON establishments(slug);
CREATE INDEX IF NOT EXISTS idx_establishments_active ON establishments(is_active);

-- Inserir estabelecimento de exemplo para desenvolvimento
INSERT INTO establishments (name, slug, email, phone, address, city, state) 
VALUES (
    'Restaurante Exemplo',
    'restaurante-exemplo',
    '<EMAIL>',
    '(11) 99999-9999',
    'Rua das Flores, 123',
    'São Paulo',
    'SP'
) ON CONFLICT (slug) DO NOTHING;

-- Comentários nas tabelas
COMMENT ON TABLE establishments IS 'Tabela principal de estabelecimentos (tenants do sistema)';
COMMENT ON COLUMN establishments.slug IS 'Identificador único para URLs amigáveis';
COMMENT ON COLUMN establishments.primary_color IS 'Cor primária da marca em hexadecimal';
COMMENT ON COLUMN establishments.secondary_color IS 'Cor secundária da marca em hexadecimal';
COMMENT ON COLUMN establishments.delivery_fee IS 'Taxa de entrega padrão';
COMMENT ON COLUMN establishments.minimum_order IS 'Valor mínimo do pedido';
COMMENT ON COLUMN establishments.mercado_pago_token IS 'Token de acesso do Mercado Pago (criptografado)';

-- Configurações de segurança
ALTER TABLE establishments ENABLE ROW LEVEL SECURITY;
