<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
    <h3 class="text-lg font-medium text-gray-900 mb-4">Resumo do Pedido</h3>

    <!-- Itens do pedido -->
    <div class="space-y-3 mb-6">
      <div
        v-for="item in cart?.items || []"
        :key="item.id"
        class="flex items-center justify-between text-sm"
      >
        <div class="flex-1 min-w-0">
          <p class="font-medium text-gray-900 truncate">
            {{ item.quantity }}x {{ item.item_name }}
          </p>
          <p v-if="item.notes" class="text-xs text-gray-500 truncate">
            {{ item.notes }}
          </p>
        </div>
        <span class="text-gray-900 font-medium ml-2">
          {{ formatCurrency(item.total_price) }}
        </span>
      </div>
    </div>

    <!-- Resumo financeiro -->
    <div class="space-y-2 mb-6 pb-4 border-b border-gray-200">
      <div class="flex justify-between text-sm">
        <span class="text-gray-600">Subtotal</span>
        <span class="text-gray-900">{{ formatCurrency(cart?.subtotal || 0) }}</span>
      </div>

      <div v-if="deliveryFee > 0" class="flex justify-between text-sm">
        <span class="text-gray-600">Taxa de entrega</span>
        <span class="text-gray-900">{{ formatCurrency(deliveryFee) }}</span>
      </div>

      <div v-if="serviceFee > 0" class="flex justify-between text-sm">
        <span class="text-gray-600">Taxa de serviço</span>
        <span class="text-gray-900">{{ formatCurrency(serviceFee) }}</span>
      </div>

      <div class="flex justify-between text-base font-medium pt-2 border-t border-gray-200">
        <span class="text-gray-900">Total</span>
        <span class="text-gray-900">{{ formatCurrency(total) }}</span>
      </div>
    </div>

    <!-- Informações do pedido -->
    <div class="space-y-3 mb-6">
      <!-- Tipo de pedido -->
      <div class="flex items-center text-sm">
        <component :is="orderTypeIcon" class="h-5 w-5 text-gray-500 mr-2" />
        <span class="text-gray-600">{{ orderTypeLabel }}</span>
      </div>

      <!-- Tempo estimado -->
      <div v-if="estimatedTime" class="flex items-center text-sm">
        <ClockIcon class="h-5 w-5 text-gray-500 mr-2" />
        <span class="text-gray-600">
          {{ estimatedTime }} min ({{ orderType === 'delivery' ? 'entrega' : 'preparo' }})
        </span>
      </div>

      <!-- Endereço de entrega -->
      <div v-if="orderType === 'delivery' && cart?.delivery_address" class="flex items-start text-sm">
        <MapPinIcon class="h-5 w-5 text-gray-500 mr-2 mt-0.5 flex-shrink-0" />
        <div class="text-gray-600">
          <p>{{ cart.delivery_address }}</p>
          <p>{{ cart.delivery_city }}, {{ cart.delivery_state }}</p>
          <p>{{ cart.delivery_zip_code }}</p>
        </div>
      </div>

      <!-- Mesa -->
      <div v-if="orderType === 'table_service' && cart?.table_number" class="flex items-center text-sm">
        <UserGroupIcon class="h-5 w-5 text-gray-500 mr-2" />
        <span class="text-gray-600">Mesa {{ cart.table_number }}</span>
      </div>
    </div>

    <!-- Botão de finalizar -->
    <button
      @click="handlePlaceOrder"
      :disabled="!isValid || isProcessing"
      :class="[
        'w-full py-3 px-4 rounded-md font-medium transition-all duration-200',
        isValid && !isProcessing
          ? 'bg-primary-600 hover:bg-primary-700 text-white shadow-sm hover:shadow-md'
          : 'bg-gray-300 text-gray-500 cursor-not-allowed'
      ]"
    >
      <div v-if="isProcessing" class="flex items-center justify-center">
        <div class="spinner w-5 h-5 mr-2"></div>
        Processando...
      </div>
      <span v-else>
        Finalizar Pedido - {{ formatCurrency(total) }}
      </span>
    </button>

    <!-- Informações de segurança -->
    <div class="mt-4 flex items-center justify-center text-xs text-gray-500">
      <ShieldCheckIcon class="h-4 w-4 mr-1" />
      <span>Transação 100% segura</span>
    </div>

    <!-- Política de cancelamento -->
    <div class="mt-3 text-xs text-gray-500 text-center">
      <p>
        Você pode cancelar seu pedido gratuitamente até a confirmação pelo restaurante.
      </p>
    </div>

    <!-- Tempo limite para pagamento -->
    <div v-if="paymentTimeLimit" class="mt-4 p-3 bg-yellow-50 rounded-lg">
      <div class="flex items-center">
        <ExclamationTriangleIcon class="h-5 w-5 text-yellow-500 mr-2" />
        <div>
          <p class="text-sm font-medium text-yellow-900">
            Tempo para pagamento
          </p>
          <p class="text-xs text-yellow-700">
            Complete o pagamento em até {{ paymentTimeLimit }} minutos
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import {
  TruckIcon,
  BuildingStorefrontIcon,
  UserGroupIcon,
  ClockIcon,
  MapPinIcon,
  ShieldCheckIcon,
  ExclamationTriangleIcon
} from '@heroicons/vue/24/outline'

import type { Cart } from '@/types'

interface Props {
  cart: Cart | null
  orderType: 'delivery' | 'pickup' | 'table_service'
  estimatedTime?: number
  isProcessing: boolean
  isValid: boolean
  paymentTimeLimit?: number
}

interface Emits {
  (e: 'place-order'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

// Computed
const deliveryFee = computed(() => props.cart?.delivery_fee || 0)
const serviceFee = computed(() => props.cart?.service_fee || 0)
const total = computed(() => props.cart?.total_amount || 0)

const orderTypeIcon = computed(() => {
  const icons = {
    delivery: TruckIcon,
    pickup: BuildingStorefrontIcon,
    table_service: UserGroupIcon
  }
  
  return icons[props.orderType]
})

const orderTypeLabel = computed(() => {
  const labels = {
    delivery: 'Entrega no endereço',
    pickup: 'Retirada no local',
    table_service: 'Atendimento na mesa'
  }
  
  return labels[props.orderType]
})

// Methods
const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value)
}

const handlePlaceOrder = () => {
  if (props.isValid && !props.isProcessing) {
    emit('place-order')
  }
}
</script>

<style scoped>
.spinner {
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
