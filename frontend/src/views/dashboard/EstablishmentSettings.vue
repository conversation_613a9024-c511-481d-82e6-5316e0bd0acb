<template>
  <div class="space-y-6">
    <!-- Header -->
    <div>
      <h1 class="text-2xl font-bold text-gray-900">Configurações do Estabelecimento</h1>
      <p class="text-gray-600"><PERSON><PERSON><PERSON><PERSON> as informações e configurações do seu estabelecimento</p>
    </div>

    <!-- Tabs de navegação -->
    <div class="border-b border-gray-200">
      <nav class="-mb-px flex space-x-8">
        <button
          v-for="tab in tabs"
          :key="tab.id"
          @click="activeTab = tab.id"
          :class="[
            'whitespace-nowrap py-2 px-1 border-b-2 font-medium text-sm',
            activeTab === tab.id
              ? 'border-primary-500 text-primary-600'
              : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
          ]"
        >
          {{ tab.name }}
        </button>
      </nav>
    </div>

    <!-- Con<PERSON><PERSON><PERSON> das tabs -->
    <div class="mt-6">
      <!-- Informações Básicas -->
      <div v-if="activeTab === 'basic'" class="space-y-6">
        <EstablishmentBasicInfo />
      </div>

      <!-- Aparência -->
      <div v-if="activeTab === 'appearance'" class="space-y-6">
        <EstablishmentAppearance />
      </div>

      <!-- Delivery -->
      <div v-if="activeTab === 'delivery'" class="space-y-6">
        <EstablishmentDelivery />
      </div>

      <!-- Pagamentos -->
      <div v-if="activeTab === 'payments'" class="space-y-6">
        <EstablishmentPayments />
      </div>

      <!-- Horários -->
      <div v-if="activeTab === 'hours'" class="space-y-6">
        <EstablishmentHours />
      </div>

      <!-- Notificações -->
      <div v-if="activeTab === 'notifications'" class="space-y-6">
        <EstablishmentNotifications />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'

// Componentes das seções (serão criados posteriormente)
const EstablishmentBasicInfo = defineAsyncComponent(() => import('@/components/settings/EstablishmentBasicInfo.vue'))
const EstablishmentAppearance = defineAsyncComponent(() => import('@/components/settings/EstablishmentAppearance.vue'))
const EstablishmentDelivery = defineAsyncComponent(() => import('@/components/settings/EstablishmentDelivery.vue'))
const EstablishmentPayments = defineAsyncComponent(() => import('@/components/settings/EstablishmentPayments.vue'))
const EstablishmentHours = defineAsyncComponent(() => import('@/components/settings/EstablishmentHours.vue'))
const EstablishmentNotifications = defineAsyncComponent(() => import('@/components/settings/EstablishmentNotifications.vue'))

// Estado
const activeTab = ref('basic')

const tabs = [
  { id: 'basic', name: 'Informações Básicas' },
  { id: 'appearance', name: 'Aparência' },
  { id: 'delivery', name: 'Delivery' },
  { id: 'payments', name: 'Pagamentos' },
  { id: 'hours', name: 'Horários' },
  { id: 'notifications', name: 'Notificações' }
]

// Lazy loading de componentes
import { defineAsyncComponent } from 'vue'
</script>
