<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Sidebar para desktop -->
    <div class="hidden lg:flex lg:w-64 lg:flex-col lg:fixed lg:inset-y-0">
      <div class="flex flex-col flex-grow bg-white border-r border-gray-200 pt-5 pb-4 overflow-y-auto">
        <!-- Logo -->
        <div class="flex items-center flex-shrink-0 px-4">
          <router-link to="/" class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
            </div>
            <span class="text-xl font-bold text-gray-900">Cardápio</span>
          </router-link>
        </div>

        <!-- Navegação -->
        <nav class="mt-8 flex-1 px-2 space-y-1">
          <router-link
            v-for="item in navigation"
            :key="item.name"
            :to="item.to"
            :class="[
              item.current
                ? 'sidebar-link-active'
                : 'sidebar-link',
            ]"
          >
            <component :is="item.icon" class="mr-3 h-5 w-5 flex-shrink-0" />
            {{ item.name }}
          </router-link>
        </nav>

        <!-- Informações do estabelecimento -->
        <div v-if="establishment" class="flex-shrink-0 px-4 py-4 border-t border-gray-200">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <img 
                v-if="establishment.logo_url" 
                :src="establishment.logo_url" 
                :alt="establishment.name"
                class="h-8 w-8 rounded-full object-cover"
              >
              <div v-else class="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center">
                <span class="text-sm font-medium text-primary-600">
                  {{ establishment.name.charAt(0).toUpperCase() }}
                </span>
              </div>
            </div>
            <div class="ml-3 min-w-0 flex-1">
              <p class="text-sm font-medium text-gray-900 truncate">
                {{ establishment.name }}
              </p>
              <p class="text-xs text-gray-500 truncate">
                {{ establishment.slug }}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Sidebar mobile -->
    <TransitionRoot as="template" :show="sidebarOpen">
      <Dialog as="div" class="relative z-40 lg:hidden" @close="sidebarOpen = false">
        <TransitionChild
          as="template"
          enter="transition-opacity ease-linear duration-300"
          enter-from="opacity-0"
          enter-to="opacity-100"
          leave="transition-opacity ease-linear duration-300"
          leave-from="opacity-100"
          leave-to="opacity-0"
        >
          <div class="fixed inset-0 bg-gray-600 bg-opacity-75" />
        </TransitionChild>

        <div class="fixed inset-0 flex z-40">
          <TransitionChild
            as="template"
            enter="transition ease-in-out duration-300 transform"
            enter-from="-translate-x-full"
            enter-to="translate-x-0"
            leave="transition ease-in-out duration-300 transform"
            leave-from="translate-x-0"
            leave-to="-translate-x-full"
          >
            <DialogPanel class="relative flex-1 flex flex-col max-w-xs w-full bg-white">
              <TransitionChild
                as="template"
                enter="ease-in-out duration-300"
                enter-from="opacity-0"
                enter-to="opacity-100"
                leave="ease-in-out duration-300"
                leave-from="opacity-100"
                leave-to="opacity-0"
              >
                <div class="absolute top-0 right-0 -mr-12 pt-2">
                  <button
                    type="button"
                    class="ml-1 flex items-center justify-center h-10 w-10 rounded-full focus:outline-none focus:ring-2 focus:ring-inset focus:ring-white"
                    @click="sidebarOpen = false"
                  >
                    <span class="sr-only">Fechar sidebar</span>
                    <XMarkIcon class="h-6 w-6 text-white" />
                  </button>
                </div>
              </TransitionChild>
              
              <!-- Conteúdo do sidebar mobile (mesmo do desktop) -->
              <div class="flex-1 h-0 pt-5 pb-4 overflow-y-auto">
                <!-- Logo -->
                <div class="flex items-center flex-shrink-0 px-4">
                  <router-link to="/" class="flex items-center space-x-2">
                    <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                      <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                      </svg>
                    </div>
                    <span class="text-xl font-bold text-gray-900">Cardápio</span>
                  </router-link>
                </div>

                <!-- Navegação -->
                <nav class="mt-8 px-2 space-y-1">
                  <router-link
                    v-for="item in navigation"
                    :key="item.name"
                    :to="item.to"
                    :class="[
                      item.current
                        ? 'sidebar-link-active'
                        : 'sidebar-link',
                    ]"
                    @click="sidebarOpen = false"
                  >
                    <component :is="item.icon" class="mr-3 h-5 w-5 flex-shrink-0" />
                    {{ item.name }}
                  </router-link>
                </nav>
              </div>

              <!-- Informações do estabelecimento -->
              <div v-if="establishment" class="flex-shrink-0 px-4 py-4 border-t border-gray-200">
                <div class="flex items-center">
                  <div class="flex-shrink-0">
                    <img 
                      v-if="establishment.logo_url" 
                      :src="establishment.logo_url" 
                      :alt="establishment.name"
                      class="h-8 w-8 rounded-full object-cover"
                    >
                    <div v-else class="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center">
                      <span class="text-sm font-medium text-primary-600">
                        {{ establishment.name.charAt(0).toUpperCase() }}
                      </span>
                    </div>
                  </div>
                  <div class="ml-3 min-w-0 flex-1">
                    <p class="text-sm font-medium text-gray-900 truncate">
                      {{ establishment.name }}
                    </p>
                    <p class="text-xs text-gray-500 truncate">
                      {{ establishment.slug }}
                    </p>
                  </div>
                </div>
              </div>
            </DialogPanel>
          </TransitionChild>
          <div class="flex-shrink-0 w-14">
            <!-- Força o sidebar a encolher para caber no botão de fechar -->
          </div>
        </div>
      </Dialog>
    </TransitionRoot>

    <!-- Conteúdo principal -->
    <div class="lg:pl-64 flex flex-col flex-1">
      <!-- Header -->
      <div class="sticky top-0 z-10 bg-white shadow-sm border-b border-gray-200">
        <div class="flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8">
          <!-- Botão do menu mobile -->
          <button
            type="button"
            class="lg:hidden -ml-0.5 -mt-0.5 h-12 w-12 inline-flex items-center justify-center rounded-md text-gray-500 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500"
            @click="sidebarOpen = true"
          >
            <span class="sr-only">Abrir sidebar</span>
            <Bars3Icon class="h-6 w-6" />
          </button>

          <!-- Título da página -->
          <div class="flex-1 min-w-0">
            <h1 class="text-lg font-semibold text-gray-900 truncate">
              {{ pageTitle }}
            </h1>
          </div>

          <!-- Menu do usuário -->
          <div class="ml-4 flex items-center space-x-4">
            <!-- Notificações -->
            <button
              type="button"
              class="bg-white p-1 rounded-full text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500"
            >
              <span class="sr-only">Ver notificações</span>
              <BellIcon class="h-6 w-6" />
            </button>

            <!-- Menu do perfil -->
            <Menu as="div" class="relative">
              <div>
                <MenuButton class="max-w-xs bg-white flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                  <span class="sr-only">Abrir menu do usuário</span>
                  <img 
                    v-if="user?.avatar_url" 
                    :src="user.avatar_url" 
                    :alt="user.full_name"
                    class="h-8 w-8 rounded-full object-cover"
                  >
                  <div v-else class="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center">
                    <span class="text-sm font-medium text-primary-600">
                      {{ user?.full_name?.charAt(0).toUpperCase() }}
                    </span>
                  </div>
                </MenuButton>
              </div>
              <transition
                enter-active-class="transition ease-out duration-100"
                enter-from-class="transform opacity-0 scale-95"
                enter-to-class="transform opacity-100 scale-100"
                leave-active-class="transition ease-in duration-75"
                leave-from-class="transform opacity-100 scale-100"
                leave-to-class="transform opacity-0 scale-95"
              >
                <MenuItems class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none">
                  <MenuItem v-slot="{ active }">
                    <router-link
                      to="/dashboard/profile"
                      :class="[active ? 'bg-gray-100' : '', 'block px-4 py-2 text-sm text-gray-700']"
                    >
                      Seu Perfil
                    </router-link>
                  </MenuItem>
                  <MenuItem v-slot="{ active }">
                    <button
                      @click="handleLogout"
                      :class="[active ? 'bg-gray-100' : '', 'block w-full text-left px-4 py-2 text-sm text-gray-700']"
                    >
                      Sair
                    </button>
                  </MenuItem>
                </MenuItems>
              </transition>
            </Menu>
          </div>
        </div>
      </div>

      <!-- Conteúdo da página -->
      <main class="flex-1">
        <div class="py-6">
          <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <router-view />
          </div>
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { 
  Dialog, 
  DialogPanel, 
  Menu, 
  MenuButton, 
  MenuItem, 
  MenuItems, 
  TransitionChild, 
  TransitionRoot 
} from '@headlessui/vue'
import {
  Bars3Icon,
  BellIcon,
  XMarkIcon,
  HomeIcon,
  BuildingStorefrontIcon,
  BookOpenIcon,
  RectangleStackIcon,
  ShoppingBagIcon,
  ChartBarIcon,
  UserIcon,
} from '@heroicons/vue/24/outline'

import { useAuthStore } from '@/stores/auth'
import { useEstablishmentStore } from '@/stores/establishment'

const route = useRoute()
const router = useRouter()
const authStore = useAuthStore()
const establishmentStore = useEstablishmentStore()

const sidebarOpen = ref(false)

const user = computed(() => authStore.user)
const establishment = computed(() => establishmentStore.currentEstablishment)

const navigation = computed(() => {
  const items = [
    { name: 'Dashboard', to: { name: 'dashboard' }, icon: HomeIcon, current: route.name === 'dashboard' },
    { name: 'Cardápio', to: { name: 'menu-management' }, icon: BookOpenIcon, current: route.name === 'menu-management' },
    { name: 'Categorias', to: { name: 'category-management' }, icon: RectangleStackIcon, current: route.name === 'category-management' },
    { name: 'Pedidos', to: { name: 'order-management' }, icon: ShoppingBagIcon, current: route.name === 'order-management' },
  ]

  // Adicionar itens apenas para gerentes/proprietários
  if (authStore.canManageEstablishment) {
    items.push(
      { name: 'Estabelecimento', to: { name: 'establishment-settings' }, icon: BuildingStorefrontIcon, current: route.name === 'establishment-settings' },
      { name: 'Relatórios', to: { name: 'reports' }, icon: ChartBarIcon, current: route.name === 'reports' }
    )
  }

  items.push(
    { name: 'Perfil', to: { name: 'profile' }, icon: UserIcon, current: route.name === 'profile' }
  )

  return items
})

const pageTitle = computed(() => {
  const currentNav = navigation.value.find(item => item.current)
  return currentNav?.name || 'Dashboard'
})

const handleLogout = async () => {
  try {
    await authStore.logout()
    router.push({ name: 'home' })
  } catch (error) {
    console.error('Erro ao fazer logout:', error)
  }
}

onMounted(async () => {
  // Carregar dados do estabelecimento se necessário
  if (authStore.isStaff && !establishment.value) {
    try {
      await establishmentStore.fetchCurrentEstablishment()
    } catch (error) {
      console.error('Erro ao carregar estabelecimento:', error)
    }
  }
})
</script>
