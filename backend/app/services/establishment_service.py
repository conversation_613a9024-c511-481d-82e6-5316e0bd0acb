"""
Serviço para estabelecimentos
"""
from sqlalchemy.orm import Session
from sqlalchemy import func, and_
from fastapi import HTTPException, status
from typing import List, Optional
from datetime import datetime, date

from app.models.establishment import Establishment
from app.models.user import User, UserRole
from app.models.order import Order, OrderStatus
from app.models.menu import MenuItem
from app.models.review import EstablishmentReview
from app.schemas.establishment import (
    EstablishmentCreate, 
    EstablishmentUpdate, 
    EstablishmentHours,
    EstablishmentStats
)


class EstablishmentService:
    """Serviço para operações com estabelecimentos"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def create_establishment(self, establishment_data: EstablishmentCreate) -> Establishment:
        """
        Criar novo estabelecimento
        
        Args:
            establishment_data: Dados do estabelecimento
            
        Returns:
            Estabelecimento criado
            
        Raises:
            HTTPException: Se slug ou email já existem
        """
        # Verificar se slug já existe
        existing_slug = self.db.query(Establishment).filter(
            Establishment.slug == establishment_data.slug
        ).first()
        
        if existing_slug:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Slug já está em uso"
            )
        
        # Verificar se email já existe
        existing_email = self.db.query(Establishment).filter(
            Establishment.email == establishment_data.email
        ).first()
        
        if existing_email:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email já está em uso"
            )
        
        # Criar estabelecimento
        establishment = Establishment(**establishment_data.dict())
        self.db.add(establishment)
        self.db.commit()
        self.db.refresh(establishment)
        
        return establishment
    
    def get_establishment_by_id(self, establishment_id: str) -> Optional[Establishment]:
        """
        Buscar estabelecimento por ID
        
        Args:
            establishment_id: ID do estabelecimento
            
        Returns:
            Estabelecimento encontrado ou None
        """
        return self.db.query(Establishment).filter(
            Establishment.id == establishment_id,
            Establishment.is_active == True
        ).first()
    
    def get_establishment_by_slug(self, slug: str) -> Optional[Establishment]:
        """
        Buscar estabelecimento por slug
        
        Args:
            slug: Slug do estabelecimento
            
        Returns:
            Estabelecimento encontrado ou None
        """
        return self.db.query(Establishment).filter(
            Establishment.slug == slug,
            Establishment.is_active == True
        ).first()
    
    def get_establishments(
        self, 
        skip: int = 0, 
        limit: int = 100,
        city: Optional[str] = None,
        state: Optional[str] = None,
        search: Optional[str] = None
    ) -> List[Establishment]:
        """
        Listar estabelecimentos com filtros
        
        Args:
            skip: Número de registros para pular
            limit: Limite de registros
            city: Filtrar por cidade
            state: Filtrar por estado
            search: Buscar por nome
            
        Returns:
            Lista de estabelecimentos
        """
        query = self.db.query(Establishment).filter(
            Establishment.is_active == True
        )
        
        if city:
            query = query.filter(Establishment.city.ilike(f"%{city}%"))
        
        if state:
            query = query.filter(Establishment.state.ilike(f"%{state}%"))
        
        if search:
            query = query.filter(Establishment.name.ilike(f"%{search}%"))
        
        return query.offset(skip).limit(limit).all()
    
    def update_establishment(
        self, 
        establishment_id: str, 
        establishment_data: EstablishmentUpdate
    ) -> Establishment:
        """
        Atualizar estabelecimento
        
        Args:
            establishment_id: ID do estabelecimento
            establishment_data: Dados para atualização
            
        Returns:
            Estabelecimento atualizado
            
        Raises:
            HTTPException: Se estabelecimento não encontrado ou email em uso
        """
        establishment = self.get_establishment_by_id(establishment_id)
        
        if not establishment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Estabelecimento não encontrado"
            )
        
        # Verificar se email já está em uso por outro estabelecimento
        if establishment_data.email:
            existing_email = self.db.query(Establishment).filter(
                and_(
                    Establishment.email == establishment_data.email,
                    Establishment.id != establishment_id
                )
            ).first()
            
            if existing_email:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Email já está em uso"
                )
        
        # Atualizar campos fornecidos
        update_data = establishment_data.dict(exclude_unset=True)
        
        for field, value in update_data.items():
            setattr(establishment, field, value)
        
        self.db.commit()
        self.db.refresh(establishment)
        
        return establishment
    
    def update_establishment_hours(
        self, 
        establishment_id: str, 
        hours_data: EstablishmentHours
    ) -> Establishment:
        """
        Atualizar horários de funcionamento
        
        Args:
            establishment_id: ID do estabelecimento
            hours_data: Dados dos horários
            
        Returns:
            Estabelecimento atualizado
            
        Raises:
            HTTPException: Se estabelecimento não encontrado
        """
        establishment = self.get_establishment_by_id(establishment_id)
        
        if not establishment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Estabelecimento não encontrado"
            )
        
        # Atualizar horários
        hours_dict = hours_data.dict()
        
        for field, value in hours_dict.items():
            setattr(establishment, field, value)
        
        self.db.commit()
        self.db.refresh(establishment)
        
        return establishment
    
    def delete_establishment(self, establishment_id: str) -> bool:
        """
        Desativar estabelecimento (soft delete)
        
        Args:
            establishment_id: ID do estabelecimento
            
        Returns:
            True se desativado com sucesso
            
        Raises:
            HTTPException: Se estabelecimento não encontrado
        """
        establishment = self.get_establishment_by_id(establishment_id)
        
        if not establishment:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Estabelecimento não encontrado"
            )
        
        establishment.is_active = False
        self.db.commit()
        
        return True
    
    def get_establishment_stats(self, establishment_id: str) -> EstablishmentStats:
        """
        Obter estatísticas do estabelecimento
        
        Args:
            establishment_id: ID do estabelecimento
            
        Returns:
            Estatísticas do estabelecimento
        """
        today = date.today()
        
        # Total de pedidos
        total_orders = self.db.query(func.count(Order.id)).filter(
            Order.establishment_id == establishment_id
        ).scalar() or 0
        
        # Receita total
        total_revenue = self.db.query(func.sum(Order.total_amount)).filter(
            and_(
                Order.establishment_id == establishment_id,
                Order.status.in_([OrderStatus.DELIVERED, OrderStatus.READY])
            )
        ).scalar() or 0.0
        
        # Total de clientes únicos
        total_customers = self.db.query(func.count(func.distinct(Order.customer_id))).filter(
            Order.establishment_id == establishment_id
        ).scalar() or 0
        
        # Total de itens do menu
        total_menu_items = self.db.query(func.count(MenuItem.id)).filter(
            MenuItem.establishment_id == establishment_id,
            MenuItem.is_active == True
        ).scalar() or 0
        
        # Avaliação média
        average_rating = self.db.query(func.avg(EstablishmentReview.overall_rating)).filter(
            EstablishmentReview.establishment_id == establishment_id,
            EstablishmentReview.is_approved == True
        ).scalar()
        
        # Pedidos de hoje
        orders_today = self.db.query(func.count(Order.id)).filter(
            and_(
                Order.establishment_id == establishment_id,
                func.date(Order.created_at) == today
            )
        ).scalar() or 0
        
        # Receita de hoje
        revenue_today = self.db.query(func.sum(Order.total_amount)).filter(
            and_(
                Order.establishment_id == establishment_id,
                func.date(Order.created_at) == today,
                Order.status.in_([OrderStatus.DELIVERED, OrderStatus.READY])
            )
        ).scalar() or 0.0
        
        return EstablishmentStats(
            total_orders=total_orders,
            total_revenue=float(total_revenue),
            total_customers=total_customers,
            total_menu_items=total_menu_items,
            average_rating=float(average_rating) if average_rating else None,
            orders_today=orders_today,
            revenue_today=float(revenue_today)
        )
