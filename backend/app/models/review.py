"""
Modelos relacionados a avaliações e favoritos
"""
from sqlalchemy import Column, String, Text, Integer, ForeignKey, Boolean, UniqueConstraint
from sqlalchemy.dialects.postgresql import UUID
from sqlalchemy.orm import relationship
from app.models.base import BaseModel, TenantMixin


class Review(BaseModel, TenantMixin):
    """Modelo de avaliação de item do cardápio"""
    
    __tablename__ = "reviews"
    
    # Avaliação (1-5 estrelas)
    rating = Column(Integer, nullable=False)
    
    # Comentário
    comment = Column(Text)
    
    # Moderação
    is_approved = Column(Boolean, default=True)
    is_featured = Column(Boolean, default=False)
    
    # Relacionamentos
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False,
        index=True
    )
    
    menu_item_id = Column(
        UUID(as_uuid=True),
        ForeignKey("menuitems.id"),
        nullable=False,
        index=True
    )
    
    order_id = Column(
        UUID(as_uuid=True),
        ForeignKey("orders.id"),
        nullable=True,  # Pode ser null se não vinculado a um pedido específico
        index=True
    )
    
    establishment_id = Column(
        UUID(as_uuid=True),
        ForeignKey("establishments.id"),
        nullable=False,
        index=True
    )
    
    user = relationship("User", back_populates="reviews")
    menu_item = relationship("MenuItem", back_populates="reviews")
    order = relationship("Order")
    
    # Constraint para evitar múltiplas avaliações do mesmo usuário para o mesmo item
    __table_args__ = (
        UniqueConstraint('user_id', 'menu_item_id', name='unique_user_menu_item_review'),
    )
    
    def __repr__(self):
        return f"<Review(rating={self.rating}, user_id='{self.user_id}')>"


class Favorite(BaseModel, TenantMixin):
    """Modelo de favoritos do usuário"""
    
    __tablename__ = "favorites"
    
    # Relacionamentos
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False,
        index=True
    )
    
    menu_item_id = Column(
        UUID(as_uuid=True),
        ForeignKey("menuitems.id"),
        nullable=False,
        index=True
    )
    
    establishment_id = Column(
        UUID(as_uuid=True),
        ForeignKey("establishments.id"),
        nullable=False,
        index=True
    )
    
    user = relationship("User", back_populates="favorites")
    menu_item = relationship("MenuItem", back_populates="favorites")
    
    # Constraint para evitar favoritos duplicados
    __table_args__ = (
        UniqueConstraint('user_id', 'menu_item_id', name='unique_user_menu_item_favorite'),
    )
    
    def __repr__(self):
        return f"<Favorite(user_id='{self.user_id}', menu_item_id='{self.menu_item_id}')>"


class EstablishmentReview(BaseModel, TenantMixin):
    """Modelo de avaliação do estabelecimento"""
    
    __tablename__ = "establishment_reviews"
    
    # Avaliações específicas
    food_rating = Column(Integer, nullable=False)      # 1-5
    service_rating = Column(Integer, nullable=False)   # 1-5
    delivery_rating = Column(Integer)                  # 1-5 (opcional para delivery)
    overall_rating = Column(Integer, nullable=False)   # 1-5
    
    # Comentário
    comment = Column(Text)
    
    # Moderação
    is_approved = Column(Boolean, default=True)
    is_featured = Column(Boolean, default=False)
    
    # Relacionamentos
    user_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=False,
        index=True
    )
    
    order_id = Column(
        UUID(as_uuid=True),
        ForeignKey("orders.id"),
        nullable=True,
        index=True
    )
    
    establishment_id = Column(
        UUID(as_uuid=True),
        ForeignKey("establishments.id"),
        nullable=False,
        index=True
    )
    
    user = relationship("User")
    order = relationship("Order")
    establishment = relationship("Establishment")
    
    # Constraint para evitar múltiplas avaliações do mesmo usuário para o mesmo estabelecimento
    __table_args__ = (
        UniqueConstraint('user_id', 'establishment_id', name='unique_user_establishment_review'),
    )
    
    def __repr__(self):
        return f"<EstablishmentReview(overall_rating={self.overall_rating}, user_id='{self.user_id}')>"
