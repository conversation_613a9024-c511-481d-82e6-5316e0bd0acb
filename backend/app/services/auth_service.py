"""
Serviço de autenticação
"""
from sqlalchemy.orm import Session
from fastapi import HTTPException, status
from datetime import datetime, timedelta
from typing import Optional
import uuid

from app.core.security import (
    verify_password, 
    get_password_hash, 
    create_access_token, 
    create_refresh_token,
    verify_token
)
from app.core.config import settings
from app.models.user import User, UserSession, UserRole
from app.models.establishment import Establishment
from app.schemas.auth import UserRegister, UserLogin, TokenResponse, UserResponse


class AuthService:
    """Serviço de autenticação"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def register_user(self, user_data: UserRegister) -> User:
        """
        Registrar novo usuário
        
        Args:
            user_data: Dados do usuário para registro
            
        Returns:
            Usuário criado
            
        Raises:
            HTTPException: Se email já existe ou estabelecimento não encontrado
        """
        # Verificar se email já existe
        existing_user = self.db.query(User).filter(User.email == user_data.email).first()
        if existing_user:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Email já está em uso"
            )
        
        # Para funcionários, verificar se estabelecimento existe
        establishment_id = None
        if user_data.role in [UserRole.OWNER, UserRole.MANAGER, UserRole.EMPLOYEE]:
            if not user_data.establishment_slug:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail="Slug do estabelecimento é obrigatório para funcionários"
                )
            
            establishment = self.db.query(Establishment).filter(
                Establishment.slug == user_data.establishment_slug
            ).first()
            
            if not establishment:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Estabelecimento não encontrado"
                )
            
            establishment_id = establishment.id
        
        # Criar usuário
        user = User(
            email=user_data.email,
            password_hash=get_password_hash(user_data.password),
            full_name=user_data.full_name,
            phone=user_data.phone,
            role=user_data.role,
            establishment_id=establishment_id
        )
        
        self.db.add(user)
        self.db.commit()
        self.db.refresh(user)
        
        return user
    
    def authenticate_user(self, email: str, password: str) -> Optional[User]:
        """
        Autenticar usuário
        
        Args:
            email: Email do usuário
            password: Senha do usuário
            
        Returns:
            Usuário autenticado ou None se credenciais inválidas
        """
        user = self.db.query(User).filter(
            User.email == email,
            User.is_active == True
        ).first()
        
        if not user or not verify_password(password, user.password_hash):
            return None
        
        if user.is_blocked:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Usuário bloqueado"
            )
        
        return user
    
    def login_user(self, login_data: UserLogin, device_info: Optional[str] = None) -> TokenResponse:
        """
        Fazer login do usuário
        
        Args:
            login_data: Dados de login
            device_info: Informações do dispositivo
            
        Returns:
            Tokens de acesso e refresh
            
        Raises:
            HTTPException: Se credenciais inválidas
        """
        user = self.authenticate_user(login_data.email, login_data.password)
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Email ou senha incorretos"
            )
        
        # Criar tokens
        access_token = create_access_token(subject=str(user.id))
        refresh_token = create_refresh_token(subject=str(user.id))
        
        # Salvar sessão
        session = UserSession(
            user_id=user.id,
            refresh_token=refresh_token,
            expires_at=datetime.utcnow() + timedelta(days=settings.JWT_REFRESH_EXPIRE_DAYS),
            device_info=device_info
        )
        
        self.db.add(session)
        self.db.commit()
        
        # Preparar resposta
        user_response = UserResponse(
            id=str(user.id),
            email=user.email,
            full_name=user.full_name,
            phone=user.phone,
            role=user.role,
            establishment_id=str(user.establishment_id) if user.establishment_id else None,
            is_active=user.is_active,
            email_verified=user.email_verified,
            avatar_url=user.avatar_url
        )
        
        return TokenResponse(
            access_token=access_token,
            refresh_token=refresh_token,
            expires_in=settings.JWT_EXPIRE_MINUTES * 60,
            user=user_response
        )
    
    def refresh_token(self, refresh_token: str) -> TokenResponse:
        """
        Renovar token de acesso
        
        Args:
            refresh_token: Token de refresh
            
        Returns:
            Novos tokens
            
        Raises:
            HTTPException: Se token inválido
        """
        # Verificar token
        user_id = verify_token(refresh_token)
        if not user_id:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Token de refresh inválido"
            )
        
        # Verificar se sessão existe e está válida
        session = self.db.query(UserSession).filter(
            UserSession.refresh_token == refresh_token,
            UserSession.expires_at > datetime.utcnow()
        ).first()
        
        if not session:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Sessão expirada ou inválida"
            )
        
        # Buscar usuário
        user = self.db.query(User).filter(
            User.id == user_id,
            User.is_active == True
        ).first()
        
        if not user:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Usuário não encontrado ou inativo"
            )
        
        # Criar novos tokens
        new_access_token = create_access_token(subject=str(user.id))
        new_refresh_token = create_refresh_token(subject=str(user.id))
        
        # Atualizar sessão
        session.refresh_token = new_refresh_token
        session.expires_at = datetime.utcnow() + timedelta(days=settings.JWT_REFRESH_EXPIRE_DAYS)
        
        self.db.commit()
        
        # Preparar resposta
        user_response = UserResponse(
            id=str(user.id),
            email=user.email,
            full_name=user.full_name,
            phone=user.phone,
            role=user.role,
            establishment_id=str(user.establishment_id) if user.establishment_id else None,
            is_active=user.is_active,
            email_verified=user.email_verified,
            avatar_url=user.avatar_url
        )
        
        return TokenResponse(
            access_token=new_access_token,
            refresh_token=new_refresh_token,
            expires_in=settings.JWT_EXPIRE_MINUTES * 60,
            user=user_response
        )
    
    def logout_user(self, refresh_token: str) -> bool:
        """
        Fazer logout do usuário
        
        Args:
            refresh_token: Token de refresh para invalidar
            
        Returns:
            True se logout realizado com sucesso
        """
        session = self.db.query(UserSession).filter(
            UserSession.refresh_token == refresh_token
        ).first()
        
        if session:
            self.db.delete(session)
            self.db.commit()
            return True
        
        return False
    
    def get_user_by_id(self, user_id: str) -> Optional[User]:
        """
        Buscar usuário por ID
        
        Args:
            user_id: ID do usuário
            
        Returns:
            Usuário encontrado ou None
        """
        return self.db.query(User).filter(
            User.id == user_id,
            User.is_active == True
        ).first()
