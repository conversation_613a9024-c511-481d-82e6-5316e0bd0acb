<template>
  <TransitionRoot as="template" :show="true">
    <Dialog as="div" class="relative z-50" @close="$emit('cancel')">
      <TransitionChild
        as="template"
        enter="ease-out duration-300"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="ease-in duration-200"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
      </TransitionChild>

      <div class="fixed inset-0 z-10 overflow-y-auto">
        <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <TransitionChild
            as="template"
            enter="ease-out duration-300"
            enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            enter-to="opacity-100 translate-y-0 sm:scale-100"
            leave="ease-in duration-200"
            leave-from="opacity-100 translate-y-0 sm:scale-100"
            leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
          >
            <DialogPanel class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
              <div class="sm:flex sm:items-start">
                <!-- Ícone -->
                <div :class="[
                  'mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full sm:mx-0 sm:h-10 sm:w-10',
                  iconBgClass
                ]">
                  <component :is="iconComponent" :class="['h-6 w-6', iconClass]" />
                </div>
                
                <!-- Conteúdo -->
                <div class="mt-3 text-center sm:ml-4 sm:mt-0 sm:text-left">
                  <DialogTitle as="h3" class="text-base font-semibold leading-6 text-gray-900">
                    {{ title }}
                  </DialogTitle>
                  
                  <div class="mt-2">
                    <p class="text-sm text-gray-500">
                      {{ message }}
                    </p>
                  </div>
                </div>
              </div>
              
              <!-- Botões -->
              <div class="mt-5 sm:mt-4 sm:flex sm:flex-row-reverse">
                <button
                  type="button"
                  @click="$emit('confirm')"
                  :class="[
                    'inline-flex w-full justify-center rounded-md px-3 py-2 text-sm font-semibold text-white shadow-sm sm:ml-3 sm:w-auto',
                    confirmButtonClass,
                    'hover:opacity-90 focus:outline-none focus:ring-2 focus:ring-offset-2',
                    confirmFocusClass
                  ]"
                  :disabled="isLoading"
                >
                  <div v-if="isLoading" class="flex items-center">
                    <div class="spinner w-4 h-4 mr-2"></div>
                    Processando...
                  </div>
                  <span v-else>
                    {{ confirmText }}
                  </span>
                </button>
                
                <button
                  type="button"
                  @click="$emit('cancel')"
                  class="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto"
                  :disabled="isLoading"
                >
                  {{ cancelText }}
                </button>
              </div>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue'
import {
  ExclamationTriangleIcon,
  InformationCircleIcon,
  CheckCircleIcon,
  XCircleIcon
} from '@heroicons/vue/24/outline'

interface Props {
  title: string
  message: string
  confirmText?: string
  cancelText?: string
  type?: 'warning' | 'danger' | 'info' | 'success'
  confirmClass?: string
  isLoading?: boolean
}

interface Emits {
  (e: 'confirm'): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  confirmText: 'Confirmar',
  cancelText: 'Cancelar',
  type: 'warning',
  confirmClass: '',
  isLoading: false
})

const emit = defineEmits<Emits>()

// Computed para ícones e classes baseados no tipo
const iconComponent = computed(() => {
  switch (props.type) {
    case 'danger':
      return XCircleIcon
    case 'info':
      return InformationCircleIcon
    case 'success':
      return CheckCircleIcon
    case 'warning':
    default:
      return ExclamationTriangleIcon
  }
})

const iconClass = computed(() => {
  switch (props.type) {
    case 'danger':
      return 'text-error-600'
    case 'info':
      return 'text-primary-600'
    case 'success':
      return 'text-success-600'
    case 'warning':
    default:
      return 'text-warning-600'
  }
})

const iconBgClass = computed(() => {
  switch (props.type) {
    case 'danger':
      return 'bg-error-100'
    case 'info':
      return 'bg-primary-100'
    case 'success':
      return 'bg-success-100'
    case 'warning':
    default:
      return 'bg-warning-100'
  }
})

const confirmButtonClass = computed(() => {
  // Se uma classe customizada foi fornecida, usar ela
  if (props.confirmClass) {
    if (props.confirmClass.includes('btn-')) {
      // Se é uma classe de botão do nosso sistema, converter para classes Tailwind
      switch (props.confirmClass) {
        case 'btn-error':
          return 'bg-error-600 hover:bg-error-700'
        case 'btn-warning':
          return 'bg-warning-600 hover:bg-warning-700'
        case 'btn-success':
          return 'bg-success-600 hover:bg-success-700'
        case 'btn-primary':
          return 'bg-primary-600 hover:bg-primary-700'
        default:
          return 'bg-primary-600 hover:bg-primary-700'
      }
    }
    return props.confirmClass
  }
  
  // Classes padrão baseadas no tipo
  switch (props.type) {
    case 'danger':
      return 'bg-error-600 hover:bg-error-700'
    case 'info':
      return 'bg-primary-600 hover:bg-primary-700'
    case 'success':
      return 'bg-success-600 hover:bg-success-700'
    case 'warning':
    default:
      return 'bg-warning-600 hover:bg-warning-700'
  }
})

const confirmFocusClass = computed(() => {
  switch (props.type) {
    case 'danger':
      return 'focus:ring-error-500'
    case 'info':
      return 'focus:ring-primary-500'
    case 'success':
      return 'focus:ring-success-500'
    case 'warning':
    default:
      return 'focus:ring-warning-500'
  }
})
</script>
