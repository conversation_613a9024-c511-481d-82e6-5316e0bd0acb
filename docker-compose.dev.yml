version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: cardapio_postgres_dev
    environment:
      POSTGRES_DB: cardapio_dev
      POSTGRES_USER: cardapio_user
      POSTGRES_PASSWORD: cardapio_password
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./docker/postgres/init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - cardapio_dev_network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: cardapio_redis_dev
    command: redis-server --appendonly yes
    volumes:
      - redis_dev_data:/data
    ports:
      - "6379:6379"
    networks:
      - cardapio_dev_network

  # MinIO Object Storage
  minio:
    image: minio/minio:latest
    container_name: cardapio_minio_dev
    command: server /data --console-address ":9001"
    environment:
      MINIO_ROOT_USER: cardapio_minio
      MINIO_ROOT_PASSWORD: cardapio_minio_password
    volumes:
      - minio_dev_data:/data
    ports:
      - "9000:9000"
      - "9001:9001"
    networks:
      - cardapio_dev_network

  # Backend API (Development mode with hot reload)
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile.dev
    container_name: cardapio_backend_dev
    environment:
      - DATABASE_URL=**********************************************************/cardapio_dev
      - REDIS_URL=redis://redis:6379
      - MINIO_ENDPOINT=minio:9000
      - MINIO_ACCESS_KEY=cardapio_minio
      - MINIO_SECRET_KEY=cardapio_minio_password
      - JWT_SECRET_KEY=dev-jwt-secret-key
      - ENVIRONMENT=development
      - DEBUG=true
    volumes:
      - ./backend:/app
    ports:
      - "8000:8000"
    depends_on:
      - postgres
      - redis
      - minio
    networks:
      - cardapio_dev_network
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  # Frontend Web App (Development mode with hot reload)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile.dev
    container_name: cardapio_frontend_dev
    environment:
      - VITE_API_URL=http://localhost:8000
      - VITE_ENVIRONMENT=development
    volumes:
      - ./frontend:/app
      - /app/node_modules
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - cardapio_dev_network
    command: npm run dev

volumes:
  postgres_dev_data:
  redis_dev_data:
  minio_dev_data:

networks:
  cardapio_dev_network:
    driver: bridge
