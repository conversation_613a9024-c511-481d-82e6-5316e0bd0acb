<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <header class="bg-white shadow-sm border-b border-gray-200">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="flex justify-between items-center h-16">
          <!-- Logo -->
          <router-link to="/" class="flex items-center space-x-2">
            <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
              <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
            </div>
            <span class="text-xl font-bold text-gray-900">Cardápio Digital</span>
          </router-link>

          <!-- Navegação desktop -->
          <nav class="hidden md:flex space-x-8">
            <router-link
              to="/"
              :class="[
                'nav-link',
                $route.name === 'home' ? 'nav-link-active' : ''
              ]"
            >
              Início
            </router-link>
            
            <router-link
              to="/sobre"
              :class="[
                'nav-link',
                $route.name === 'about' ? 'nav-link-active' : ''
              ]"
            >
              Sobre
            </router-link>
            
            <a
              href="#features"
              class="nav-link"
              @click="scrollToSection('features')"
            >
              Recursos
            </a>
            
            <a
              href="#pricing"
              class="nav-link"
              @click="scrollToSection('pricing')"
            >
              Preços
            </a>
          </nav>

          <!-- Ações do usuário -->
          <div class="flex items-center space-x-4">
            <!-- Usuário autenticado -->
            <div v-if="isAuthenticated" class="flex items-center space-x-4">
              <router-link
                to="/dashboard"
                class="btn btn-outline btn-sm"
              >
                Dashboard
              </router-link>
              
              <!-- Menu do usuário -->
              <Menu as="div" class="relative">
                <div>
                  <MenuButton class="flex items-center text-sm rounded-full focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500">
                    <img 
                      v-if="user?.avatar_url" 
                      :src="user.avatar_url" 
                      :alt="user.full_name"
                      class="h-8 w-8 rounded-full object-cover"
                    >
                    <div v-else class="h-8 w-8 rounded-full bg-primary-100 flex items-center justify-center">
                      <span class="text-sm font-medium text-primary-600">
                        {{ user?.full_name?.charAt(0).toUpperCase() }}
                      </span>
                    </div>
                  </MenuButton>
                </div>
                
                <transition
                  enter-active-class="transition ease-out duration-100"
                  enter-from-class="transform opacity-0 scale-95"
                  enter-to-class="transform opacity-100 scale-100"
                  leave-active-class="transition ease-in duration-75"
                  leave-from-class="transform opacity-100 scale-100"
                  leave-to-class="transform opacity-0 scale-95"
                >
                  <MenuItems class="origin-top-right absolute right-0 mt-2 w-48 rounded-md shadow-lg py-1 bg-white ring-1 ring-black ring-opacity-5 focus:outline-none">
                    <MenuItem v-slot="{ active }">
                      <router-link
                        to="/dashboard/profile"
                        :class="[active ? 'bg-gray-100' : '', 'block px-4 py-2 text-sm text-gray-700']"
                      >
                        Perfil
                      </router-link>
                    </MenuItem>
                    <MenuItem v-slot="{ active }">
                      <button
                        @click="handleLogout"
                        :class="[active ? 'bg-gray-100' : '', 'block w-full text-left px-4 py-2 text-sm text-gray-700']"
                      >
                        Sair
                      </button>
                    </MenuItem>
                  </MenuItems>
                </transition>
              </Menu>
            </div>

            <!-- Usuário não autenticado -->
            <div v-else class="flex items-center space-x-4">
              <router-link
                to="/auth/login"
                class="text-gray-600 hover:text-gray-900 text-sm font-medium transition-colors duration-200"
              >
                Entrar
              </router-link>
              
              <router-link
                to="/auth/register"
                class="btn btn-primary btn-sm"
              >
                Começar grátis
              </router-link>
            </div>

            <!-- Menu mobile -->
            <button
              @click="mobileMenuOpen = !mobileMenuOpen"
              class="md:hidden p-2 rounded-md text-gray-400 hover:text-gray-500 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary-500"
            >
              <span class="sr-only">Abrir menu</span>
              <Bars3Icon v-if="!mobileMenuOpen" class="h-6 w-6" />
              <XMarkIcon v-else class="h-6 w-6" />
            </button>
          </div>
        </div>

        <!-- Menu mobile -->
        <div v-if="mobileMenuOpen" class="md:hidden border-t border-gray-200 pt-4 pb-3">
          <div class="space-y-1">
            <router-link
              to="/"
              class="block px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md"
              @click="mobileMenuOpen = false"
            >
              Início
            </router-link>
            
            <router-link
              to="/sobre"
              class="block px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md"
              @click="mobileMenuOpen = false"
            >
              Sobre
            </router-link>
            
            <a
              href="#features"
              class="block px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md"
              @click="scrollToSection('features'); mobileMenuOpen = false"
            >
              Recursos
            </a>
            
            <a
              href="#pricing"
              class="block px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md"
              @click="scrollToSection('pricing'); mobileMenuOpen = false"
            >
              Preços
            </a>
          </div>

          <!-- Ações mobile -->
          <div class="mt-4 pt-4 border-t border-gray-200">
            <div v-if="!isAuthenticated" class="space-y-2">
              <router-link
                to="/auth/login"
                class="block w-full text-left px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md"
                @click="mobileMenuOpen = false"
              >
                Entrar
              </router-link>
              
              <router-link
                to="/auth/register"
                class="block w-full text-center px-3 py-2 text-base font-medium bg-primary-600 text-white hover:bg-primary-700 rounded-md"
                @click="mobileMenuOpen = false"
              >
                Começar grátis
              </router-link>
            </div>
            
            <div v-else class="space-y-2">
              <router-link
                to="/dashboard"
                class="block w-full text-left px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md"
                @click="mobileMenuOpen = false"
              >
                Dashboard
              </router-link>
              
              <button
                @click="handleLogout; mobileMenuOpen = false"
                class="block w-full text-left px-3 py-2 text-base font-medium text-gray-600 hover:text-gray-900 hover:bg-gray-50 rounded-md"
              >
                Sair
              </button>
            </div>
          </div>
        </div>
      </div>
    </header>

    <!-- Conteúdo principal -->
    <main>
      <router-view />
    </main>

    <!-- Footer -->
    <footer class="bg-white border-t border-gray-200">
      <div class="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-8">
          <!-- Logo e descrição -->
          <div class="col-span-1 md:col-span-2">
            <div class="flex items-center space-x-2 mb-4">
              <div class="w-8 h-8 bg-primary-600 rounded-lg flex items-center justify-center">
                <svg class="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                </svg>
              </div>
              <span class="text-xl font-bold text-gray-900">Cardápio Digital</span>
            </div>
            <p class="text-gray-600 max-w-md">
              A solução completa para digitalizar o cardápio do seu restaurante e gerenciar pedidos online.
            </p>
          </div>

          <!-- Links -->
          <div>
            <h3 class="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">
              Produto
            </h3>
            <ul class="space-y-2">
              <li>
                <a href="#features" class="text-gray-600 hover:text-gray-900 transition-colors duration-200">
                  Recursos
                </a>
              </li>
              <li>
                <a href="#pricing" class="text-gray-600 hover:text-gray-900 transition-colors duration-200">
                  Preços
                </a>
              </li>
              <li>
                <router-link to="/sobre" class="text-gray-600 hover:text-gray-900 transition-colors duration-200">
                  Sobre
                </router-link>
              </li>
            </ul>
          </div>

          <!-- Suporte -->
          <div>
            <h3 class="text-sm font-semibold text-gray-900 tracking-wider uppercase mb-4">
              Suporte
            </h3>
            <ul class="space-y-2">
              <li>
                <a href="#" class="text-gray-600 hover:text-gray-900 transition-colors duration-200">
                  Central de Ajuda
                </a>
              </li>
              <li>
                <a href="#" class="text-gray-600 hover:text-gray-900 transition-colors duration-200">
                  Contato
                </a>
              </li>
              <li>
                <a href="#" class="text-gray-600 hover:text-gray-900 transition-colors duration-200">
                  Termos de Uso
                </a>
              </li>
              <li>
                <a href="#" class="text-gray-600 hover:text-gray-900 transition-colors duration-200">
                  Privacidade
                </a>
              </li>
            </ul>
          </div>
        </div>

        <!-- Copyright -->
        <div class="mt-8 pt-8 border-t border-gray-200">
          <p class="text-center text-gray-500 text-sm">
            &copy; 2024 Cardápio Digital. Todos os direitos reservados.
          </p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import { Menu, MenuButton, MenuItem, MenuItems } from '@headlessui/vue'
import { Bars3Icon, XMarkIcon } from '@heroicons/vue/24/outline'

import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

const mobileMenuOpen = ref(false)

const isAuthenticated = computed(() => authStore.isAuthenticated)
const user = computed(() => authStore.user)

const handleLogout = async () => {
  try {
    await authStore.logout()
    router.push('/')
  } catch (error) {
    console.error('Erro ao fazer logout:', error)
  }
}

const scrollToSection = (sectionId: string) => {
  const element = document.getElementById(sectionId)
  if (element) {
    element.scrollIntoView({ behavior: 'smooth' })
  }
}
</script>
