<template>
  <div class="space-y-6">
    <!-- Header com saudação -->
    <div class="bg-white shadow-soft rounded-lg p-6">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-2xl font-bold text-gray-900">
            <PERSON><PERSON><PERSON>, {{ user?.full_name?.split(' ')[0] }}! 👋
          </h1>
          <p class="text-gray-600 mt-1">
            Bem-vindo ao painel do {{ establishment?.name || 'seu estabelecimento' }}
          </p>
        </div>
        
        <div v-if="establishment" class="flex items-center space-x-4">
          <!-- Status do estabelecimento -->
          <div class="flex items-center space-x-2">
            <div :class="[
              'w-3 h-3 rounded-full',
              establishment.is_active ? 'bg-success-400' : 'bg-gray-400'
            ]"></div>
            <span class="text-sm font-medium text-gray-700">
              {{ establishment.is_active ? 'Ativo' : 'Inativo' }}
            </span>
          </div>
          
          <!-- Toggle de pedidos -->
          <button
            @click="toggleOrders"
            :disabled="isTogglingOrders"
            :class="[
              'relative inline-flex h-6 w-11 flex-shrink-0 cursor-pointer rounded-full border-2 border-transparent transition-colors duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-offset-2',
              establishment.accepts_orders ? 'bg-primary-600' : 'bg-gray-200'
            ]"
          >
            <span
              :class="[
                'pointer-events-none inline-block h-5 w-5 transform rounded-full bg-white shadow ring-0 transition duration-200 ease-in-out',
                establishment.accepts_orders ? 'translate-x-5' : 'translate-x-0'
              ]"
            />
          </button>
          <span class="text-sm font-medium text-gray-700">
            {{ establishment.accepts_orders ? 'Aceitando pedidos' : 'Pedidos pausados' }}
          </span>
        </div>
      </div>
    </div>

    <!-- Cards de estatísticas -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
      <!-- Total de pedidos -->
      <div class="card">
        <div class="card-body">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <ShoppingBagIcon class="h-8 w-8 text-primary-600" />
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">
                  Total de Pedidos
                </dt>
                <dd class="text-lg font-medium text-gray-900">
                  {{ stats?.total_orders || 0 }}
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- Receita total -->
      <div class="card">
        <div class="card-body">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <CurrencyDollarIcon class="h-8 w-8 text-success-600" />
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">
                  Receita Total
                </dt>
                <dd class="text-lg font-medium text-gray-900">
                  {{ formatCurrency(stats?.total_revenue || 0) }}
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- Clientes -->
      <div class="card">
        <div class="card-body">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <UsersIcon class="h-8 w-8 text-warning-600" />
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">
                  Total de Clientes
                </dt>
                <dd class="text-lg font-medium text-gray-900">
                  {{ stats?.total_customers || 0 }}
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <!-- Itens do menu -->
      <div class="card">
        <div class="card-body">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <BookOpenIcon class="h-8 w-8 text-secondary-600" />
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">
                  Itens do Menu
                </dt>
                <dd class="text-lg font-medium text-gray-900">
                  {{ stats?.total_menu_items || 0 }}
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Estatísticas de hoje -->
    <div class="grid grid-cols-1 gap-5 lg:grid-cols-2">
      <!-- Pedidos de hoje -->
      <div class="card">
        <div class="card-header">
          <h3 class="text-lg font-medium text-gray-900">Hoje</h3>
        </div>
        <div class="card-body">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <dt class="text-sm font-medium text-gray-500">Pedidos</dt>
              <dd class="text-2xl font-bold text-gray-900">
                {{ stats?.orders_today || 0 }}
              </dd>
            </div>
            <div>
              <dt class="text-sm font-medium text-gray-500">Receita</dt>
              <dd class="text-2xl font-bold text-gray-900">
                {{ formatCurrency(stats?.revenue_today || 0) }}
              </dd>
            </div>
          </div>
        </div>
      </div>

      <!-- Ações rápidas -->
      <div class="card">
        <div class="card-header">
          <h3 class="text-lg font-medium text-gray-900">Ações Rápidas</h3>
        </div>
        <div class="card-body">
          <div class="grid grid-cols-2 gap-4">
            <router-link
              to="/dashboard/menu"
              class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
            >
              <BookOpenIcon class="h-8 w-8 text-primary-600 mb-2" />
              <span class="text-sm font-medium text-gray-900">Gerenciar Menu</span>
            </router-link>
            
            <router-link
              to="/dashboard/orders"
              class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
            >
              <ShoppingBagIcon class="h-8 w-8 text-success-600 mb-2" />
              <span class="text-sm font-medium text-gray-900">Ver Pedidos</span>
            </router-link>
            
            <router-link
              v-if="canManageEstablishment"
              to="/dashboard/establishment"
              class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
            >
              <BuildingStorefrontIcon class="h-8 w-8 text-warning-600 mb-2" />
              <span class="text-sm font-medium text-gray-900">Configurações</span>
            </router-link>
            
            <router-link
              v-if="canManageEstablishment"
              to="/dashboard/reports"
              class="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors duration-200"
            >
              <ChartBarIcon class="h-8 w-8 text-secondary-600 mb-2" />
              <span class="text-sm font-medium text-gray-900">Relatórios</span>
            </router-link>
          </div>
        </div>
      </div>
    </div>

    <!-- Informações do estabelecimento -->
    <div v-if="establishment" class="card">
      <div class="card-header">
        <h3 class="text-lg font-medium text-gray-900">Informações do Estabelecimento</h3>
      </div>
      <div class="card-body">
        <div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
          <!-- Informações básicas -->
          <div>
            <h4 class="text-sm font-medium text-gray-900 mb-3">Dados Básicos</h4>
            <dl class="space-y-2">
              <div class="flex justify-between">
                <dt class="text-sm text-gray-500">Nome:</dt>
                <dd class="text-sm font-medium text-gray-900">{{ establishment.name }}</dd>
              </div>
              <div class="flex justify-between">
                <dt class="text-sm text-gray-500">Slug:</dt>
                <dd class="text-sm font-medium text-gray-900">{{ establishment.slug }}</dd>
              </div>
              <div class="flex justify-between">
                <dt class="text-sm text-gray-500">Email:</dt>
                <dd class="text-sm font-medium text-gray-900">{{ establishment.email }}</dd>
              </div>
              <div v-if="establishment.phone" class="flex justify-between">
                <dt class="text-sm text-gray-500">Telefone:</dt>
                <dd class="text-sm font-medium text-gray-900">{{ establishment.phone }}</dd>
              </div>
            </dl>
          </div>

          <!-- Configurações de delivery -->
          <div>
            <h4 class="text-sm font-medium text-gray-900 mb-3">Delivery</h4>
            <dl class="space-y-2">
              <div class="flex justify-between">
                <dt class="text-sm text-gray-500">Taxa de entrega:</dt>
                <dd class="text-sm font-medium text-gray-900">
                  {{ formatCurrency(establishment.delivery_fee) }}
                </dd>
              </div>
              <div class="flex justify-between">
                <dt class="text-sm text-gray-500">Pedido mínimo:</dt>
                <dd class="text-sm font-medium text-gray-900">
                  {{ formatCurrency(establishment.minimum_order) }}
                </dd>
              </div>
              <div class="flex justify-between">
                <dt class="text-sm text-gray-500">Distância máxima:</dt>
                <dd class="text-sm font-medium text-gray-900">
                  {{ establishment.max_delivery_distance }}km
                </dd>
              </div>
            </dl>
          </div>
        </div>

        <!-- Link público -->
        <div class="mt-6 pt-6 border-t border-gray-200">
          <div class="flex items-center justify-between">
            <div>
              <h4 class="text-sm font-medium text-gray-900">Cardápio Público</h4>
              <p class="text-sm text-gray-500">Compartilhe este link com seus clientes</p>
            </div>
            <div class="flex items-center space-x-2">
              <input
                :value="establishment.public_url"
                readonly
                class="input text-sm w-64"
              />
              <button
                @click="copyPublicUrl"
                class="btn btn-outline btn-sm"
              >
                <ClipboardIcon class="h-4 w-4" />
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Estado de loading -->
    <div v-if="isLoadingStats" class="flex justify-center py-12">
      <div class="spinner w-8 h-8"></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import {
  ShoppingBagIcon,
  CurrencyDollarIcon,
  UsersIcon,
  BookOpenIcon,
  BuildingStorefrontIcon,
  ChartBarIcon,
  ClipboardIcon
} from '@heroicons/vue/24/outline'
import { useToast } from 'vue-toastification'

import { useAuthStore } from '@/stores/auth'
import { useEstablishmentStore } from '@/stores/establishment'

const toast = useToast()
const authStore = useAuthStore()
const establishmentStore = useEstablishmentStore()

const isTogglingOrders = ref(false)

const user = computed(() => authStore.user)
const establishment = computed(() => establishmentStore.currentEstablishment)
const stats = computed(() => establishmentStore.stats)
const isLoadingStats = computed(() => establishmentStore.isLoadingStats)
const canManageEstablishment = computed(() => authStore.canManageEstablishment)

const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value)
}

const toggleOrders = async () => {
  if (!establishment.value) return
  
  isTogglingOrders.value = true
  
  try {
    await establishmentStore.toggleAcceptOrders()
    
    const message = establishment.value.accepts_orders 
      ? 'Agora você está aceitando pedidos!' 
      : 'Pedidos pausados temporariamente'
    
    toast.success(message)
  } catch (error) {
    console.error('Erro ao alterar status de pedidos:', error)
    toast.error('Erro ao alterar status de pedidos')
  } finally {
    isTogglingOrders.value = false
  }
}

const copyPublicUrl = async () => {
  if (!establishment.value?.public_url) return
  
  try {
    await navigator.clipboard.writeText(establishment.value.public_url)
    toast.success('Link copiado para a área de transferência!')
  } catch (error) {
    console.error('Erro ao copiar link:', error)
    toast.error('Erro ao copiar link')
  }
}

onMounted(async () => {
  // Carregar estatísticas se ainda não foram carregadas
  if (!stats.value) {
    try {
      await establishmentStore.fetchStats()
    } catch (error) {
      console.error('Erro ao carregar estatísticas:', error)
    }
  }
})
</script>
