"""
Cliente Redis para cache e filas
"""
import redis
import json
from typing import Any, Optional
from app.core.config import settings

# Cliente Redis global
redis_client = redis.Redis.from_url(
    settings.REDIS_URL,
    decode_responses=True,
    socket_connect_timeout=5,
    socket_timeout=5,
    retry_on_timeout=True
)


class RedisCache:
    """Classe para operações de cache no Redis"""
    
    def __init__(self, client: redis.Redis = redis_client):
        self.client = client
    
    async def get(self, key: str) -> Optional[Any]:
        """
        Obter valor do cache
        
        Args:
            key: Chave do cache
            
        Returns:
            Valor deserializado ou None se não encontrado
        """
        try:
            value = self.client.get(key)
            if value:
                return json.loads(value)
            return None
        except Exception:
            return None
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        expire: Optional[int] = None
    ) -> bool:
        """
        Definir valor no cache
        
        Args:
            key: Chave do cache
            value: Valor a ser armazenado
            expire: Tempo de expiração em segundos
            
        Returns:
            True se sucesso
        """
        try:
            serialized_value = json.dumps(value, default=str)
            return self.client.set(key, serialized_value, ex=expire)
        except Exception:
            return False
    
    async def delete(self, key: str) -> bool:
        """
        Remover valor do cache
        
        Args:
            key: Chave do cache
            
        Returns:
            True se removido
        """
        try:
            return bool(self.client.delete(key))
        except Exception:
            return False
    
    async def exists(self, key: str) -> bool:
        """
        Verificar se chave existe no cache
        
        Args:
            key: Chave do cache
            
        Returns:
            True se existe
        """
        try:
            return bool(self.client.exists(key))
        except Exception:
            return False
    
    async def increment(self, key: str, amount: int = 1) -> Optional[int]:
        """
        Incrementar valor numérico
        
        Args:
            key: Chave do cache
            amount: Quantidade a incrementar
            
        Returns:
            Novo valor ou None se erro
        """
        try:
            return self.client.incr(key, amount)
        except Exception:
            return None
    
    async def expire(self, key: str, seconds: int) -> bool:
        """
        Definir expiração para uma chave
        
        Args:
            key: Chave do cache
            seconds: Segundos até expirar
            
        Returns:
            True se sucesso
        """
        try:
            return bool(self.client.expire(key, seconds))
        except Exception:
            return False


# Instância global do cache
cache = RedisCache()
