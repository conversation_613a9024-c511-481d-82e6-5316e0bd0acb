<template>
  <div class="fixed bottom-4 right-4 z-50">
    <!-- Botão do carrinho -->
    <button
      @click="toggleCart"
      class="relative bg-primary-600 hover:bg-primary-700 text-white rounded-full p-4 shadow-lg transition-all duration-200 transform hover:scale-105"
    >
      <ShoppingCartIcon class="h-6 w-6" />
      
      <!-- Badge com quantidade -->
      <span
        v-if="totalItems > 0"
        class="absolute -top-2 -right-2 bg-red-500 text-white text-xs font-bold rounded-full h-6 w-6 flex items-center justify-center"
      >
        {{ totalItems > 99 ? '99+' : totalItems }}
      </span>
    </button>

    <!-- Mini carrinho expandido -->
    <Transition
      enter-active-class="transition ease-out duration-200"
      enter-from-class="opacity-0 translate-y-2"
      enter-to-class="opacity-100 translate-y-0"
      leave-active-class="transition ease-in duration-150"
      leave-from-class="opacity-100 translate-y-0"
      leave-to-class="opacity-0 translate-y-2"
    >
      <div
        v-if="showCart"
        class="absolute bottom-16 right-0 w-80 bg-white rounded-lg shadow-xl border border-gray-200 overflow-hidden"
      >
        <!-- Header -->
        <div class="bg-gray-50 px-4 py-3 border-b border-gray-200">
          <div class="flex items-center justify-between">
            <h3 class="text-lg font-medium text-gray-900">Seu Pedido</h3>
            <button
              @click="closeCart"
              class="text-gray-400 hover:text-gray-500"
            >
              <XMarkIcon class="h-5 w-5" />
            </button>
          </div>
        </div>

        <!-- Lista de itens -->
        <div class="max-h-64 overflow-y-auto">
          <div
            v-for="item in items"
            :key="item.id"
            class="px-4 py-3 border-b border-gray-100 last:border-b-0"
          >
            <div class="flex items-center justify-between">
              <div class="flex-1 min-w-0">
                <h4 class="text-sm font-medium text-gray-900 truncate">
                  {{ item.item_name }}
                </h4>
                <p v-if="item.notes" class="text-xs text-gray-500 mt-1">
                  {{ item.notes }}
                </p>
              </div>
              
              <div class="flex items-center space-x-2 ml-3">
                <!-- Controles de quantidade -->
                <button
                  @click="decreaseQuantity(item)"
                  class="w-6 h-6 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center"
                  :disabled="isUpdating"
                >
                  <MinusIcon class="h-3 w-3 text-gray-600" />
                </button>
                
                <span class="text-sm font-medium text-gray-900 min-w-[1.5rem] text-center">
                  {{ item.quantity }}
                </span>
                
                <button
                  @click="increaseQuantity(item)"
                  class="w-6 h-6 rounded-full bg-primary-100 hover:bg-primary-200 flex items-center justify-center"
                  :disabled="isUpdating"
                >
                  <PlusIcon class="h-3 w-3 text-primary-600" />
                </button>
              </div>
            </div>
            
            <div class="flex items-center justify-between mt-2">
              <span class="text-xs text-gray-500">
                {{ formatCurrency(item.unit_price) }} cada
              </span>
              <span class="text-sm font-medium text-gray-900">
                {{ formatCurrency(item.total_price) }}
              </span>
            </div>
          </div>
        </div>

        <!-- Resumo -->
        <div class="px-4 py-3 bg-gray-50 border-t border-gray-200">
          <div class="space-y-2">
            <div class="flex justify-between text-sm">
              <span class="text-gray-600">Subtotal</span>
              <span class="text-gray-900">{{ formatCurrency(subtotal) }}</span>
            </div>
            
            <div v-if="deliveryFee > 0" class="flex justify-between text-sm">
              <span class="text-gray-600">Taxa de entrega</span>
              <span class="text-gray-900">{{ formatCurrency(deliveryFee) }}</span>
            </div>
            
            <div v-if="serviceFee > 0" class="flex justify-between text-sm">
              <span class="text-gray-600">Taxa de serviço</span>
              <span class="text-gray-900">{{ formatCurrency(serviceFee) }}</span>
            </div>
            
            <div class="flex justify-between text-base font-medium border-t border-gray-200 pt-2">
              <span class="text-gray-900">Total</span>
              <span class="text-gray-900">{{ formatCurrency(total) }}</span>
            </div>
          </div>
        </div>

        <!-- Ações -->
        <div class="px-4 py-3 space-y-2">
          <button
            @click="goToCart"
            class="w-full btn btn-outline btn-sm"
          >
            Ver Carrinho Completo
          </button>
          
          <button
            @click="goToCheckout"
            class="w-full btn btn-primary btn-sm"
            :disabled="!isValidForCheckout"
          >
            Finalizar Pedido
          </button>
        </div>
      </div>
    </Transition>

    <!-- Overlay para fechar o carrinho -->
    <div
      v-if="showCart"
      class="fixed inset-0 z-40"
      @click="closeCart"
    ></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useRouter } from 'vue-router'
import {
  ShoppingCartIcon,
  XMarkIcon,
  PlusIcon,
  MinusIcon
} from '@heroicons/vue/24/outline'
import { useToast } from 'vue-toastification'

import { useCartStore } from '@/stores/cart'
import type { CartItem } from '@/types'

const router = useRouter()
const toast = useToast()
const cartStore = useCartStore()

// Estado local
const showCart = ref(false)
const isUpdating = ref(false)

// Computed
const items = computed(() => cartStore.items)
const totalItems = computed(() => cartStore.totalItems)
const subtotal = computed(() => cartStore.subtotal)
const deliveryFee = computed(() => cartStore.deliveryFee)
const serviceFee = computed(() => cartStore.serviceFee)
const total = computed(() => cartStore.total)
const isValidForCheckout = computed(() => cartStore.isValidForCheckout)

// Methods
const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value)
}

const toggleCart = () => {
  showCart.value = !showCart.value
}

const closeCart = () => {
  showCart.value = false
}

const increaseQuantity = async (item: CartItem) => {
  if (isUpdating.value || !cartStore.cart) return

  isUpdating.value = true

  try {
    await cartStore.updateItem(
      cartStore.cart.establishment_id,
      item.id,
      item.quantity + 1,
      item.notes
    )
  } catch (error: any) {
    toast.error(error.message || 'Erro ao atualizar item')
  } finally {
    isUpdating.value = false
  }
}

const decreaseQuantity = async (item: CartItem) => {
  if (isUpdating.value || !cartStore.cart) return

  isUpdating.value = true

  try {
    if (item.quantity <= 1) {
      await cartStore.removeItem(cartStore.cart.establishment_id, item.id)
      toast.success('Item removido do carrinho')
    } else {
      await cartStore.updateItem(
        cartStore.cart.establishment_id,
        item.id,
        item.quantity - 1,
        item.notes
      )
    }
  } catch (error: any) {
    toast.error(error.message || 'Erro ao atualizar item')
  } finally {
    isUpdating.value = false
  }
}

const goToCart = () => {
  closeCart()
  router.push('/cart')
}

const goToCheckout = () => {
  closeCart()
  router.push('/checkout')
}

// Fechar carrinho quando clicar fora
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.floating-cart')) {
    closeCart()
  }
}

// Auto-fechar após adicionar item
watch(() => totalItems.value, (newValue, oldValue) => {
  if (newValue > oldValue && !showCart.value) {
    // Mostrar brevemente quando item é adicionado
    showCart.value = true
    setTimeout(() => {
      if (showCart.value) {
        closeCart()
      }
    }, 2000)
  }
})
</script>

<style scoped>
.floating-cart {
  /* Classe para identificar o componente no handleClickOutside */
}
</style>
