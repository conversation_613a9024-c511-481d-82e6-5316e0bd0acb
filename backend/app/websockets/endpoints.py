"""
Endpoints WebSocket
"""
import json
from fastapi import WebSocket, WebSocketDisconnect, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import Optional

from app.database import get_db
from app.models.user import User
from app.models.order import Order
from app.api.deps import get_current_user_websocket
from app.websockets.manager import manager


async def websocket_establishment_endpoint(
    websocket: WebSocket,
    establishment_id: str,
    token: str,
    db: Session = Depends(get_db)
):
    """WebSocket para estabelecimento (dashboard em tempo real)"""
    
    try:
        # Autenticar usuário
        user = await get_current_user_websocket(token, db)
        
        if not user:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
            return
        
        # Verificar se o usuário pertence ao estabelecimento
        if user.establishment_id != establishment_id:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
            return
        
        # Conectar
        await manager.connect(
            websocket,
            user=user,
            establishment_id=establishment_id,
            connection_type="establishment_dashboard"
        )
        
        try:
            while True:
                # Aguardar mensagens do cliente
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # Processar mensagens
                await handle_establishment_message(websocket, message, user, db)
                
        except WebSocketDisconnect:
            manager.disconnect(websocket)
            
    except Exception as e:
        print(f"Erro no WebSocket do estabelecimento: {str(e)}")
        await websocket.close(code=status.WS_1011_INTERNAL_ERROR)


async def websocket_order_tracking_endpoint(
    websocket: WebSocket,
    order_id: str,
    token: Optional[str] = None,
    db: Session = Depends(get_db)
):
    """WebSocket para acompanhamento de pedido (público)"""
    
    try:
        user = None
        
        # Autenticar se token fornecido
        if token:
            user = await get_current_user_websocket(token, db)
        
        # Buscar pedido
        order = db.query(Order).filter(Order.id == order_id).first()
        
        if not order:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
            return
        
        # Verificar permissões se usuário autenticado
        if user and user.role == "customer" and order.user_id != user.id:
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
            return
        
        # Conectar
        await manager.connect(
            websocket,
            user=user,
            order_id=order_id,
            connection_type="order_tracking"
        )
        
        # Enviar status atual do pedido
        await manager.send_personal_message(websocket, {
            "type": "order_status",
            "data": {
                "order_id": order.id,
                "order_number": order.order_number,
                "status": order.status.value,
                "estimated_prep_time": order.estimated_prep_time,
                "estimated_delivery_time": order.estimated_delivery_time
            }
        })
        
        try:
            while True:
                # Aguardar mensagens do cliente
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # Processar mensagens de tracking
                await handle_tracking_message(websocket, message, order, user)
                
        except WebSocketDisconnect:
            manager.disconnect(websocket)
            
    except Exception as e:
        print(f"Erro no WebSocket de tracking: {str(e)}")
        await websocket.close(code=status.WS_1011_INTERNAL_ERROR)


async def websocket_customer_endpoint(
    websocket: WebSocket,
    token: str,
    db: Session = Depends(get_db)
):
    """WebSocket para cliente (notificações de pedidos)"""
    
    try:
        # Autenticar usuário
        user = await get_current_user_websocket(token, db)
        
        if not user or user.role != "customer":
            await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
            return
        
        # Conectar
        await manager.connect(
            websocket,
            user=user,
            connection_type="customer_notifications"
        )
        
        try:
            while True:
                # Aguardar mensagens do cliente
                data = await websocket.receive_text()
                message = json.loads(data)
                
                # Processar mensagens do cliente
                await handle_customer_message(websocket, message, user, db)
                
        except WebSocketDisconnect:
            manager.disconnect(websocket)
            
    except Exception as e:
        print(f"Erro no WebSocket do cliente: {str(e)}")
        await websocket.close(code=status.WS_1011_INTERNAL_ERROR)


async def handle_establishment_message(
    websocket: WebSocket,
    message: dict,
    user: User,
    db: Session
):
    """Processar mensagens do estabelecimento"""
    
    message_type = message.get("type")
    
    if message_type == "get_stats":
        # Enviar estatísticas atualizadas
        from app.services.order import OrderService
        order_service = OrderService()
        
        stats = await order_service.get_order_stats(user.establishment_id, db)
        
        await manager.send_personal_message(websocket, {
            "type": "stats_response",
            "data": stats.dict()
        })
    
    elif message_type == "get_active_orders":
        # Enviar pedidos ativos
        from app.services.order import OrderService
        order_service = OrderService()
        
        orders = await order_service.get_active_orders(user.establishment_id, db)
        
        orders_data = []
        for order in orders:
            orders_data.append({
                "id": order.id,
                "order_number": order.order_number,
                "customer_name": order.customer_name,
                "status": order.status.value,
                "order_type": order.order_type.value,
                "total_amount": float(order.total_amount),
                "created_at": order.created_at.isoformat()
            })
        
        await manager.send_personal_message(websocket, {
            "type": "active_orders_response",
            "data": orders_data
        })
    
    elif message_type == "ping":
        # Responder ping
        await manager.send_personal_message(websocket, {
            "type": "pong"
        })


async def handle_tracking_message(
    websocket: WebSocket,
    message: dict,
    order: Order,
    user: Optional[User]
):
    """Processar mensagens de tracking"""
    
    message_type = message.get("type")
    
    if message_type == "get_status":
        # Enviar status atual
        await manager.send_personal_message(websocket, {
            "type": "order_status",
            "data": {
                "order_id": order.id,
                "order_number": order.order_number,
                "status": order.status.value,
                "estimated_prep_time": order.estimated_prep_time,
                "estimated_delivery_time": order.estimated_delivery_time,
                "created_at": order.created_at.isoformat(),
                "updated_at": order.updated_at.isoformat()
            }
        })
    
    elif message_type == "ping":
        # Responder ping
        await manager.send_personal_message(websocket, {
            "type": "pong"
        })


async def handle_customer_message(
    websocket: WebSocket,
    message: dict,
    user: User,
    db: Session
):
    """Processar mensagens do cliente"""
    
    message_type = message.get("type")
    
    if message_type == "get_my_orders":
        # Enviar pedidos do cliente
        orders = db.query(Order).filter(
            Order.user_id == user.id
        ).order_by(Order.created_at.desc()).limit(10).all()
        
        orders_data = []
        for order in orders:
            orders_data.append({
                "id": order.id,
                "order_number": order.order_number,
                "establishment_name": order.establishment.name,
                "status": order.status.value,
                "total_amount": float(order.total_amount),
                "created_at": order.created_at.isoformat()
            })
        
        await manager.send_personal_message(websocket, {
            "type": "my_orders_response",
            "data": orders_data
        })
    
    elif message_type == "ping":
        # Responder ping
        await manager.send_personal_message(websocket, {
            "type": "pong"
        })


# Funções auxiliares para notificações
async def notify_new_order(order: Order):
    """Notificar sobre novo pedido via WebSocket"""
    await manager.notify_new_order(order)


async def notify_order_status_change(order: Order, old_status: str):
    """Notificar sobre mudança de status via WebSocket"""
    await manager.notify_order_status_change(order, old_status)


async def notify_payment_status_change(order: Order, payment_status: str):
    """Notificar sobre mudança de pagamento via WebSocket"""
    await manager.notify_payment_status_change(order, payment_status)


async def send_order_update(order: Order):
    """Enviar atualização completa do pedido via WebSocket"""
    await manager.send_order_update(order)
