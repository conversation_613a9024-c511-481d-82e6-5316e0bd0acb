/**
 * Store para acompanhamento de pedidos em tempo real
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { 
  Order, 
  OrderTracking,
  OrderTimeline 
} from '@/types'
import { ApiService } from '@/services/api'

export const useOrderTrackingStore = defineStore('orderTracking', () => {
  // Estado
  const currentOrder = ref<Order | null>(null)
  const orderTracking = ref<OrderTracking | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const websocket = ref<WebSocket | null>(null)
  const isConnected = ref(false)

  // Computed
  const orderStatus = computed(() => currentOrder.value?.status || '')
  
  const estimatedTime = computed(() => {
    if (!currentOrder.value) return null
    
    const order = currentOrder.value
    if (order.order_type === 'delivery') {
      return order.estimated_delivery_time
    } else {
      return order.estimated_prep_time
    }
  })

  const timeline = computed(() => orderTracking.value?.timeline || [])
  
  const currentStep = computed(() => orderTracking.value?.current_step || 0)
  
  const totalSteps = computed(() => orderTracking.value?.total_steps || 0)
  
  const progressPercentage = computed(() => {
    if (totalSteps.value === 0) return 0
    return Math.round((currentStep.value / totalSteps.value) * 100)
  })

  const isCompleted = computed(() => {
    return ['delivered', 'cancelled'].includes(orderStatus.value)
  })

  const statusMessage = computed(() => {
    const messages: Record<string, string> = {
      pending: 'Aguardando confirmação',
      confirmed: 'Pedido confirmado',
      preparing: 'Preparando seu pedido',
      ready: 'Pedido pronto',
      out_for_delivery: 'Saiu para entrega',
      delivered: 'Pedido entregue',
      cancelled: 'Pedido cancelado'
    }
    
    return messages[orderStatus.value] || 'Status desconhecido'
  })

  // Actions
  const fetchOrder = async (orderId: string) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await ApiService.get<Order>(`/orders/${orderId}`)
      currentOrder.value = response
      return response
    } catch (err: any) {
      error.value = err.response?.data?.detail || 'Pedido não encontrado'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const fetchOrderTracking = async (orderId: string) => {
    isLoading.value = true
    error.value = null

    try {
      const response = await ApiService.get<OrderTracking>(`/orders/${orderId}/tracking`)
      orderTracking.value = response
      return response
    } catch (err: any) {
      error.value = err.response?.data?.detail || 'Erro ao carregar acompanhamento'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const connectWebSocket = (orderId: string, token?: string) => {
    if (websocket.value) {
      websocket.value.close()
    }

    const wsUrl = `${import.meta.env.VITE_WS_URL}/ws/order/${orderId}`
    const url = token ? `${wsUrl}?token=${token}` : wsUrl

    websocket.value = new WebSocket(url)

    websocket.value.onopen = () => {
      isConnected.value = true
      console.log('WebSocket conectado para pedido:', orderId)
    }

    websocket.value.onmessage = (event) => {
      try {
        const data = JSON.parse(event.data)
        handleWebSocketMessage(data)
      } catch (error) {
        console.error('Erro ao processar mensagem WebSocket:', error)
      }
    }

    websocket.value.onclose = () => {
      isConnected.value = false
      console.log('WebSocket desconectado')
      
      // Tentar reconectar após 5 segundos se não foi fechado intencionalmente
      if (!isCompleted.value) {
        setTimeout(() => {
          if (!isConnected.value && !isCompleted.value) {
            connectWebSocket(orderId, token)
          }
        }, 5000)
      }
    }

    websocket.value.onerror = (error) => {
      console.error('Erro no WebSocket:', error)
      isConnected.value = false
    }
  }

  const disconnectWebSocket = () => {
    if (websocket.value) {
      websocket.value.close()
      websocket.value = null
    }
    isConnected.value = false
  }

  const handleWebSocketMessage = (data: any) => {
    switch (data.type) {
      case 'order_status':
        if (currentOrder.value && data.data.order_id === currentOrder.value.id) {
          currentOrder.value.status = data.data.status
          currentOrder.value.estimated_prep_time = data.data.estimated_prep_time
          currentOrder.value.estimated_delivery_time = data.data.estimated_delivery_time
        }
        break

      case 'order_status_changed':
        if (currentOrder.value && data.data.order_id === currentOrder.value.id) {
          currentOrder.value.status = data.data.new_status
          currentOrder.value.updated_at = data.data.updated_at
          
          // Atualizar tracking
          if (orderTracking.value) {
            fetchOrderTracking(currentOrder.value.id)
          }
        }
        break

      case 'order_updated':
        if (currentOrder.value && data.data.order_id === currentOrder.value.id) {
          // Atualizar dados do pedido
          Object.assign(currentOrder.value, data.data)
        }
        break

      case 'pong':
        // Resposta ao ping - manter conexão viva
        break

      default:
        console.log('Mensagem WebSocket não tratada:', data)
    }
  }

  const sendWebSocketMessage = (message: any) => {
    if (websocket.value && isConnected.value) {
      websocket.value.send(JSON.stringify(message))
    }
  }

  const requestStatusUpdate = () => {
    sendWebSocketMessage({ type: 'get_status' })
  }

  const startTracking = async (orderId: string, token?: string) => {
    try {
      // Buscar dados iniciais
      await Promise.all([
        fetchOrder(orderId),
        fetchOrderTracking(orderId)
      ])

      // Conectar WebSocket para atualizações em tempo real
      connectWebSocket(orderId, token)
      
    } catch (error) {
      console.error('Erro ao iniciar tracking:', error)
      throw error
    }
  }

  const stopTracking = () => {
    disconnectWebSocket()
    currentOrder.value = null
    orderTracking.value = null
  }

  const formatTimelineDate = (dateString: string): string => {
    return new Intl.DateTimeFormat('pt-BR', {
      day: '2-digit',
      month: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(dateString))
  }

  const getTimelineIcon = (status: string): string => {
    const icons: Record<string, string> = {
      pending: 'clock',
      confirmed: 'check-circle',
      preparing: 'fire',
      ready: 'check-circle',
      out_for_delivery: 'truck',
      delivered: 'check-circle',
      cancelled: 'x-circle'
    }
    
    return icons[status] || 'circle'
  }

  const getTimelineColor = (status: string, isCompleted: boolean, isCurrent: boolean): string => {
    if (status === 'cancelled') return 'text-red-500'
    if (isCompleted) return 'text-green-500'
    if (isCurrent) return 'text-blue-500'
    return 'text-gray-400'
  }

  // Reset store
  const $reset = () => {
    disconnectWebSocket()
    currentOrder.value = null
    orderTracking.value = null
    isLoading.value = false
    error.value = null
  }

  return {
    // Estado
    currentOrder,
    orderTracking,
    isLoading,
    error,
    isConnected,

    // Computed
    orderStatus,
    estimatedTime,
    timeline,
    currentStep,
    totalSteps,
    progressPercentage,
    isCompleted,
    statusMessage,

    // Actions
    fetchOrder,
    fetchOrderTracking,
    connectWebSocket,
    disconnectWebSocket,
    startTracking,
    stopTracking,
    requestStatusUpdate,
    formatTimelineDate,
    getTimelineIcon,
    getTimelineColor,
    $reset
  }
})
