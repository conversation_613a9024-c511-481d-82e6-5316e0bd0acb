"""
Schemas para carrinho de compras
"""
from pydantic import BaseModel, Field, validator
from typing import Optional, List
from datetime import datetime
from decimal import Decimal


class CartItemBase(BaseModel):
    """Schema base para item do carrinho"""
    menu_item_id: str = Field(..., description="ID do item do menu")
    quantity: int = Field(..., ge=1, description="Quantidade do item")
    notes: Optional[str] = Field(None, description="Observações do item")


class CartItemCreate(CartItemBase):
    """Schema para criação de item do carrinho"""
    pass


class CartItemUpdate(BaseModel):
    """Schema para atualização de item do carrinho"""
    quantity: Optional[int] = Field(None, ge=1, description="Nova quantidade")
    notes: Optional[str] = Field(None, description="Novas observações")


class CartItemResponse(CartItemBase):
    """Schema de resposta para item do carrinho"""
    id: str
    cart_id: str
    unit_price: Decimal
    total_price: Decimal
    item_name: str
    item_description: Optional[str]
    created_at: datetime
    updated_at: datetime
    
    class Config:
        from_attributes = True


class CartBase(BaseModel):
    """Schema base para carrinho"""
    order_type: str = Field("delivery", description="Tipo do pedido")
    table_number: Optional[int] = Field(None, description="Número da mesa")
    
    # Endereço de entrega
    delivery_address: Optional[str] = Field(None, description="Endereço de entrega")
    delivery_city: Optional[str] = Field(None, description="Cidade")
    delivery_state: Optional[str] = Field(None, description="Estado")
    delivery_zip_code: Optional[str] = Field(None, description="CEP")
    delivery_latitude: Optional[float] = Field(None, description="Latitude")
    delivery_longitude: Optional[float] = Field(None, description="Longitude")
    
    # Informações do cliente
    customer_name: Optional[str] = Field(None, description="Nome do cliente")
    customer_phone: Optional[str] = Field(None, description="Telefone do cliente")
    customer_email: Optional[str] = Field(None, description="Email do cliente")
    
    notes: Optional[str] = Field(None, description="Observações gerais")
    
    @validator('order_type')
    def validate_order_type(cls, v):
        valid_types = ['delivery', 'pickup', 'table_service']
        if v not in valid_types:
            raise ValueError(f'order_type deve ser um de: {valid_types}')
        return v
    
    @validator('delivery_state')
    def validate_state(cls, v):
        if v and len(v) != 2:
            raise ValueError('Estado deve ter 2 caracteres')
        return v.upper() if v else v


class CartCreate(CartBase):
    """Schema para criação de carrinho"""
    establishment_id: str = Field(..., description="ID do estabelecimento")


class CartUpdate(CartBase):
    """Schema para atualização de carrinho"""
    pass


class CartResponse(CartBase):
    """Schema de resposta para carrinho"""
    id: str
    session_id: Optional[str]
    user_id: Optional[str]
    establishment_id: str
    items: List[CartItemResponse] = []
    subtotal: Decimal
    delivery_fee: Decimal
    service_fee: Decimal
    total_amount: Decimal
    total_items: int
    created_at: datetime
    updated_at: datetime
    expires_at: Optional[datetime]
    
    class Config:
        from_attributes = True


class CartValidation(BaseModel):
    """Schema para validação do carrinho"""
    is_valid: bool
    errors: List[str] = []


class AddToCartRequest(BaseModel):
    """Schema para adicionar item ao carrinho"""
    menu_item_id: str = Field(..., description="ID do item do menu")
    quantity: int = Field(1, ge=1, description="Quantidade")
    notes: Optional[str] = Field(None, description="Observações")


class UpdateCartItemRequest(BaseModel):
    """Schema para atualizar item do carrinho"""
    quantity: int = Field(..., ge=1, description="Nova quantidade")
    notes: Optional[str] = Field(None, description="Observações")


class RemoveFromCartRequest(BaseModel):
    """Schema para remover item do carrinho"""
    menu_item_id: str = Field(..., description="ID do item do menu")


class CartSummary(BaseModel):
    """Schema para resumo do carrinho"""
    total_items: int
    subtotal: Decimal
    delivery_fee: Decimal
    service_fee: Decimal
    total_amount: Decimal
    is_valid_for_checkout: bool
    validation_errors: List[str] = []


class CheckoutRequest(BaseModel):
    """Schema para processo de checkout"""
    # Informações obrigatórias do cliente
    customer_name: str = Field(..., min_length=2, description="Nome do cliente")
    customer_phone: str = Field(..., min_length=10, description="Telefone do cliente")
    customer_email: Optional[str] = Field(None, description="Email do cliente")
    
    # Tipo de pedido
    order_type: str = Field("delivery", description="Tipo do pedido")
    table_number: Optional[int] = Field(None, description="Número da mesa")
    
    # Endereço de entrega (obrigatório para delivery)
    delivery_address: Optional[str] = Field(None, description="Endereço de entrega")
    delivery_city: Optional[str] = Field(None, description="Cidade")
    delivery_state: Optional[str] = Field(None, description="Estado")
    delivery_zip_code: Optional[str] = Field(None, description="CEP")
    delivery_latitude: Optional[float] = Field(None, description="Latitude")
    delivery_longitude: Optional[float] = Field(None, description="Longitude")
    
    # Método de pagamento
    payment_method: str = Field(..., description="Método de pagamento")
    
    # Observações
    notes: Optional[str] = Field(None, description="Observações do pedido")
    
    @validator('order_type')
    def validate_order_type(cls, v):
        valid_types = ['delivery', 'pickup', 'table_service']
        if v not in valid_types:
            raise ValueError(f'order_type deve ser um de: {valid_types}')
        return v
    
    @validator('payment_method')
    def validate_payment_method(cls, v):
        valid_methods = ['pix', 'credit_card', 'debit_card', 'cash']
        if v not in valid_methods:
            raise ValueError(f'payment_method deve ser um de: {valid_methods}')
        return v
    
    @validator('delivery_address')
    def validate_delivery_address(cls, v, values):
        if values.get('order_type') == 'delivery' and not v:
            raise ValueError('Endereço é obrigatório para delivery')
        return v
    
    @validator('table_number')
    def validate_table_number(cls, v, values):
        if values.get('order_type') == 'table_service' and not v:
            raise ValueError('Número da mesa é obrigatório para atendimento na mesa')
        return v


class CheckoutResponse(BaseModel):
    """Schema de resposta do checkout"""
    order_id: str
    order_number: str
    payment_id: Optional[str]
    payment_url: Optional[str]  # Para PIX ou cartão
    qr_code: Optional[str]  # Para PIX
    total_amount: Decimal
    status: str
    estimated_prep_time: Optional[int]
    estimated_delivery_time: Optional[int]
