"""
Aplicação principal FastAPI
"""
from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from slowapi import Limiter, _rate_limit_exceeded_handler
from slowapi.util import get_remote_address
from slowapi.errors import RateLimitExceeded
import time
import logging

from app.core.config import settings
from app.core.database import create_tables
from app.core.init_db import init_db

# Configurar logging
logging.basicConfig(
    level=getattr(logging, settings.LOG_LEVEL),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)
logger = logging.getLogger(__name__)

# Configurar rate limiting
limiter = Limiter(key_func=get_remote_address)

# Criar aplicação FastAPI
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description=settings.APP_DESCRIPTION,
    openapi_url="/api/v1/openapi.json",
    docs_url="/docs",
    redoc_url="/redoc",
)

# Adicionar rate limiting
app.state.limiter = limiter
app.add_exception_handler(RateLimitExceeded, _rate_limit_exceeded_handler)

# Middleware de CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Middleware de hosts confiáveis
app.add_middleware(
    TrustedHostMiddleware,
    allowed_hosts=settings.ALLOWED_HOSTS
)


@app.middleware("http")
async def add_process_time_header(request: Request, call_next):
    """Middleware para adicionar tempo de processamento no header"""
    start_time = time.time()
    response = await call_next(request)
    process_time = time.time() - start_time
    response.headers["X-Process-Time"] = str(process_time)
    return response


@app.on_event("startup")
async def startup_event():
    """Eventos executados na inicialização da aplicação"""
    logger.info(f"Iniciando {settings.APP_NAME} v{settings.APP_VERSION}")
    logger.info(f"Ambiente: {settings.ENVIRONMENT}")
    
    # Criar tabelas do banco de dados e dados de exemplo
    if settings.ENVIRONMENT == "development":
        try:
            init_db()
            logger.info("Banco de dados inicializado com sucesso")
        except Exception as e:
            logger.error(f"Erro ao inicializar banco de dados: {e}")
            # Fallback para apenas criar tabelas
            create_tables()
            logger.info("Tabelas do banco de dados criadas/verificadas")


@app.on_event("shutdown")
async def shutdown_event():
    """Eventos executados no encerramento da aplicação"""
    logger.info("Encerrando aplicação")


@app.get("/")
async def root():
    """Endpoint raiz da API"""
    return {
        "message": f"Bem-vindo ao {settings.APP_NAME}",
        "version": settings.APP_VERSION,
        "environment": settings.ENVIRONMENT,
        "docs": "/docs"
    }


@app.get("/health")
@limiter.limit("10/minute")
async def health_check(request: Request):
    """Endpoint de verificação de saúde da aplicação"""
    return {
        "status": "healthy",
        "timestamp": time.time(),
        "version": settings.APP_VERSION
    }


@app.exception_handler(404)
async def not_found_handler(request: Request, exc):
    """Handler para rotas não encontradas"""
    return JSONResponse(
        status_code=404,
        content={
            "detail": "Endpoint não encontrado",
            "path": str(request.url.path)
        }
    )


@app.exception_handler(500)
async def internal_error_handler(request: Request, exc):
    """Handler para erros internos do servidor"""
    logger.error(f"Erro interno: {exc}")
    return JSONResponse(
        status_code=500,
        content={
            "detail": "Erro interno do servidor",
            "message": "Entre em contato com o suporte se o problema persistir"
        }
    )


# Incluir routers da API
from app.api.v1.api import api_router
app.include_router(api_router, prefix="/api/v1")

# Incluir WebSocket endpoints
from app.websockets.endpoints import (
    websocket_establishment_endpoint,
    websocket_order_tracking_endpoint,
    websocket_customer_endpoint
)

@app.websocket("/ws/establishment/{establishment_id}")
async def websocket_establishment(websocket, establishment_id: str, token: str):
    await websocket_establishment_endpoint(websocket, establishment_id, token)

@app.websocket("/ws/order/{order_id}")
async def websocket_order_tracking(websocket, order_id: str, token: str = None):
    await websocket_order_tracking_endpoint(websocket, order_id, token)

@app.websocket("/ws/customer")
async def websocket_customer(websocket, token: str):
    await websocket_customer_endpoint(websocket, token)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "app.main:app",
        host=settings.API_HOST,
        port=settings.API_PORT,
        reload=settings.DEBUG,
        log_level=settings.LOG_LEVEL.lower()
    )
