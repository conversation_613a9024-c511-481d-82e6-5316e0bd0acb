# Frontend - Vue.js + Tailwind CSS

## Visão Geral
Interface web responsiva para administração de estabelecimentos e visualização pública de cardápios.

## Tecnologias
- **Vue.js 3**: Framework progressivo
- **Tailwind CSS**: Framework CSS utilitário
- **Pinia**: Gerenciamento de estado
- **Vue Router**: Roteamento SPA
- **Vite**: Build tool moderna
- **TypeScript**: Tipagem estática

## Estrutura
```
frontend/
├── src/
│   ├── components/        # Componentes reutilizáveis
│   │   ├── ui/           # Componentes de UI base
│   │   ├── forms/        # Formulários
│   │   ├── layout/       # Layout e navegação
│   │   └── charts/       # Gráficos e relatórios
│   ├── views/            # Páginas/Views
│   │   ├── auth/         # Login e registro
│   │   ├── admin/        # Painel administrativo
│   │   ├── menu/         # Gestão de cardápio
│   │   ├── orders/       # Gestão de pedidos
│   │   └── public/       # Páginas públicas
│   ├── stores/           # Estado global (Pinia)
│   │   ├── auth.js       # Autenticação
│   │   ├── establishment.js  # Estabelecimento
│   │   ├── menu.js       # Cardápio
│   │   └── orders.js     # Pedidos
│   ├── router/           # Configuração de rotas
│   │   └── index.js
│   ├── services/         # Serviços e APIs
│   │   ├── api.js        # Cliente HTTP
│   │   ├── auth.js       # Serviços de auth
│   │   └── websocket.js  # WebSocket client
│   ├── utils/            # Utilitários
│   │   ├── formatters.js # Formatação de dados
│   │   ├── validators.js # Validações
│   │   └── constants.js  # Constantes
│   ├── assets/           # Recursos estáticos
│   │   ├── images/
│   │   └── icons/
│   ├── styles/           # Estilos globais
│   │   └── main.css
│   ├── App.vue          # Componente raiz
│   └── main.js          # Ponto de entrada
├── public/              # Arquivos públicos
├── package.json         # Dependências
├── vite.config.js       # Configuração Vite
├── tailwind.config.js   # Configuração Tailwind
├── Dockerfile          # Container produção
├── Dockerfile.dev      # Container desenvolvimento
└── README.md           # Esta documentação
```

## Configuração

### Instalação
```bash
cd frontend
npm install
```

### Desenvolvimento
```bash
npm run dev
```

### Build para Produção
```bash
npm run build
```

## Funcionalidades

### Painel Administrativo
- **Dashboard**: Métricas e resumos
- **Gestão de Cardápio**: CRUD de categorias e produtos
- **Gestão de Pedidos**: Visualização e atualização de status
- **Relatórios**: Vendas, produtos populares, etc.
- **Configurações**: Personalização do estabelecimento

### Página Pública
- **Cardápio Digital**: Visualização responsiva
- **Carrinho**: Adicionar produtos e fazer pedidos
- **Personalização**: Cores e logo do estabelecimento
- **Multi-tenant**: Suporte a múltiplos estabelecimentos

### Recursos Técnicos
- **Responsivo**: Mobile-first design
- **PWA**: Progressive Web App
- **Real-time**: WebSocket para atualizações
- **Offline**: Cache para funcionalidade offline
- **SEO**: Meta tags dinâmicas

## Componentes Principais

### Layout
- `AppHeader.vue` - Cabeçalho principal
- `AppSidebar.vue` - Menu lateral
- `AppFooter.vue` - Rodapé

### Formulários
- `MenuItemForm.vue` - Formulário de produtos
- `CategoryForm.vue` - Formulário de categorias
- `EstablishmentForm.vue` - Configurações

### UI
- `Button.vue` - Botões padronizados
- `Modal.vue` - Modais reutilizáveis
- `Table.vue` - Tabelas de dados
- `Card.vue` - Cards informativos

## Stores (Pinia)

### Auth Store
```javascript
// Gerenciamento de autenticação
const authStore = useAuthStore()
authStore.login(credentials)
authStore.logout()
authStore.user // Usuário atual
```

### Menu Store
```javascript
// Gerenciamento de cardápio
const menuStore = useMenuStore()
menuStore.fetchCategories()
menuStore.addProduct(product)
menuStore.categories // Lista de categorias
```

## Roteamento

### Rotas Administrativas
- `/admin` - Dashboard
- `/admin/menu` - Gestão de cardápio
- `/admin/orders` - Gestão de pedidos
- `/admin/settings` - Configurações

### Rotas Públicas
- `/:slug` - Cardápio público do estabelecimento
- `/:slug/cart` - Carrinho de compras
- `/:slug/order/:id` - Acompanhamento de pedido

## Estilos e Temas

### Tailwind CSS
Utilizamos classes utilitárias do Tailwind para estilização:
```vue
<template>
  <div class="bg-white shadow-lg rounded-lg p-6">
    <h2 class="text-2xl font-bold text-gray-800 mb-4">
      Título
    </h2>
  </div>
</template>
```

### Personalização por Estabelecimento
```javascript
// Aplicar cores personalizadas
const establishment = useEstablishmentStore()
document.documentElement.style.setProperty(
  '--primary-color', 
  establishment.primaryColor
)
```

## Testes
```bash
# Testes unitários
npm run test:unit

# Testes E2E
npm run test:e2e

# Linting
npm run lint
```

## Próximos Passos
1. Configurar Vue 3 + Vite
2. Implementar sistema de autenticação
3. Criar componentes de UI base
4. Desenvolver painel administrativo
5. Implementar página pública do cardápio
