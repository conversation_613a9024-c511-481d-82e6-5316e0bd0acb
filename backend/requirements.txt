# FastAPI and ASGI server
fastapi==0.104.1
uvicorn[standard]==0.24.0

# Database
sqlalchemy==2.0.23
alembic==1.12.1
psycopg2-binary==2.9.9

# Authentication and Security
python-jose[cryptography]==3.3.0
passlib[bcrypt]==1.7.4
python-multipart==0.0.6

# Redis
redis==5.0.1
celery==5.3.4

# Object Storage (MinIO)
minio==7.2.0

# HTTP Client
httpx==0.25.2
requests==2.31.0

# Data Validation
pydantic==2.5.0
pydantic-settings==2.1.0
email-validator==2.1.0

# Date and Time
python-dateutil==2.8.2

# Environment Variables
python-dotenv==1.0.0

# CORS
python-cors==1.0.0

# WebSockets
websockets==12.0

# Payment Processing
mercadopago==2.2.1
stripe==7.8.0

# Image Processing
Pillow==10.1.0

# Logging
structlog==23.2.0

# Testing
pytest==7.4.3
pytest-asyncio==0.21.1
pytest-cov==4.1.0
httpx==0.25.2

# Development
black==23.11.0
isort==5.12.0
flake8==6.1.0
mypy==1.7.1

# Monitoring
prometheus-client==0.19.0

# Rate Limiting
slowapi==0.1.9

# UUID
uuid==1.30

# JSON
orjson==3.9.10

# Timezone
pytz==2023.3
