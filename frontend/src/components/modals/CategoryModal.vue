<template>
  <TransitionRoot as="template" :show="true">
    <Dialog as="div" class="relative z-50" @close="$emit('close')">
      <TransitionChild
        as="template"
        enter="ease-out duration-300"
        enter-from="opacity-0"
        enter-to="opacity-100"
        leave="ease-in duration-200"
        leave-from="opacity-100"
        leave-to="opacity-0"
      >
        <div class="fixed inset-0 bg-gray-500 bg-opacity-75 transition-opacity" />
      </TransitionChild>

      <div class="fixed inset-0 z-10 overflow-y-auto">
        <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
          <TransitionChild
            as="template"
            enter="ease-out duration-300"
            enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            enter-to="opacity-100 translate-y-0 sm:scale-100"
            leave="ease-in duration-200"
            leave-from="opacity-100 translate-y-0 sm:scale-100"
            leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
          >
            <DialogPanel class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
              <!-- Header -->
              <div class="flex items-center justify-between mb-6">
                <DialogTitle as="h3" class="text-lg font-medium leading-6 text-gray-900">
                  {{ isEditing ? 'Editar Categoria' : 'Nova Categoria' }}
                </DialogTitle>
                <button
                  @click="$emit('close')"
                  class="rounded-md text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-primary-500"
                >
                  <XMarkIcon class="h-6 w-6" />
                </button>
              </div>

              <!-- Formulário -->
              <form @submit.prevent="handleSubmit" class="space-y-6">
                <!-- Nome -->
                <div class="form-group">
                  <label for="name" class="form-label">
                    Nome da categoria *
                  </label>
                  <input
                    id="name"
                    v-model="form.name"
                    type="text"
                    required
                    :class="[
                      'input',
                      errors.name ? 'input-error' : ''
                    ]"
                    placeholder="Ex: Entradas, Pratos principais..."
                    :disabled="isLoading"
                  />
                  <p v-if="errors.name" class="form-error">
                    {{ errors.name }}
                  </p>
                </div>

                <!-- Descrição -->
                <div class="form-group">
                  <label for="description" class="form-label">
                    Descrição
                  </label>
                  <textarea
                    id="description"
                    v-model="form.description"
                    rows="3"
                    :class="[
                      'input',
                      errors.description ? 'input-error' : ''
                    ]"
                    placeholder="Descrição opcional da categoria..."
                    :disabled="isLoading"
                  />
                  <p v-if="errors.description" class="form-error">
                    {{ errors.description }}
                  </p>
                </div>

                <!-- Ordem de exibição -->
                <div class="form-group">
                  <label for="sort_order" class="form-label">
                    Ordem de exibição
                  </label>
                  <input
                    id="sort_order"
                    v-model.number="form.sort_order"
                    type="number"
                    min="0"
                    :class="[
                      'input',
                      errors.sort_order ? 'input-error' : ''
                    ]"
                    placeholder="0"
                    :disabled="isLoading"
                  />
                  <p class="form-help">
                    Categorias com menor número aparecem primeiro
                  </p>
                  <p v-if="errors.sort_order" class="form-error">
                    {{ errors.sort_order }}
                  </p>
                </div>

                <!-- Configurações -->
                <div class="space-y-4">
                  <h4 class="text-sm font-medium text-gray-900">Configurações</h4>
                  
                  <!-- Categoria em destaque -->
                  <div class="flex items-center">
                    <input
                      id="is_featured"
                      v-model="form.is_featured"
                      type="checkbox"
                      class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      :disabled="isLoading"
                    />
                    <label for="is_featured" class="ml-3 text-sm text-gray-700">
                      Categoria em destaque
                      <span class="block text-xs text-gray-500">
                        Aparecerá com destaque no cardápio
                      </span>
                    </label>
                  </div>

                  <!-- Disponível o dia todo -->
                  <div class="flex items-center">
                    <input
                      id="available_all_day"
                      v-model="form.available_all_day"
                      type="checkbox"
                      class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                      :disabled="isLoading"
                    />
                    <label for="available_all_day" class="ml-3 text-sm text-gray-700">
                      Disponível 24 horas
                      <span class="block text-xs text-gray-500">
                        Categoria sempre disponível
                      </span>
                    </label>
                  </div>
                </div>

                <!-- Horários de disponibilidade -->
                <div v-if="!form.available_all_day" class="space-y-4">
                  <h4 class="text-sm font-medium text-gray-900">Horários de Disponibilidade</h4>
                  
                  <div class="grid grid-cols-2 gap-4">
                    <!-- Horário de início -->
                    <div class="form-group">
                      <label for="available_start_time" class="form-label">
                        Início
                      </label>
                      <input
                        id="available_start_time"
                        v-model="form.available_start_time"
                        type="time"
                        :class="[
                          'input',
                          errors.available_start_time ? 'input-error' : ''
                        ]"
                        :disabled="isLoading"
                      />
                      <p v-if="errors.available_start_time" class="form-error">
                        {{ errors.available_start_time }}
                      </p>
                    </div>

                    <!-- Horário de fim -->
                    <div class="form-group">
                      <label for="available_end_time" class="form-label">
                        Fim
                      </label>
                      <input
                        id="available_end_time"
                        v-model="form.available_end_time"
                        type="time"
                        :class="[
                          'input',
                          errors.available_end_time ? 'input-error' : ''
                        ]"
                        :disabled="isLoading"
                      />
                      <p v-if="errors.available_end_time" class="form-error">
                        {{ errors.available_end_time }}
                      </p>
                    </div>
                  </div>
                </div>

                <!-- Botões -->
                <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
                  <button
                    type="button"
                    @click="$emit('close')"
                    class="btn btn-outline"
                    :disabled="isLoading"
                  >
                    Cancelar
                  </button>
                  
                  <button
                    type="submit"
                    :disabled="isLoading || !isFormValid"
                    class="btn btn-primary"
                  >
                    <div v-if="isLoading" class="flex items-center">
                      <div class="spinner w-4 h-4 mr-2"></div>
                      Salvando...
                    </div>
                    <span v-else>
                      {{ isEditing ? 'Atualizar' : 'Criar' }} Categoria
                    </span>
                  </button>
                </div>
              </form>
            </DialogPanel>
          </TransitionChild>
        </div>
      </div>
    </Dialog>
  </TransitionRoot>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'
import { Dialog, DialogPanel, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue'
import { XMarkIcon } from '@heroicons/vue/24/outline'

import { useMenuStore } from '@/stores/menu'
import type { Category, CategoryCreate, CategoryUpdate } from '@/types'

interface Props {
  category?: Category | null
}

interface Emits {
  (e: 'close'): void
  (e: 'saved'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const menuStore = useMenuStore()

// Estado do formulário
const form = ref<CategoryCreate & { available_start_time?: string; available_end_time?: string }>({
  name: '',
  description: '',
  sort_order: 0,
  is_featured: false,
  available_all_day: true,
  available_start_time: '',
  available_end_time: ''
})

const isLoading = ref(false)
const errors = ref<Record<string, string>>({})

// Computed
const isEditing = computed(() => !!props.category)

const isFormValid = computed(() => {
  return form.value.name.trim().length >= 2 &&
         (form.value.available_all_day || 
          (form.value.available_start_time && form.value.available_end_time))
})

// Métodos
const validateField = (field: string, value: any) => {
  errors.value[field] = ''
  
  switch (field) {
    case 'name':
      if (!value || value.trim().length < 2) {
        errors.value[field] = 'Nome deve ter pelo menos 2 caracteres'
      }
      break
      
    case 'available_start_time':
    case 'available_end_time':
      if (!form.value.available_all_day && !value) {
        errors.value[field] = 'Horário é obrigatório quando não disponível 24h'
      }
      break
  }
}

const validateForm = () => {
  validateField('name', form.value.name)
  
  if (!form.value.available_all_day) {
    validateField('available_start_time', form.value.available_start_time)
    validateField('available_end_time', form.value.available_end_time)
  }
  
  return Object.values(errors.value).every(error => !error)
}

const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }
  
  isLoading.value = true
  
  try {
    const categoryData: CategoryCreate | CategoryUpdate = {
      name: form.value.name.trim(),
      description: form.value.description?.trim() || undefined,
      sort_order: form.value.sort_order,
      is_featured: form.value.is_featured,
      available_all_day: form.value.available_all_day,
      available_start_time: form.value.available_all_day ? undefined : form.value.available_start_time,
      available_end_time: form.value.available_all_day ? undefined : form.value.available_end_time
    }
    
    if (isEditing.value && props.category) {
      await menuStore.updateCategory(props.category.id, categoryData as CategoryUpdate)
    } else {
      await menuStore.createCategory(categoryData as CategoryCreate)
    }
    
    emit('saved')
  } catch (error) {
    console.error('Erro ao salvar categoria:', error)
    
    // Tratar erros de validação do servidor
    if (error.response?.status === 422) {
      const serverErrors = error.response.data.detail
      if (Array.isArray(serverErrors)) {
        serverErrors.forEach((err: any) => {
          const field = err.loc?.[1]
          if (field && Object.keys(form.value).includes(field)) {
            errors.value[field] = err.msg
          }
        })
      }
    }
  } finally {
    isLoading.value = false
  }
}

// Watchers para validação em tempo real
watch(() => form.value.name, (newValue) => {
  if (errors.value.name) {
    validateField('name', newValue)
  }
})

watch(() => form.value.available_all_day, (newValue) => {
  if (newValue) {
    // Limpar horários quando marcar como 24h
    form.value.available_start_time = ''
    form.value.available_end_time = ''
    errors.value.available_start_time = ''
    errors.value.available_end_time = ''
  }
})

// Inicializar formulário
onMounted(() => {
  if (props.category) {
    form.value = {
      name: props.category.name,
      description: props.category.description || '',
      sort_order: props.category.sort_order,
      is_featured: props.category.is_featured,
      available_all_day: props.category.available_all_day,
      available_start_time: props.category.available_start_time || '',
      available_end_time: props.category.available_end_time || ''
    }
  }
})
</script>
