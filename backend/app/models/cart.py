from sqlalchemy import Column, String, Integer, Float, DateTime, Text, ForeignKey, Boolean
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from app.database import Base
import uuid


class Cart(Base):
    __tablename__ = "carts"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    session_id = Column(String, nullable=True, index=True)  # Para usuários não logados
    user_id = Column(String, ForeignKey("users.id"), nullable=True, index=True)  # Para usuários logados
    establishment_id = Column(String, ForeignKey("establishments.id"), nullable=False, index=True)
    
    # Informações de entrega
    order_type = Column(String, nullable=False, default="delivery")  # delivery, pickup, table_service
    table_number = Column(Integer, nullable=True)  # Para table_service
    
    # Endereço de entrega (para delivery)
    delivery_address = Column(Text, nullable=True)
    delivery_city = Column(String(100), nullable=True)
    delivery_state = Column(String(2), nullable=True)
    delivery_zip_code = Column(String(10), nullable=True)
    delivery_latitude = Column(Float, nullable=True)
    delivery_longitude = Column(Float, nullable=True)
    
    # Informações do cliente
    customer_name = Column(String(100), nullable=True)
    customer_phone = Column(String(20), nullable=True)
    customer_email = Column(String(100), nullable=True)
    
    # Observações
    notes = Column(Text, nullable=True)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    expires_at = Column(DateTime(timezone=True), nullable=True)  # Expiração do carrinho
    
    # Relacionamentos
    user = relationship("User", back_populates="carts")
    establishment = relationship("Establishment", back_populates="carts")
    items = relationship("CartItem", back_populates="cart", cascade="all, delete-orphan")
    
    @property
    def subtotal(self) -> float:
        """Calcula o subtotal dos itens no carrinho"""
        return sum(item.total_price for item in self.items)
    
    @property
    def total_items(self) -> int:
        """Conta o total de itens no carrinho"""
        return sum(item.quantity for item in self.items)
    
    @property
    def delivery_fee(self) -> float:
        """Calcula a taxa de entrega baseada no estabelecimento"""
        if self.order_type != "delivery":
            return 0.0
        return self.establishment.delivery_fee if self.establishment else 0.0
    
    @property
    def service_fee(self) -> float:
        """Calcula a taxa de serviço se aplicável"""
        if self.order_type == "table_service":
            # Taxa de serviço de 10% para atendimento na mesa
            return self.subtotal * 0.10
        return 0.0
    
    @property
    def total_amount(self) -> float:
        """Calcula o valor total do carrinho"""
        return self.subtotal + self.delivery_fee + self.service_fee
    
    def is_valid_for_checkout(self) -> tuple[bool, list[str]]:
        """Valida se o carrinho está pronto para checkout"""
        errors = []
        
        if not self.items:
            errors.append("Carrinho está vazio")
        
        if not self.customer_name:
            errors.append("Nome do cliente é obrigatório")
        
        if not self.customer_phone:
            errors.append("Telefone do cliente é obrigatório")
        
        if self.order_type == "delivery":
            if not self.delivery_address:
                errors.append("Endereço de entrega é obrigatório")
            if not self.delivery_city:
                errors.append("Cidade de entrega é obrigatória")
            if not self.delivery_state:
                errors.append("Estado de entrega é obrigatório")
            if not self.delivery_zip_code:
                errors.append("CEP de entrega é obrigatório")
        
        if self.order_type == "table_service" and not self.table_number:
            errors.append("Número da mesa é obrigatório")
        
        # Verificar se o estabelecimento aceita o tipo de pedido
        if self.establishment:
            if self.order_type == "delivery" and not self.establishment.has_delivery:
                errors.append("Estabelecimento não aceita delivery")
            elif self.order_type == "pickup" and not self.establishment.has_pickup:
                errors.append("Estabelecimento não aceita retirada")
            elif self.order_type == "table_service" and not self.establishment.has_table_service:
                errors.append("Estabelecimento não aceita atendimento na mesa")
        
        # Verificar valor mínimo para delivery
        if self.order_type == "delivery" and self.establishment:
            if self.subtotal < self.establishment.minimum_order:
                errors.append(f"Valor mínimo para delivery: R$ {self.establishment.minimum_order:.2f}")
        
        return len(errors) == 0, errors


class CartItem(Base):
    __tablename__ = "cart_items"

    id = Column(String, primary_key=True, default=lambda: str(uuid.uuid4()))
    cart_id = Column(String, ForeignKey("carts.id"), nullable=False, index=True)
    menu_item_id = Column(String, ForeignKey("menu_items.id"), nullable=False, index=True)
    
    quantity = Column(Integer, nullable=False, default=1)
    unit_price = Column(Float, nullable=False)  # Preço no momento da adição
    notes = Column(Text, nullable=True)  # Observações específicas do item
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now())
    
    # Relacionamentos
    cart = relationship("Cart", back_populates="items")
    menu_item = relationship("MenuItem", back_populates="cart_items")
    
    @property
    def total_price(self) -> float:
        """Calcula o preço total do item (quantidade * preço unitário)"""
        return self.quantity * self.unit_price
    
    @property
    def item_name(self) -> str:
        """Nome do item do menu"""
        return self.menu_item.name if self.menu_item else ""
    
    @property
    def item_description(self) -> str:
        """Descrição do item do menu"""
        return self.menu_item.description if self.menu_item else ""
    
    def update_price_from_menu_item(self):
        """Atualiza o preço baseado no item do menu atual"""
        if self.menu_item:
            self.unit_price = self.menu_item.price
