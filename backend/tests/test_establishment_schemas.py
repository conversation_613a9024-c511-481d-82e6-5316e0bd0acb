"""
Testes para schemas de estabelecimentos
"""
import pytest
from pydantic import ValidationError
from app.schemas.establishment import EstablishmentCreate, EstablishmentUpdate


def test_establishment_create_valid():
    """Testar criação de schema válido"""
    data = {
        "name": "Restaurante Teste",
        "slug": "restaurante-teste",
        "email": "<EMAIL>",
        "phone": "(11) 99999-9999",
        "primary_color": "#FF5722",
        "delivery_fee": 5.00,
        "minimum_order": 20.00
    }
    
    establishment = EstablishmentCreate(**data)
    
    assert establishment.name == "Restaurante Teste"
    assert establishment.slug == "restaurante-teste"
    assert establishment.email == "<EMAIL>"
    assert establishment.primary_color == "#FF5722"
    assert establishment.delivery_fee == 5.00
    assert establishment.accepts_orders is True  # valor padrão


def test_establishment_create_invalid_slug():
    """Testar slug inválido"""
    data = {
        "name": "Restaurante Teste",
        "slug": "Slug Inválido!",  # Contém espaços e caracteres especiais
        "email": "<EMAIL>"
    }
    
    with pytest.raises(ValidationError) as exc_info:
        EstablishmentCreate(**data)
    
    assert "Slug deve conter apenas" in str(exc_info.value)


def test_establishment_create_invalid_color():
    """Testar cor inválida"""
    data = {
        "name": "Restaurante Teste",
        "slug": "restaurante-teste",
        "email": "<EMAIL>",
        "primary_color": "cor-invalida"  # Não é hexadecimal
    }
    
    with pytest.raises(ValidationError) as exc_info:
        EstablishmentCreate(**data)
    
    assert "formato hexadecimal" in str(exc_info.value)


def test_establishment_create_negative_values():
    """Testar valores negativos"""
    data = {
        "name": "Restaurante Teste",
        "slug": "restaurante-teste",
        "email": "<EMAIL>",
        "delivery_fee": -5.00  # Valor negativo
    }
    
    with pytest.raises(ValidationError) as exc_info:
        EstablishmentCreate(**data)
    
    assert "positivo" in str(exc_info.value)


def test_establishment_create_short_name():
    """Testar nome muito curto"""
    data = {
        "name": "A",  # Muito curto
        "slug": "restaurante-teste",
        "email": "<EMAIL>"
    }
    
    with pytest.raises(ValidationError) as exc_info:
        EstablishmentCreate(**data)
    
    assert "at least 2 characters" in str(exc_info.value)


def test_establishment_update_valid():
    """Testar atualização válida"""
    data = {
        "name": "Novo Nome",
        "primary_color": "#9C27B0",
        "delivery_fee": 8.00
    }
    
    establishment_update = EstablishmentUpdate(**data)
    
    assert establishment_update.name == "Novo Nome"
    assert establishment_update.primary_color == "#9C27B0"
    assert establishment_update.delivery_fee == 8.00
    # Campos não fornecidos devem ser None
    assert establishment_update.email is None
    assert establishment_update.phone is None


def test_establishment_update_partial():
    """Testar atualização parcial"""
    data = {
        "primary_color": "#4CAF50"
    }
    
    establishment_update = EstablishmentUpdate(**data)
    
    assert establishment_update.primary_color == "#4CAF50"
    assert establishment_update.name is None
    assert establishment_update.email is None


def test_establishment_update_invalid_color():
    """Testar atualização com cor inválida"""
    data = {
        "primary_color": "invalid-color"
    }
    
    with pytest.raises(ValidationError) as exc_info:
        EstablishmentUpdate(**data)
    
    assert "formato hexadecimal" in str(exc_info.value)


def test_establishment_create_default_values():
    """Testar valores padrão"""
    data = {
        "name": "Restaurante Padrão",
        "slug": "restaurante-padrao",
        "email": "<EMAIL>"
    }
    
    establishment = EstablishmentCreate(**data)
    
    # Verificar valores padrão
    assert establishment.country == "Brasil"
    assert establishment.primary_color == "#3B82F6"
    assert establishment.secondary_color == "#1F2937"
    assert establishment.accent_color == "#10B981"
    assert establishment.accepts_orders is True
    assert establishment.delivery_fee == 0.00
    assert establishment.minimum_order == 0.00
    assert establishment.max_delivery_distance == 10.0
    assert establishment.has_delivery is True
    assert establishment.has_pickup is True
    assert establishment.has_table_service is False
    assert establishment.accepts_pix is True
    assert establishment.accepts_card is True
    assert establishment.accepts_cash is True


def test_establishment_create_email_validation():
    """Testar validação de email"""
    data = {
        "name": "Restaurante Email",
        "slug": "restaurante-email",
        "email": "email-invalido"  # Email inválido
    }
    
    with pytest.raises(ValidationError) as exc_info:
        EstablishmentCreate(**data)
    
    # Pydantic deve validar o formato do email
    assert "email" in str(exc_info.value).lower()


def test_establishment_create_slug_normalization():
    """Testar normalização do slug"""
    data = {
        "name": "Restaurante Teste",
        "slug": "restaurante-teste-123",  # Slug válido
        "email": "<EMAIL>"
    }
    
    establishment = EstablishmentCreate(**data)
    assert establishment.slug == "restaurante-teste-123"


def test_establishment_name_strip():
    """Testar remoção de espaços do nome"""
    data = {
        "name": "  Restaurante com Espaços  ",
        "slug": "restaurante-espacos",
        "email": "<EMAIL>"
    }
    
    establishment = EstablishmentCreate(**data)
    assert establishment.name == "Restaurante com Espaços"  # Espaços removidos
