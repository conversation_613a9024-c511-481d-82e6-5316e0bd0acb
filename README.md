# Sistema de Cardápio Digital Multi-Empresas

## Objetivo
Desenvolver um sistema completo de cardápio digital online onde múltiplos estabelecimentos (restaurantes, bares, etc.) possam gerenciar seus produtos, pedidos e clientes.

## Arquitetura do Sistema
- **Backend**: Python (FastAPI) com arquitetura multi-tenant
- **Frontend**: Vue.js 3 + Tailwind CSS (responsivo)
- **Mobile**: Flutter para clientes
- **Infraestrutura**: Docker + PostgreSQL + Redis + MinIO

## Status do Projeto
🚧 **Em Desenvolvimento** - Estrutura inicial criada

## Estrutura do Repositório
```
cardapio/
├── backend/          # API FastAPI
├── frontend/         # Vue.js + Tailwind
├── mobile/           # App Flutter
├── docker/           # Configurações Docker
├── docs/             # Documentação
└── README.md         # Este arquivo
```

## Funcionalidades Principais

### Para Estabelecimentos (Painel Admin)
- ✅ Gestão de Cardápio (categorias, produtos, promoções)
- ✅ Gestão de Pedidos (tempo real, status tracking)
- ✅ Relatórios de vendas e analytics
- ✅ Cadastro e fidelização de clientes

### Para Clientes (App Mobile)
- ✅ Busca de estabelecimentos por localização
- ✅ Carrinho de compras e pedidos
- ✅ Pagamentos online (Mercado Pago/Stripe)
- ✅ Acompanhamento em tempo real
- ✅ Avaliações e favoritos

## Tecnologias Utilizadas

### Backend
- FastAPI (Python)
- PostgreSQL (multi-tenant)
- Redis (cache/filas)
- MinIO (armazenamento)
- JWT (autenticação)
- WebSockets (tempo real)

### Frontend
- Vue.js 3
- Tailwind CSS
- Pinia (state management)
- Vite (build tool)

### Mobile
- Flutter
- Firebase (push notifications)
- Integração com APIs de pagamento

### Infraestrutura
- Docker & Docker Compose
- Nginx (proxy reverso)
- PostgreSQL
- Redis
- MinIO

## Requisitos de Segurança
- ✅ Autenticação JWT multi-nível
- ✅ Criptografia de dados sensíveis (LGPD)
- ✅ Rate limiting anti-fraude
- ✅ Isolamento de dados por tenant

## Como Executar

### Desenvolvimento
```bash
# Clone o repositório
git clone https://github.com/reginaldobertoluci/cardapio.git
cd cardapio

# Execute com Docker
docker-compose up -d
```

### Produção
Consulte a documentação em `docs/deploy.md`

## Contribuição
1. Fork o projeto
2. Crie uma branch para sua feature
3. Commit suas mudanças
4. Push para a branch
5. Abra um Pull Request

## Licença
MIT License - veja o arquivo LICENSE para detalhes