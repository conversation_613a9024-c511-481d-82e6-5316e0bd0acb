import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ApiService } from '@/services/api'
import type { 
  Category, 
  CategoryCreate, 
  CategoryUpdate,
  MenuItem, 
  MenuItemCreate, 
  MenuItemUpdate,
  Menu,
  MenuItemFilters 
} from '@/types'

export const useMenuStore = defineStore('menu', () => {
  // Estado
  const categories = ref<Category[]>([])
  const menuItems = ref<MenuItem[]>([])
  const fullMenu = ref<Menu | null>(null)
  const currentCategory = ref<Category | null>(null)
  const currentMenuItem = ref<MenuItem | null>(null)
  const isLoading = ref(false)
  const isLoadingMenu = ref(false)

  // Getters computados
  const activeCategories = computed(() => 
    categories.value.filter(cat => cat.is_active)
  )
  
  const featuredCategories = computed(() => 
    activeCategories.value.filter(cat => cat.is_featured)
  )
  
  const availableMenuItems = computed(() => 
    menuItems.value.filter(item => item.is_available && item.is_active)
  )
  
  const featuredMenuItems = computed(() => 
    availableMenuItems.value.filter(item => item.is_featured)
  )
  
  const menuItemsByCategory = computed(() => {
    const grouped: Record<string, MenuItem[]> = {}
    menuItems.value.forEach(item => {
      if (!grouped[item.category_id]) {
        grouped[item.category_id] = []
      }
      grouped[item.category_id].push(item)
    })
    return grouped
  })

  // Ações para categorias
  const fetchCategories = async (includeInactive = false): Promise<void> => {
    isLoading.value = true
    
    try {
      const params = includeInactive ? { include_inactive: true } : {}
      const fetchedCategories = await ApiService.get<Category[]>('/menu/categories', { params })
      categories.value = fetchedCategories
    } catch (error) {
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const createCategory = async (data: CategoryCreate): Promise<Category> => {
    isLoading.value = true
    
    try {
      const category = await ApiService.post<Category>('/menu/categories', data)
      categories.value.push(category)
      return category
    } catch (error) {
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const updateCategory = async (id: string, data: CategoryUpdate): Promise<void> => {
    isLoading.value = true
    
    try {
      const updated = await ApiService.put<Category>(`/menu/categories/${id}`, data)
      const index = categories.value.findIndex(cat => cat.id === id)
      if (index !== -1) {
        categories.value[index] = updated
      }
      
      if (currentCategory.value?.id === id) {
        currentCategory.value = updated
      }
    } catch (error) {
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const deleteCategory = async (id: string): Promise<void> => {
    isLoading.value = true
    
    try {
      await ApiService.delete(`/menu/categories/${id}`)
      categories.value = categories.value.filter(cat => cat.id !== id)
      
      if (currentCategory.value?.id === id) {
        currentCategory.value = null
      }
    } catch (error) {
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const uploadCategoryImage = async (
    id: string, 
    file: File, 
    onProgress?: (progress: number) => void
  ): Promise<string> => {
    try {
      const response = await ApiService.uploadFile<{ image_url: string }>(
        `/menu/categories/${id}/image`,
        file,
        onProgress
      )
      
      // Atualizar categoria na lista
      const index = categories.value.findIndex(cat => cat.id === id)
      if (index !== -1) {
        categories.value[index].image_url = response.image_url
      }
      
      if (currentCategory.value?.id === id) {
        currentCategory.value.image_url = response.image_url
      }
      
      return response.image_url
    } catch (error) {
      throw error
    }
  }

  // Ações para itens do menu
  const fetchMenuItems = async (filters: MenuItemFilters = {}): Promise<void> => {
    isLoading.value = true
    
    try {
      const items = await ApiService.get<MenuItem[]>('/menu/items', { params: filters })
      menuItems.value = items
    } catch (error) {
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const createMenuItem = async (data: MenuItemCreate): Promise<MenuItem> => {
    isLoading.value = true
    
    try {
      const item = await ApiService.post<MenuItem>('/menu/items', data)
      menuItems.value.push(item)
      return item
    } catch (error) {
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const updateMenuItem = async (id: string, data: MenuItemUpdate): Promise<void> => {
    isLoading.value = true
    
    try {
      const updated = await ApiService.put<MenuItem>(`/menu/items/${id}`, data)
      const index = menuItems.value.findIndex(item => item.id === id)
      if (index !== -1) {
        menuItems.value[index] = updated
      }
      
      if (currentMenuItem.value?.id === id) {
        currentMenuItem.value = updated
      }
    } catch (error) {
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const deleteMenuItem = async (id: string): Promise<void> => {
    isLoading.value = true
    
    try {
      await ApiService.delete(`/menu/items/${id}`)
      menuItems.value = menuItems.value.filter(item => item.id !== id)
      
      if (currentMenuItem.value?.id === id) {
        currentMenuItem.value = null
      }
    } catch (error) {
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const uploadMenuItemImage = async (
    id: string, 
    file: File, 
    onProgress?: (progress: number) => void
  ): Promise<string> => {
    try {
      const response = await ApiService.uploadFile<{ image_url: string }>(
        `/menu/items/${id}/image`,
        file,
        onProgress
      )
      
      // Atualizar item na lista
      const index = menuItems.value.findIndex(item => item.id === id)
      if (index !== -1) {
        menuItems.value[index].image_url = response.image_url
      }
      
      if (currentMenuItem.value?.id === id) {
        currentMenuItem.value.image_url = response.image_url
      }
      
      return response.image_url
    } catch (error) {
      throw error
    }
  }

  const uploadMenuItemGallery = async (
    id: string, 
    files: File[], 
    onProgress?: (progress: number) => void
  ): Promise<string[]> => {
    try {
      const response = await ApiService.uploadFiles<{ gallery_urls: string[] }>(
        `/menu/items/${id}/gallery`,
        files,
        onProgress
      )
      
      // Atualizar item na lista
      const index = menuItems.value.findIndex(item => item.id === id)
      if (index !== -1) {
        menuItems.value[index].gallery_urls = response.gallery_urls
      }
      
      if (currentMenuItem.value?.id === id) {
        currentMenuItem.value.gallery_urls = response.gallery_urls
      }
      
      return response.gallery_urls
    } catch (error) {
      throw error
    }
  }

  // Ações para cardápio completo
  const fetchFullMenu = async (): Promise<void> => {
    isLoadingMenu.value = true
    
    try {
      const menu = await ApiService.get<Menu>('/menu/')
      fullMenu.value = menu
    } catch (error) {
      throw error
    } finally {
      isLoadingMenu.value = false
    }
  }

  const fetchPublicMenu = async (establishmentSlug: string): Promise<Menu> => {
    isLoadingMenu.value = true
    
    try {
      const menu = await ApiService.get<Menu>(`/menu/public/${establishmentSlug}`)
      fullMenu.value = menu
      return menu
    } catch (error) {
      throw error
    } finally {
      isLoadingMenu.value = false
    }
  }

  // Ações para seleção atual
  const setCurrentCategory = (category: Category | null): void => {
    currentCategory.value = category
  }

  const setCurrentMenuItem = (item: MenuItem | null): void => {
    currentMenuItem.value = item
  }

  // Ações para busca e filtros
  const searchMenuItems = async (query: string): Promise<MenuItem[]> => {
    try {
      return await ApiService.get<MenuItem[]>('/menu/items', {
        params: { search: query }
      })
    } catch (error) {
      throw error
    }
  }

  const getMenuItemsByCategory = (categoryId: string): MenuItem[] => {
    return menuItems.value.filter(item => item.category_id === categoryId)
  }

  // Ações para disponibilidade
  const toggleMenuItemAvailability = async (id: string): Promise<void> => {
    const item = menuItems.value.find(item => item.id === id)
    if (!item) return

    await updateMenuItem(id, { is_available: !item.is_available })
  }

  const toggleCategoryFeatured = async (id: string): Promise<void> => {
    const category = categories.value.find(cat => cat.id === id)
    if (!category) return

    await updateCategory(id, { is_featured: !category.is_featured })
  }

  const toggleMenuItemFeatured = async (id: string): Promise<void> => {
    const item = menuItems.value.find(item => item.id === id)
    if (!item) return

    await updateMenuItem(id, { is_featured: !item.is_featured })
  }

  // Ações para limpeza
  const clearMenu = (): void => {
    categories.value = []
    menuItems.value = []
    fullMenu.value = null
    currentCategory.value = null
    currentMenuItem.value = null
  }

  return {
    // Estado
    categories,
    menuItems,
    fullMenu,
    currentCategory,
    currentMenuItem,
    isLoading,
    isLoadingMenu,
    
    // Getters
    activeCategories,
    featuredCategories,
    availableMenuItems,
    featuredMenuItems,
    menuItemsByCategory,
    
    // Ações para categorias
    fetchCategories,
    createCategory,
    updateCategory,
    deleteCategory,
    uploadCategoryImage,
    
    // Ações para itens
    fetchMenuItems,
    createMenuItem,
    updateMenuItem,
    deleteMenuItem,
    uploadMenuItemImage,
    uploadMenuItemGallery,
    
    // Ações para cardápio completo
    fetchFullMenu,
    fetchPublicMenu,
    
    // Ações para seleção
    setCurrentCategory,
    setCurrentMenuItem,
    
    // Ações para busca
    searchMenuItems,
    getMenuItemsByCategory,
    
    // Ações para disponibilidade
    toggleMenuItemAvailability,
    toggleCategoryFeatured,
    toggleMenuItemFeatured,
    
    // Ações para limpeza
    clearMenu,
  }
})
