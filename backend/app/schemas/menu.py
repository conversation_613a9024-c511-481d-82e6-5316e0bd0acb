"""
Schemas para cardápio (categorias e itens)
"""
from pydantic import BaseModel, validator, Field
from typing import Optional, List
from decimal import Decimal


class CategoryBase(BaseModel):
    """Schema base para categoria"""
    name: str = Field(..., min_length=2, max_length=255)
    description: Optional[str] = None
    sort_order: int = 0
    is_featured: bool = False
    available_all_day: bool = True
    available_start_time: Optional[str] = None  # HH:MM
    available_end_time: Optional[str] = None    # HH:MM
    
    @validator('name')
    def validate_name(cls, v):
        if len(v.strip()) < 2:
            raise ValueError('Nome deve ter pelo menos 2 caracteres')
        return v.strip()
    
    @validator('available_start_time', 'available_end_time')
    def validate_time_format(cls, v):
        if v is not None:
            import re
            if not re.match(r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$', v):
                raise ValueError('Horário deve estar no formato HH:MM')
        return v


class CategoryCreate(CategoryBase):
    """Schema para criação de categoria"""
    pass


class CategoryUpdate(BaseModel):
    """Schema para atualização de categoria"""
    name: Optional[str] = None
    description: Optional[str] = None
    sort_order: Optional[int] = None
    is_featured: Optional[bool] = None
    available_all_day: Optional[bool] = None
    available_start_time: Optional[str] = None
    available_end_time: Optional[str] = None
    
    @validator('name')
    def validate_name(cls, v):
        if v is not None and len(v.strip()) < 2:
            raise ValueError('Nome deve ter pelo menos 2 caracteres')
        return v.strip() if v else v
    
    @validator('available_start_time', 'available_end_time')
    def validate_time_format(cls, v):
        if v is not None:
            import re
            if not re.match(r'^([01]?[0-9]|2[0-3]):[0-5][0-9]$', v):
                raise ValueError('Horário deve estar no formato HH:MM')
        return v


class CategoryResponse(CategoryBase):
    """Schema para resposta de categoria"""
    id: str
    image_url: Optional[str] = None
    is_active: bool
    created_at: str
    updated_at: str
    
    class Config:
        from_attributes = True


class MenuItemBase(BaseModel):
    """Schema base para item do menu"""
    name: str = Field(..., min_length=2, max_length=255)
    description: Optional[str] = None
    price: Decimal = Field(..., gt=0, decimal_places=2)
    is_available: bool = True
    stock_quantity: Optional[int] = None
    
    # Informações nutricionais
    calories: Optional[int] = None
    protein: Optional[Decimal] = None
    carbs: Optional[Decimal] = None
    fat: Optional[Decimal] = None
    fiber: Optional[Decimal] = None
    
    # Tempo de preparo
    prep_time_minutes: int = 15
    
    # Configurações especiais
    is_featured: bool = False
    is_spicy: bool = False
    is_vegetarian: bool = False
    is_vegan: bool = False
    is_gluten_free: bool = False
    is_dairy_free: bool = False
    
    # Ordenação
    sort_order: int = 0
    
    @validator('name')
    def validate_name(cls, v):
        if len(v.strip()) < 2:
            raise ValueError('Nome deve ter pelo menos 2 caracteres')
        return v.strip()
    
    @validator('price')
    def validate_price(cls, v):
        if v <= 0:
            raise ValueError('Preço deve ser maior que zero')
        return v
    
    @validator('stock_quantity')
    def validate_stock(cls, v):
        if v is not None and v < 0:
            raise ValueError('Estoque não pode ser negativo')
        return v
    
    @validator('prep_time_minutes')
    def validate_prep_time(cls, v):
        if v < 0:
            raise ValueError('Tempo de preparo não pode ser negativo')
        return v


class MenuItemCreate(MenuItemBase):
    """Schema para criação de item do menu"""
    category_id: str


class MenuItemUpdate(BaseModel):
    """Schema para atualização de item do menu"""
    name: Optional[str] = None
    description: Optional[str] = None
    price: Optional[Decimal] = None
    category_id: Optional[str] = None
    is_available: Optional[bool] = None
    stock_quantity: Optional[int] = None
    
    # Informações nutricionais
    calories: Optional[int] = None
    protein: Optional[Decimal] = None
    carbs: Optional[Decimal] = None
    fat: Optional[Decimal] = None
    fiber: Optional[Decimal] = None
    
    # Tempo de preparo
    prep_time_minutes: Optional[int] = None
    
    # Configurações especiais
    is_featured: Optional[bool] = None
    is_spicy: Optional[bool] = None
    is_vegetarian: Optional[bool] = None
    is_vegan: Optional[bool] = None
    is_gluten_free: Optional[bool] = None
    is_dairy_free: Optional[bool] = None
    
    # Ordenação
    sort_order: Optional[int] = None
    
    @validator('name')
    def validate_name(cls, v):
        if v is not None and len(v.strip()) < 2:
            raise ValueError('Nome deve ter pelo menos 2 caracteres')
        return v.strip() if v else v
    
    @validator('price')
    def validate_price(cls, v):
        if v is not None and v <= 0:
            raise ValueError('Preço deve ser maior que zero')
        return v
    
    @validator('stock_quantity')
    def validate_stock(cls, v):
        if v is not None and v < 0:
            raise ValueError('Estoque não pode ser negativo')
        return v
    
    @validator('prep_time_minutes')
    def validate_prep_time(cls, v):
        if v is not None and v < 0:
            raise ValueError('Tempo de preparo não pode ser negativo')
        return v


class MenuItemResponse(MenuItemBase):
    """Schema para resposta de item do menu"""
    id: str
    category_id: str
    image_url: Optional[str] = None
    gallery_urls: Optional[List[str]] = None
    is_active: bool
    is_in_stock: bool
    created_at: str
    updated_at: str
    
    class Config:
        from_attributes = True


class MenuItemPublic(BaseModel):
    """Schema público de item do menu (para clientes)"""
    id: str
    name: str
    description: Optional[str]
    price: Decimal
    image_url: Optional[str]
    gallery_urls: Optional[List[str]]
    
    # Informações nutricionais
    calories: Optional[int]
    protein: Optional[Decimal]
    carbs: Optional[Decimal]
    fat: Optional[Decimal]
    fiber: Optional[Decimal]
    
    # Tempo de preparo
    prep_time_minutes: int
    
    # Configurações especiais
    is_spicy: bool
    is_vegetarian: bool
    is_vegan: bool
    is_gluten_free: bool
    is_dairy_free: bool
    
    # Disponibilidade
    is_available: bool
    is_in_stock: bool
    
    class Config:
        from_attributes = True


class CategoryWithItems(CategoryResponse):
    """Schema de categoria com seus itens"""
    items: List[MenuItemPublic] = []


class MenuResponse(BaseModel):
    """Schema para resposta completa do cardápio"""
    establishment_id: str
    establishment_name: str
    categories: List[CategoryWithItems] = []


class IngredientBase(BaseModel):
    """Schema base para ingrediente"""
    name: str = Field(..., min_length=2, max_length=255)
    description: Optional[str] = None
    
    # Informações nutricionais por 100g
    calories_per_100g: Optional[int] = None
    protein_per_100g: Optional[Decimal] = None
    carbs_per_100g: Optional[Decimal] = None
    fat_per_100g: Optional[Decimal] = None
    
    # Alérgenos
    is_allergen: bool = False
    allergen_type: Optional[str] = None
    
    @validator('name')
    def validate_name(cls, v):
        if len(v.strip()) < 2:
            raise ValueError('Nome deve ter pelo menos 2 caracteres')
        return v.strip()


class IngredientCreate(IngredientBase):
    """Schema para criação de ingrediente"""
    pass


class IngredientResponse(IngredientBase):
    """Schema para resposta de ingrediente"""
    id: str
    is_active: bool
    created_at: str
    updated_at: str
    
    class Config:
        from_attributes = True


class CustomizationOptionBase(BaseModel):
    """Schema base para opção de personalização"""
    name: str = Field(..., min_length=1, max_length=255)
    description: Optional[str] = None
    price_modifier: Decimal = 0.00
    is_default: bool = False
    sort_order: int = 0
    
    @validator('name')
    def validate_name(cls, v):
        if len(v.strip()) < 1:
            raise ValueError('Nome é obrigatório')
        return v.strip()


class CustomizationOptionCreate(CustomizationOptionBase):
    """Schema para criação de opção de personalização"""
    pass


class CustomizationOptionResponse(CustomizationOptionBase):
    """Schema para resposta de opção de personalização"""
    id: str
    is_active: bool
    
    class Config:
        from_attributes = True


class MenuItemCustomizationBase(BaseModel):
    """Schema base para personalização de item"""
    name: str = Field(..., min_length=2, max_length=255)
    description: Optional[str] = None
    customization_type: str = Field(..., pattern="^(single|multiple|text)$")
    is_required: bool = False
    max_selections: int = 1
    min_selections: int = 0
    
    @validator('name')
    def validate_name(cls, v):
        if len(v.strip()) < 2:
            raise ValueError('Nome deve ter pelo menos 2 caracteres')
        return v.strip()
    
    @validator('max_selections', 'min_selections')
    def validate_selections(cls, v):
        if v < 0:
            raise ValueError('Número de seleções não pode ser negativo')
        return v


class MenuItemCustomizationCreate(MenuItemCustomizationBase):
    """Schema para criação de personalização"""
    menu_item_id: str
    options: List[CustomizationOptionCreate] = []


class MenuItemCustomizationResponse(MenuItemCustomizationBase):
    """Schema para resposta de personalização"""
    id: str
    menu_item_id: str
    options: List[CustomizationOptionResponse] = []
    is_active: bool
    
    class Config:
        from_attributes = True
