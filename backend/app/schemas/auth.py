"""
Schemas para autenticação
"""
from pydantic import BaseModel, EmailStr, validator
from typing import Optional
from app.models.user import UserRole


class UserLogin(BaseModel):
    """Schema para login de usuário"""
    email: EmailStr
    password: str


class UserRegister(BaseModel):
    """Schema para registro de usuário"""
    email: EmailStr
    password: str
    full_name: str
    phone: Optional[str] = None
    role: UserRole = UserRole.CUSTOMER
    establishment_slug: Optional[str] = None  # Para funcionários
    
    @validator('password')
    def validate_password(cls, v):
        if len(v) < 6:
            raise ValueError('Senha deve ter pelo menos 6 caracteres')
        return v
    
    @validator('full_name')
    def validate_full_name(cls, v):
        if len(v.strip()) < 2:
            raise ValueError('Nome deve ter pelo menos 2 caracteres')
        return v.strip()


class UserResponse(BaseModel):
    """Schema para resposta de dados do usuário"""
    id: str
    email: str
    full_name: str
    phone: Optional[str]
    role: UserRole
    establishment_id: Optional[str]
    is_active: bool
    email_verified: bool
    avatar_url: Optional[str]
    
    class Config:
        from_attributes = True


class TokenResponse(BaseModel):
    """Schema para resposta de token"""
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    expires_in: int
    user: UserResponse


class TokenRefresh(BaseModel):
    """Schema para refresh de token"""
    refresh_token: str


class PasswordReset(BaseModel):
    """Schema para solicitação de reset de senha"""
    email: EmailStr


class PasswordResetConfirm(BaseModel):
    """Schema para confirmação de reset de senha"""
    token: str
    new_password: str
    
    @validator('new_password')
    def validate_password(cls, v):
        if len(v) < 6:
            raise ValueError('Senha deve ter pelo menos 6 caracteres')
        return v


class PasswordChange(BaseModel):
    """Schema para mudança de senha"""
    current_password: str
    new_password: str
    
    @validator('new_password')
    def validate_password(cls, v):
        if len(v) < 6:
            raise ValueError('Senha deve ter pelo menos 6 caracteres')
        return v


class EmailVerification(BaseModel):
    """Schema para verificação de email"""
    token: str


class UserUpdate(BaseModel):
    """Schema para atualização de dados do usuário"""
    full_name: Optional[str] = None
    phone: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    zip_code: Optional[str] = None
    
    @validator('full_name')
    def validate_full_name(cls, v):
        if v is not None and len(v.strip()) < 2:
            raise ValueError('Nome deve ter pelo menos 2 caracteres')
        return v.strip() if v else v
