<template>
  <div class="space-y-6">
    <!-- Informações básicas -->
    <div class="card">
      <div class="card-header">
        <h3 class="text-lg font-medium text-gray-900">Informações Básicas</h3>
        <p class="text-sm text-gray-500">Dados principais do seu estabelecimento</p>
      </div>
      
      <div class="card-body">
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
            <!-- Nome -->
            <div class="form-group">
              <label for="name" class="form-label">
                Nome do estabelecimento *
              </label>
              <input
                id="name"
                v-model="form.name"
                type="text"
                required
                :class="[
                  'input',
                  errors.name ? 'input-error' : ''
                ]"
                placeholder="Nome do seu restaurante"
                :disabled="isLoading"
              />
              <p v-if="errors.name" class="form-error">
                {{ errors.name }}
              </p>
            </div>

            <!-- Slug -->
            <div class="form-group">
              <label for="slug" class="form-label">
                URL personalizada *
              </label>
              <div class="flex">
                <span class="inline-flex items-center px-3 rounded-l-md border border-r-0 border-gray-300 bg-gray-50 text-gray-500 text-sm">
                  cardapio.app/
                </span>
                <input
                  id="slug"
                  v-model="form.slug"
                  type="text"
                  required
                  :class="[
                    'input rounded-l-none',
                    errors.slug ? 'input-error' : ''
                  ]"
                  placeholder="meu-restaurante"
                  :disabled="isLoading"
                />
              </div>
              <p v-if="errors.slug" class="form-error">
                {{ errors.slug }}
              </p>
              <p class="form-help">
                Esta será a URL do seu cardápio público
              </p>
            </div>

            <!-- Email -->
            <div class="form-group">
              <label for="email" class="form-label">
                Email de contato *
              </label>
              <input
                id="email"
                v-model="form.email"
                type="email"
                required
                :class="[
                  'input',
                  errors.email ? 'input-error' : ''
                ]"
                placeholder="<EMAIL>"
                :disabled="isLoading"
              />
              <p v-if="errors.email" class="form-error">
                {{ errors.email }}
              </p>
            </div>

            <!-- Telefone -->
            <div class="form-group">
              <label for="phone" class="form-label">
                Telefone
              </label>
              <input
                id="phone"
                v-model="form.phone"
                type="tel"
                :class="[
                  'input',
                  errors.phone ? 'input-error' : ''
                ]"
                placeholder="(11) 99999-9999"
                :disabled="isLoading"
              />
              <p v-if="errors.phone" class="form-error">
                {{ errors.phone }}
              </p>
            </div>
          </div>

          <!-- Endereço -->
          <div class="space-y-4">
            <h4 class="text-sm font-medium text-gray-900">Endereço</h4>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div class="md:col-span-2 form-group">
                <label for="address" class="form-label">
                  Endereço
                </label>
                <input
                  id="address"
                  v-model="form.address"
                  type="text"
                  class="input"
                  placeholder="Rua, número, complemento"
                  :disabled="isLoading"
                />
              </div>

              <div class="form-group">
                <label for="zip_code" class="form-label">
                  CEP
                </label>
                <input
                  id="zip_code"
                  v-model="form.zip_code"
                  type="text"
                  class="input"
                  placeholder="00000-000"
                  :disabled="isLoading"
                />
              </div>

              <div class="form-group">
                <label for="city" class="form-label">
                  Cidade
                </label>
                <input
                  id="city"
                  v-model="form.city"
                  type="text"
                  class="input"
                  placeholder="São Paulo"
                  :disabled="isLoading"
                />
              </div>

              <div class="form-group">
                <label for="state" class="form-label">
                  Estado
                </label>
                <select
                  id="state"
                  v-model="form.state"
                  class="input"
                  :disabled="isLoading"
                >
                  <option value="">Selecione</option>
                  <option value="AC">Acre</option>
                  <option value="AL">Alagoas</option>
                  <option value="AP">Amapá</option>
                  <option value="AM">Amazonas</option>
                  <option value="BA">Bahia</option>
                  <option value="CE">Ceará</option>
                  <option value="DF">Distrito Federal</option>
                  <option value="ES">Espírito Santo</option>
                  <option value="GO">Goiás</option>
                  <option value="MA">Maranhão</option>
                  <option value="MT">Mato Grosso</option>
                  <option value="MS">Mato Grosso do Sul</option>
                  <option value="MG">Minas Gerais</option>
                  <option value="PA">Pará</option>
                  <option value="PB">Paraíba</option>
                  <option value="PR">Paraná</option>
                  <option value="PE">Pernambuco</option>
                  <option value="PI">Piauí</option>
                  <option value="RJ">Rio de Janeiro</option>
                  <option value="RN">Rio Grande do Norte</option>
                  <option value="RS">Rio Grande do Sul</option>
                  <option value="RO">Rondônia</option>
                  <option value="RR">Roraima</option>
                  <option value="SC">Santa Catarina</option>
                  <option value="SP">São Paulo</option>
                  <option value="SE">Sergipe</option>
                  <option value="TO">Tocantins</option>
                </select>
              </div>

              <div class="form-group">
                <label for="country" class="form-label">
                  País
                </label>
                <input
                  id="country"
                  v-model="form.country"
                  type="text"
                  class="input"
                  value="Brasil"
                  readonly
                  :disabled="isLoading"
                />
              </div>
            </div>
          </div>

          <!-- Configurações gerais -->
          <div class="space-y-4">
            <h4 class="text-sm font-medium text-gray-900">Configurações</h4>
            
            <div class="space-y-3">
              <label class="flex items-center">
                <input
                  v-model="form.is_active"
                  type="checkbox"
                  class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  :disabled="isLoading"
                />
                <span class="ml-3 text-sm text-gray-700">
                  Estabelecimento ativo
                  <span class="block text-xs text-gray-500">
                    Desmarque para desativar temporariamente
                  </span>
                </span>
              </label>

              <label class="flex items-center">
                <input
                  v-model="form.accepts_orders"
                  type="checkbox"
                  class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  :disabled="isLoading"
                />
                <span class="ml-3 text-sm text-gray-700">
                  Aceitar pedidos online
                  <span class="block text-xs text-gray-500">
                    Permite que clientes façam pedidos pelo cardápio
                  </span>
                </span>
              </label>
            </div>
          </div>

          <!-- Botões -->
          <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              @click="resetForm"
              class="btn btn-outline"
              :disabled="isLoading"
            >
              Cancelar
            </button>
            
            <button
              type="submit"
              :disabled="isLoading || !isFormValid"
              class="btn btn-primary"
            >
              <div v-if="isLoading" class="flex items-center">
                <div class="spinner w-4 h-4 mr-2"></div>
                Salvando...
              </div>
              <span v-else>
                Salvar Alterações
              </span>
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Link público -->
    <div class="card">
      <div class="card-header">
        <h3 class="text-lg font-medium text-gray-900">Cardápio Público</h3>
        <p class="text-sm text-gray-500">Compartilhe este link com seus clientes</p>
      </div>
      
      <div class="card-body">
        <div class="flex items-center space-x-3">
          <input
            :value="publicUrl"
            readonly
            class="input flex-1"
          />
          <button
            @click="copyPublicUrl"
            class="btn btn-outline"
          >
            <ClipboardIcon class="h-4 w-4 mr-2" />
            Copiar
          </button>
          <a
            :href="publicUrl"
            target="_blank"
            class="btn btn-primary"
          >
            <ExternalLinkIcon class="h-4 w-4 mr-2" />
            Visualizar
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ClipboardIcon, ExternalLinkIcon } from '@heroicons/vue/24/outline'
import { useToast } from 'vue-toastification'

import { useEstablishmentStore } from '@/stores/establishment'
import type { EstablishmentUpdate } from '@/types'

const toast = useToast()
const establishmentStore = useEstablishmentStore()

// Estado do formulário
const form = ref<EstablishmentUpdate & { 
  is_active: boolean
  accepts_orders: boolean
  country: string
}>({
  name: '',
  email: '',
  phone: '',
  address: '',
  city: '',
  state: '',
  zip_code: '',
  country: 'Brasil',
  is_active: true,
  accepts_orders: true
})

const isLoading = ref(false)
const errors = ref<Record<string, string>>({})

// Computed
const establishment = computed(() => establishmentStore.currentEstablishment)

const isFormValid = computed(() => {
  return form.value.name && 
         form.value.email && 
         form.value.email.includes('@')
})

const publicUrl = computed(() => {
  return establishment.value?.public_url || `https://cardapio.app/${form.value.slug || 'seu-restaurante'}`
})

// Métodos
const validateField = (field: string, value: any) => {
  errors.value[field] = ''
  
  switch (field) {
    case 'name':
      if (!value || value.trim().length < 2) {
        errors.value[field] = 'Nome deve ter pelo menos 2 caracteres'
      }
      break
      
    case 'email':
      if (!value) {
        errors.value[field] = 'Email é obrigatório'
      } else if (!value.includes('@')) {
        errors.value[field] = 'Email deve ter um formato válido'
      }
      break
      
    case 'slug':
      if (!value) {
        errors.value[field] = 'URL personalizada é obrigatória'
      } else if (!/^[a-z0-9-]+$/.test(value)) {
        errors.value[field] = 'Use apenas letras minúsculas, números e hífens'
      }
      break
  }
}

const validateForm = () => {
  validateField('name', form.value.name)
  validateField('email', form.value.email)
  validateField('slug', form.value.slug)
  
  return Object.values(errors.value).every(error => !error)
}

const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }
  
  isLoading.value = true
  
  try {
    await establishmentStore.updateEstablishment(form.value)
    toast.success('Informações atualizadas com sucesso!')
  } catch (error) {
    console.error('Erro ao atualizar estabelecimento:', error)
    
    // Tratar erros de validação do servidor
    if (error.response?.status === 422) {
      const serverErrors = error.response.data.detail
      if (Array.isArray(serverErrors)) {
        serverErrors.forEach((err: any) => {
          const field = err.loc?.[1]
          if (field && Object.keys(form.value).includes(field)) {
            errors.value[field] = err.msg
          }
        })
      }
    } else {
      toast.error('Erro ao atualizar informações')
    }
  } finally {
    isLoading.value = false
  }
}

const resetForm = () => {
  if (establishment.value) {
    form.value = {
      name: establishment.value.name,
      email: establishment.value.email,
      phone: establishment.value.phone || '',
      address: establishment.value.address || '',
      city: establishment.value.city || '',
      state: establishment.value.state || '',
      zip_code: establishment.value.zip_code || '',
      country: establishment.value.country,
      is_active: establishment.value.is_active,
      accepts_orders: establishment.value.accepts_orders
    }
  }
  errors.value = {}
}

const copyPublicUrl = async () => {
  try {
    await navigator.clipboard.writeText(publicUrl.value)
    toast.success('Link copiado para a área de transferência!')
  } catch (error) {
    console.error('Erro ao copiar link:', error)
    toast.error('Erro ao copiar link')
  }
}

// Watchers para validação em tempo real
watch(() => form.value.name, (newValue) => {
  if (errors.value.name) {
    validateField('name', newValue)
  }
  
  // Gerar slug automaticamente se estiver vazio
  if (newValue && !form.value.slug) {
    form.value.slug = newValue
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '') // Remove acentos
      .replace(/[^a-z0-9\s-]/g, '') // Remove caracteres especiais
      .replace(/\s+/g, '-') // Substitui espaços por hífens
      .replace(/-+/g, '-') // Remove hífens duplicados
      .replace(/^-|-$/g, '') // Remove hífens do início e fim
  }
})

watch(() => form.value.email, (newValue) => {
  if (errors.value.email) {
    validateField('email', newValue)
  }
})

watch(() => form.value.slug, (newValue) => {
  if (errors.value.slug) {
    validateField('slug', newValue)
  }
})

// Lifecycle
onMounted(() => {
  resetForm()
})
</script>
