Sistema de Cardápio Digital Multi-Empresas

Objetivo:
Desenvolver um sistema completo de cardápio digital online onde múltiplos estabelecimentos (restaurantes, bares, etc.) possam gerenciar seus produtos, pedidos e clientes, com:

    Backend em Python (FastAPI).

    Frontend responsivo em Vue.js + Tailwind CSS.

    Aplicativo mobile em Flutter (para clientes fazerem pedidos).

    Infraestrutura em Docker (com arquivos docker-compose para deploy).

Requisitos Técnicos:

    Backend (FastAPI):

        Arquitetura multi-tenant (cada estabelecimento com dados isolados).

        Autenticação via JWT (proprietários, funcionários, clientes).

        API RESTful com Swagger/OpenAPI (documentação interativa).

        Banco de dados: PostgreSQL (schema separado por estabelecimento).

        Armazenamento de imagens: Minio

    Frontend Web (Vue 3 + Tailwind):

        Painel administrativo por estabelecimento (gerenciar cardápio, pedidos, clientes).

        Página pública personalizável (ex.: nome-empresa.cardapiodigital.com).

        Responsividade: Mobile-first (acessível em celulares, tablets e desktop).

    App Mobile (Flutter):

        Funcionalidades para clientes:

            Visualizar cardápios por estabelecimento.

            Fazer pedidos com carrinho de compras.

            Pagamento online (integrar Mercado Pago/Stripe).

            Acompanhamento de pedidos em tempo real (WebSockets).

        Push notifications (status do pedido: "em preparo", "entregue").

    Infraestrutura (Docker):

        Containers para:

            Backend (FastAPI + Uvicorn).

            Frontend (Vue.js + Nginx).

            Banco de dados (PostgreSQL).

            Redis (para cache e filas de pedidos).

        Arquivo docker-compose.yml pronto para produção.

Funcionalidades Principais:
Para Estabelecimentos (Painel Admin):

    Gestão de Cardápio:

        Cadastrar categorias (ex.: "Entradas", "Bebidas").

        Adicionar produtos (com fotos, descrição, preço, ingredientes).

        Promoções (descontos, combos).

    Gestão de Pedidos:

        Visualizar pedidos em tempo real (dashboard com status).

        Mudar status ("recebido", "preparando", "entregue").

        Relatórios de vendas (por período, produto mais vendido).

    Clientes:

        Cadastro de clientes (com histórico de pedidos).

        Fidelidade (pontos por compra).

Para Clientes (App Flutter):

    Buscar estabelecimentos por geolocalização ou nome.

    Favoritar estabelecimentos e produtos.

    Avaliar pedidos (1-5 estrelas).

Fluxos Especiais:

    Multi-tenancy:

        Cada estabelecimento tem:

            Subdomínio ou rota própria (empresaX.sistema.com).

            Personalização de cores/logos no painel.

    Pagamentos:

        Integração com Pix, cartão e Mercado Pago.

        Notificação de pagamento confirmado para o estabelecimento.

Segurança e Conformidade:

    LGPD: Criptografia de dados sensíveis (ex.: CPF do cliente).

    Proteção contra fraudes: Rate limiting na API (evitar spam de pedidos).

Saída Esperada:

    Repositório GitHub organizado em pastas:

        backend/ (FastAPI + PostgreSQL).

        frontend/ (Vue.js + Tailwind).

        mobile/ (Flutter).

        docker/ (arquivos de configuração).

    Documentação:

        Como configurar variáveis de ambiente.

        Deploy em produção (AWS, DigitalOcean).

    Protótipos de UI:

        Figma ou screenshots do app e painel admin.