"""
Schemas para estabelecimentos
"""
from pydantic import BaseModel, EmailStr, validator, Field
from typing import Optional, List
from datetime import time


class EstablishmentBase(BaseModel):
    """Schema base para estabelecimento"""
    name: str = Field(..., min_length=2, max_length=255)
    email: EmailStr
    phone: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    zip_code: Optional[str] = None
    country: str = "Brasil"
    
    # Personalização visual
    primary_color: str = "#3B82F6"
    secondary_color: str = "#1F2937"
    accent_color: str = "#10B981"
    
    # Configurações de funcionamento
    accepts_orders: bool = True
    delivery_fee: float = 0.00
    minimum_order: float = 0.00
    max_delivery_distance: float = 10.0
    
    # Configurações de delivery
    has_delivery: bool = True
    has_pickup: bool = True
    has_table_service: bool = False
    
    # Configurações de pagamento
    accepts_pix: bool = True
    accepts_card: bool = True
    accepts_cash: bool = True
    
    @validator('name')
    def validate_name(cls, v):
        if len(v.strip()) < 2:
            raise ValueError('Nome deve ter pelo menos 2 caracteres')
        return v.strip()
    
    @validator('primary_color', 'secondary_color', 'accent_color')
    def validate_color(cls, v):
        if not v.startswith('#') or len(v) != 7:
            raise ValueError('Cor deve estar no formato hexadecimal (#RRGGBB)')
        return v
    
    @validator('delivery_fee', 'minimum_order', 'max_delivery_distance')
    def validate_positive_numbers(cls, v):
        if v < 0:
            raise ValueError('Valor deve ser positivo')
        return v


class EstablishmentCreate(EstablishmentBase):
    """Schema para criação de estabelecimento"""
    slug: str = Field(..., min_length=3, max_length=100)
    
    @validator('slug')
    def validate_slug(cls, v):
        import re
        if not re.match(r'^[a-z0-9-]+$', v):
            raise ValueError('Slug deve conter apenas letras minúsculas, números e hífens')
        return v


class EstablishmentUpdate(BaseModel):
    """Schema para atualização de estabelecimento"""
    name: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    address: Optional[str] = None
    city: Optional[str] = None
    state: Optional[str] = None
    zip_code: Optional[str] = None
    country: Optional[str] = None
    
    # Personalização visual
    primary_color: Optional[str] = None
    secondary_color: Optional[str] = None
    accent_color: Optional[str] = None
    
    # Configurações de funcionamento
    accepts_orders: Optional[bool] = None
    delivery_fee: Optional[float] = None
    minimum_order: Optional[float] = None
    max_delivery_distance: Optional[float] = None
    
    # Configurações de delivery
    has_delivery: Optional[bool] = None
    has_pickup: Optional[bool] = None
    has_table_service: Optional[bool] = None
    
    # Configurações de pagamento
    accepts_pix: Optional[bool] = None
    accepts_card: Optional[bool] = None
    accepts_cash: Optional[bool] = None
    
    @validator('name')
    def validate_name(cls, v):
        if v is not None and len(v.strip()) < 2:
            raise ValueError('Nome deve ter pelo menos 2 caracteres')
        return v.strip() if v else v
    
    @validator('primary_color', 'secondary_color', 'accent_color')
    def validate_color(cls, v):
        if v is not None and (not v.startswith('#') or len(v) != 7):
            raise ValueError('Cor deve estar no formato hexadecimal (#RRGGBB)')
        return v
    
    @validator('delivery_fee', 'minimum_order', 'max_delivery_distance')
    def validate_positive_numbers(cls, v):
        if v is not None and v < 0:
            raise ValueError('Valor deve ser positivo')
        return v


class EstablishmentHours(BaseModel):
    """Schema para horários de funcionamento"""
    monday_open: Optional[time] = None
    monday_close: Optional[time] = None
    tuesday_open: Optional[time] = None
    tuesday_close: Optional[time] = None
    wednesday_open: Optional[time] = None
    wednesday_close: Optional[time] = None
    thursday_open: Optional[time] = None
    thursday_close: Optional[time] = None
    friday_open: Optional[time] = None
    friday_close: Optional[time] = None
    saturday_open: Optional[time] = None
    saturday_close: Optional[time] = None
    sunday_open: Optional[time] = None
    sunday_close: Optional[time] = None


class EstablishmentResponse(EstablishmentBase):
    """Schema para resposta de estabelecimento"""
    id: str
    slug: str
    logo_url: Optional[str] = None
    banner_url: Optional[str] = None
    is_active: bool
    
    # Horários de funcionamento
    monday_open: Optional[time] = None
    monday_close: Optional[time] = None
    tuesday_open: Optional[time] = None
    tuesday_close: Optional[time] = None
    wednesday_open: Optional[time] = None
    wednesday_close: Optional[time] = None
    thursday_open: Optional[time] = None
    thursday_close: Optional[time] = None
    friday_open: Optional[time] = None
    friday_close: Optional[time] = None
    saturday_open: Optional[time] = None
    saturday_close: Optional[time] = None
    sunday_open: Optional[time] = None
    sunday_close: Optional[time] = None
    
    # URLs e informações adicionais
    public_url: str
    is_open_now: bool
    
    created_at: str
    updated_at: str
    
    class Config:
        from_attributes = True


class EstablishmentList(BaseModel):
    """Schema para listagem de estabelecimentos"""
    id: str
    name: str
    slug: str
    email: str
    city: Optional[str]
    state: Optional[str]
    logo_url: Optional[str]
    is_active: bool
    accepts_orders: bool
    created_at: str
    
    class Config:
        from_attributes = True


class EstablishmentPublic(BaseModel):
    """Schema público de estabelecimento (para clientes)"""
    id: str
    name: str
    slug: str
    address: Optional[str]
    city: Optional[str]
    state: Optional[str]
    phone: Optional[str]
    
    # Personalização visual
    logo_url: Optional[str]
    banner_url: Optional[str]
    primary_color: str
    secondary_color: str
    accent_color: str
    
    # Configurações públicas
    accepts_orders: bool
    delivery_fee: float
    minimum_order: float
    max_delivery_distance: float
    has_delivery: bool
    has_pickup: bool
    has_table_service: bool
    
    # Métodos de pagamento
    accepts_pix: bool
    accepts_card: bool
    accepts_cash: bool
    
    # Status
    is_open_now: bool
    
    class Config:
        from_attributes = True


class EstablishmentStats(BaseModel):
    """Schema para estatísticas do estabelecimento"""
    total_orders: int
    total_revenue: float
    total_customers: int
    total_menu_items: int
    average_rating: Optional[float]
    orders_today: int
    revenue_today: float
