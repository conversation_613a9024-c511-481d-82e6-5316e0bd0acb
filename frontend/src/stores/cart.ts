/**
 * Store para gerenciar o carrinho de compras
 */
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { 
  CartItem, 
  Cart, 
  MenuItem,
  CartSummary,
  CheckoutRequest,
  CheckoutResponse 
} from '@/types'
import { ApiService } from '@/services/api'
import { useAuthStore } from './auth'

export const useCartStore = defineStore('cart', () => {
  // Estado
  const cart = ref<Cart | null>(null)
  const isLoading = ref(false)
  const error = ref<string | null>(null)
  const sessionId = ref<string>('')

  // Computed
  const items = computed(() => cart.value?.items || [])
  
  const totalItems = computed(() => 
    items.value.reduce((total, item) => total + item.quantity, 0)
  )
  
  const subtotal = computed(() => 
    items.value.reduce((total, item) => total + item.total_price, 0)
  )
  
  const deliveryFee = computed(() => cart.value?.delivery_fee || 0)
  const serviceFee = computed(() => cart.value?.service_fee || 0)
  const total = computed(() => subtotal.value + deliveryFee.value + serviceFee.value)
  
  const isEmpty = computed(() => items.value.length === 0)
  
  const isValidForCheckout = computed(() => {
    return !isEmpty.value && 
           cart.value?.customer_name &&
           cart.value?.customer_phone &&
           (cart.value?.order_type !== 'delivery' || cart.value?.delivery_address)
  })

  // Actions
  const initializeCart = () => {
    // Gerar session ID se não existir
    if (!sessionId.value) {
      sessionId.value = localStorage.getItem('cart_session_id') || generateSessionId()
      localStorage.setItem('cart_session_id', sessionId.value)
    }
  }

  const generateSessionId = (): string => {
    return 'session_' + Math.random().toString(36).substr(2, 9) + Date.now().toString(36)
  }

  const fetchCart = async (establishmentId: string) => {
    isLoading.value = true
    error.value = null

    try {
      const headers: Record<string, string> = {}
      
      // Adicionar session ID se usuário não estiver logado
      const authStore = useAuthStore()
      if (!authStore.isAuthenticated) {
        headers['X-Session-ID'] = sessionId.value
      }

      const response = await ApiService.get<Cart>(`/cart/${establishmentId}`, { headers })
      cart.value = response
      return response
    } catch (err: any) {
      error.value = err.response?.data?.detail || 'Erro ao carregar carrinho'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const addItem = async (
    establishmentId: string, 
    menuItem: MenuItem, 
    quantity: number = 1, 
    notes?: string
  ) => {
    isLoading.value = true
    error.value = null

    try {
      const headers: Record<string, string> = {}
      
      const authStore = useAuthStore()
      if (!authStore.isAuthenticated) {
        headers['X-Session-ID'] = sessionId.value
      }

      const response = await ApiService.post<CartItem>(
        `/cart/${establishmentId}/items`,
        {
          menu_item_id: menuItem.id,
          quantity,
          notes
        },
        { headers }
      )

      // Atualizar carrinho
      await fetchCart(establishmentId)
      
      return response
    } catch (err: any) {
      error.value = err.response?.data?.detail || 'Erro ao adicionar item'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const updateItem = async (
    establishmentId: string,
    itemId: string,
    quantity: number,
    notes?: string
  ) => {
    isLoading.value = true
    error.value = null

    try {
      const headers: Record<string, string> = {}
      
      const authStore = useAuthStore()
      if (!authStore.isAuthenticated) {
        headers['X-Session-ID'] = sessionId.value
      }

      const response = await ApiService.put<CartItem>(
        `/cart/${establishmentId}/items/${itemId}`,
        { quantity, notes },
        { headers }
      )

      // Atualizar carrinho
      await fetchCart(establishmentId)
      
      return response
    } catch (err: any) {
      error.value = err.response?.data?.detail || 'Erro ao atualizar item'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const removeItem = async (establishmentId: string, itemId: string) => {
    isLoading.value = true
    error.value = null

    try {
      const headers: Record<string, string> = {}
      
      const authStore = useAuthStore()
      if (!authStore.isAuthenticated) {
        headers['X-Session-ID'] = sessionId.value
      }

      await ApiService.delete(`/cart/${establishmentId}/items/${itemId}`, { headers })

      // Atualizar carrinho
      await fetchCart(establishmentId)
    } catch (err: any) {
      error.value = err.response?.data?.detail || 'Erro ao remover item'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const clearCart = async (establishmentId: string) => {
    isLoading.value = true
    error.value = null

    try {
      const headers: Record<string, string> = {}
      
      const authStore = useAuthStore()
      if (!authStore.isAuthenticated) {
        headers['X-Session-ID'] = sessionId.value
      }

      await ApiService.delete(`/cart/${establishmentId}/clear`, { headers })
      cart.value = null
    } catch (err: any) {
      error.value = err.response?.data?.detail || 'Erro ao limpar carrinho'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const updateCartInfo = async (establishmentId: string, data: Partial<Cart>) => {
    isLoading.value = true
    error.value = null

    try {
      const headers: Record<string, string> = {}
      
      const authStore = useAuthStore()
      if (!authStore.isAuthenticated) {
        headers['X-Session-ID'] = sessionId.value
      }

      const response = await ApiService.put<Cart>(
        `/cart/${establishmentId}`,
        data,
        { headers }
      )

      cart.value = response
      return response
    } catch (err: any) {
      error.value = err.response?.data?.detail || 'Erro ao atualizar carrinho'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const getSummary = async (establishmentId: string): Promise<CartSummary> => {
    try {
      const headers: Record<string, string> = {}
      
      const authStore = useAuthStore()
      if (!authStore.isAuthenticated) {
        headers['X-Session-ID'] = sessionId.value
      }

      return await ApiService.get<CartSummary>(
        `/cart/${establishmentId}/summary`,
        { headers }
      )
    } catch (err: any) {
      error.value = err.response?.data?.detail || 'Erro ao obter resumo'
      throw err
    }
  }

  const validateCart = async (establishmentId: string) => {
    try {
      const headers: Record<string, string> = {}
      
      const authStore = useAuthStore()
      if (!authStore.isAuthenticated) {
        headers['X-Session-ID'] = sessionId.value
      }

      return await ApiService.get<{ is_valid: boolean; errors: string[] }>(
        `/cart/${establishmentId}/validate`,
        { headers }
      )
    } catch (err: any) {
      error.value = err.response?.data?.detail || 'Erro ao validar carrinho'
      throw err
    }
  }

  const checkout = async (
    establishmentId: string, 
    checkoutData: CheckoutRequest
  ): Promise<CheckoutResponse> => {
    isLoading.value = true
    error.value = null

    try {
      const headers: Record<string, string> = {}
      
      const authStore = useAuthStore()
      if (!authStore.isAuthenticated) {
        headers['X-Session-ID'] = sessionId.value
      }

      const response = await ApiService.post<CheckoutResponse>(
        `/checkout/${establishmentId}/checkout`,
        checkoutData,
        { headers }
      )

      // Limpar carrinho após checkout bem-sucedido
      cart.value = null
      
      return response
    } catch (err: any) {
      error.value = err.response?.data?.detail || 'Erro no checkout'
      throw err
    } finally {
      isLoading.value = false
    }
  }

  const getItemQuantity = (menuItemId: string): number => {
    const item = items.value.find(item => item.menu_item_id === menuItemId)
    return item?.quantity || 0
  }

  const hasItem = (menuItemId: string): boolean => {
    return items.value.some(item => item.menu_item_id === menuItemId)
  }

  // Reset store
  const $reset = () => {
    cart.value = null
    isLoading.value = false
    error.value = null
  }

  // Initialize on store creation
  initializeCart()

  return {
    // Estado
    cart,
    isLoading,
    error,
    sessionId,

    // Computed
    items,
    totalItems,
    subtotal,
    deliveryFee,
    serviceFee,
    total,
    isEmpty,
    isValidForCheckout,

    // Actions
    fetchCart,
    addItem,
    updateItem,
    removeItem,
    clearCart,
    updateCartInfo,
    getSummary,
    validateCart,
    checkout,
    getItemQuantity,
    hasItem,
    $reset
  }
})
