# Documentação do Sistema de Cardápio Digital

## Visão Geral
Este sistema permite que múltiplos estabelecimentos gerenciem seus cardápios digitais, pedidos e clientes através de uma plataforma unificada com arquitetura multi-tenant.

## Arquitetura

### Componentes Principais
- **Backend**: FastAPI (Python) com PostgreSQL
- **Frontend**: Vue.js 3 + Tailwind CSS
- **Mobile**: Flutter para clientes
- **Infraestrutura**: Docker + Redis + MinIO

### Fluxo de Dados
```
Cliente (Mobile) → API (FastAPI) → PostgreSQL
                ↓
Admin (Web) ← WebSocket ← Redis
```

## Configuração do Ambiente

### Pré-requisitos
- Docker e Docker Compose
- Node.js 18+ (para desenvolvimento frontend)
- Python 3.11+ (para desenvolvimento backend)
- Flutter SDK (para desenvolvimento mobile)

### Variáveis de Ambiente

#### Backend (.env)
```bash
# Banco de dados
DATABASE_URL=postgresql://cardapio_user:cardapio_password@localhost:5432/cardapio

# Redis
REDIS_URL=redis://localhost:6379

# MinIO
MINIO_ENDPOINT=localhost:9000
MINIO_ACCESS_KEY=cardapio_minio
MINIO_SECRET_KEY=cardapio_minio_password

# JWT
JWT_SECRET_KEY=your-super-secret-jwt-key
JWT_ALGORITHM=HS256
JWT_EXPIRE_MINUTES=30

# Mercado Pago
MERCADO_PAGO_ACCESS_TOKEN=your-mercado-pago-token
MERCADO_PAGO_PUBLIC_KEY=your-mercado-pago-public-key

# Ambiente
ENVIRONMENT=development
DEBUG=true
```

#### Frontend (.env)
```bash
VITE_API_URL=http://localhost:8000
VITE_ENVIRONMENT=development
```

## Executando o Projeto

### Desenvolvimento
```bash
# Clone o repositório
git clone https://github.com/reginaldobertoluci/cardapio.git
cd cardapio

# Execute os serviços de infraestrutura
docker-compose -f docker-compose.dev.yml up -d postgres redis minio

# Backend (em um terminal)
cd backend
python -m venv venv
source venv/bin/activate  # Linux/Mac
# ou venv\Scripts\activate  # Windows
pip install -r requirements.txt
uvicorn app.main:app --reload

# Frontend (em outro terminal)
cd frontend
npm install
npm run dev

# Mobile (em outro terminal)
cd mobile
flutter pub get
flutter run
```

### Produção
```bash
# Execute todos os serviços
docker-compose up -d

# Verifique os logs
docker-compose logs -f
```

## Estrutura do Projeto

```
cardapio/
├── backend/                 # API FastAPI
│   ├── app/
│   │   ├── api/            # Endpoints da API
│   │   ├── core/           # Configurações e segurança
│   │   ├── models/         # Modelos SQLAlchemy
│   │   ├── schemas/        # Schemas Pydantic
│   │   ├── services/       # Lógica de negócio
│   │   └── utils/          # Utilitários
│   ├── tests/              # Testes automatizados
│   ├── requirements.txt    # Dependências Python
│   └── Dockerfile         # Container backend
├── frontend/               # Interface web Vue.js
│   ├── src/
│   │   ├── components/     # Componentes Vue
│   │   ├── views/          # Páginas/Views
│   │   ├── stores/         # Estado global (Pinia)
│   │   ├── router/         # Roteamento
│   │   └── utils/          # Utilitários
│   ├── public/             # Arquivos estáticos
│   ├── package.json        # Dependências Node.js
│   └── Dockerfile         # Container frontend
├── mobile/                 # App Flutter
│   ├── lib/
│   │   ├── models/         # Modelos de dados
│   │   ├── screens/        # Telas do app
│   │   ├── services/       # Serviços e APIs
│   │   └── widgets/        # Widgets reutilizáveis
│   └── pubspec.yaml       # Dependências Flutter
├── docker/                 # Configurações Docker
│   ├── nginx/             # Configuração Nginx
│   └── postgres/          # Scripts PostgreSQL
├── docs/                   # Documentação
└── docker-compose.yml     # Orquestração de containers
```

## Próximos Passos
1. [Configuração do Backend](./backend.md)
2. [Configuração do Frontend](./frontend.md)
3. [Configuração do Mobile](./mobile.md)
4. [Deploy em Produção](./deploy.md)
