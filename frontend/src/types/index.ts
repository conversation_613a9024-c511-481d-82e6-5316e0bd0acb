// Tipos para o sistema de cardápio digital

// Filtros para o cardápio público
export interface MenuFilters {
  search: string
  categoryId: string
  isVegetarian: boolean
  isVegan: boolean
  isGlutenFree: boolean
  isSpicy: boolean
  minPrice: number
  maxPrice: number
  sortBy: string
  sortOrder: 'asc' | 'desc'
}

// Carrinho de compras
export interface CartItem {
  id: string
  cart_id: string
  menu_item_id: string
  quantity: number
  unit_price: number
  total_price: number
  item_name: string
  item_description?: string
  notes?: string
  created_at: string
  updated_at: string
}

export interface Cart {
  id: string
  session_id?: string
  user_id?: string
  establishment_id: string
  order_type: 'delivery' | 'pickup' | 'table_service'
  table_number?: number
  delivery_address?: string
  delivery_city?: string
  delivery_state?: string
  delivery_zip_code?: string
  delivery_latitude?: number
  delivery_longitude?: number
  customer_name?: string
  customer_phone?: string
  customer_email?: string
  notes?: string
  items: CartItem[]
  subtotal: number
  delivery_fee: number
  service_fee: number
  total_amount: number
  total_items: number
  created_at: string
  updated_at: string
  expires_at?: string
}

export interface CartSummary {
  total_items: number
  subtotal: number
  delivery_fee: number
  service_fee: number
  total_amount: number
  is_valid_for_checkout: boolean
  validation_errors: string[]
}

// Checkout
export interface CheckoutRequest {
  customer_name: string
  customer_phone: string
  customer_email?: string
  order_type: 'delivery' | 'pickup' | 'table_service'
  table_number?: number
  delivery_address?: string
  delivery_city?: string
  delivery_state?: string
  delivery_zip_code?: string
  delivery_latitude?: number
  delivery_longitude?: number
  payment_method: string
  notes?: string
}

export interface CheckoutResponse {
  order_id: string
  order_number: string
  payment_id?: string
  payment_url?: string
  qr_code?: string
  total_amount: number
  status: string
  estimated_prep_time?: number
  estimated_delivery_time?: number
}

// Acompanhamento de pedidos
export interface OrderTimeline {
  status: string
  timestamp?: string
  description: string
  is_current: boolean
  is_completed: boolean
}

export interface OrderTracking {
  order_id: string
  order_number: string
  status: string
  estimated_prep_time?: number
  estimated_delivery_time?: number
  timeline: OrderTimeline[]
  current_step: number
  total_steps: number
}

// Tipos para autenticação
export interface User {
  id: string
  email: string
  full_name: string
  phone?: string
  role: 'owner' | 'manager' | 'employee' | 'customer'
  establishment_id?: string
  is_active: boolean
  email_verified: boolean
  avatar_url?: string
}

export interface LoginCredentials {
  email: string
  password: string
}

export interface RegisterData {
  email: string
  password: string
  full_name: string
  phone?: string
  role?: 'customer' | 'employee' | 'manager' | 'owner'
  establishment_slug?: string
}

export interface AuthResponse {
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
  user: User
}

// Tipos para estabelecimentos
export interface Establishment {
  id: string
  name: string
  slug: string
  email: string
  phone?: string
  address?: string
  city?: string
  state?: string
  zip_code?: string
  country: string
  logo_url?: string
  banner_url?: string
  primary_color: string
  secondary_color: string
  accent_color: string
  accepts_orders: boolean
  delivery_fee: number
  minimum_order: number
  max_delivery_distance: number
  has_delivery: boolean
  has_pickup: boolean
  has_table_service: boolean
  accepts_pix: boolean
  accepts_card: boolean
  accepts_cash: boolean
  is_active: boolean
  is_open_now: boolean
  public_url: string
  created_at: string
  updated_at: string
}

export interface EstablishmentCreate {
  name: string
  slug: string
  email: string
  phone?: string
  address?: string
  city?: string
  state?: string
  zip_code?: string
  primary_color?: string
  secondary_color?: string
  accent_color?: string
  delivery_fee?: number
  minimum_order?: number
  max_delivery_distance?: number
  has_delivery?: boolean
  has_pickup?: boolean
  has_table_service?: boolean
  accepts_pix?: boolean
  accepts_card?: boolean
  accepts_cash?: boolean
}

export interface EstablishmentUpdate {
  name?: string
  email?: string
  phone?: string
  address?: string
  city?: string
  state?: string
  zip_code?: string
  primary_color?: string
  secondary_color?: string
  accent_color?: string
  delivery_fee?: number
  minimum_order?: number
  max_delivery_distance?: number
  has_delivery?: boolean
  has_pickup?: boolean
  has_table_service?: boolean
  accepts_pix?: boolean
  accepts_card?: boolean
  accepts_cash?: boolean
}

export interface EstablishmentStats {
  total_orders: number
  total_revenue: number
  total_customers: number
  total_menu_items: number
  average_rating?: number
  orders_today: number
  revenue_today: number
}

// Tipos para cardápio
export interface Category {
  id: string
  name: string
  description?: string
  image_url?: string
  sort_order: number
  is_featured: boolean
  available_all_day: boolean
  available_start_time?: string
  available_end_time?: string
  is_active: boolean
  created_at: string
  updated_at: string
}

export interface CategoryCreate {
  name: string
  description?: string
  sort_order?: number
  is_featured?: boolean
  available_all_day?: boolean
  available_start_time?: string
  available_end_time?: string
}

export interface CategoryUpdate {
  name?: string
  description?: string
  sort_order?: number
  is_featured?: boolean
  available_all_day?: boolean
  available_start_time?: string
  available_end_time?: string
}

export interface MenuItem {
  id: string
  category_id: string
  name: string
  description?: string
  price: number
  image_url?: string
  gallery_urls?: string[]
  is_available: boolean
  stock_quantity?: number
  calories?: number
  protein?: number
  carbs?: number
  fat?: number
  fiber?: number
  prep_time_minutes: number
  is_featured: boolean
  is_spicy: boolean
  is_vegetarian: boolean
  is_vegan: boolean
  is_gluten_free: boolean
  is_dairy_free: boolean
  sort_order: number
  is_active: boolean
  is_in_stock: boolean
  created_at: string
  updated_at: string
}

export interface MenuItemCreate {
  category_id: string
  name: string
  description?: string
  price: number
  is_available?: boolean
  stock_quantity?: number
  calories?: number
  protein?: number
  carbs?: number
  fat?: number
  fiber?: number
  prep_time_minutes?: number
  is_featured?: boolean
  is_spicy?: boolean
  is_vegetarian?: boolean
  is_vegan?: boolean
  is_gluten_free?: boolean
  is_dairy_free?: boolean
  sort_order?: number
}

export interface MenuItemUpdate {
  name?: string
  description?: string
  price?: number
  category_id?: string
  is_available?: boolean
  stock_quantity?: number
  calories?: number
  protein?: number
  carbs?: number
  fat?: number
  fiber?: number
  prep_time_minutes?: number
  is_featured?: boolean
  is_spicy?: boolean
  is_vegetarian?: boolean
  is_vegan?: boolean
  is_gluten_free?: boolean
  is_dairy_free?: boolean
  sort_order?: number
}

export interface CategoryWithItems extends Category {
  items: MenuItem[]
}

export interface Menu {
  establishment_id: string
  establishment_name: string
  categories: CategoryWithItems[]
}

// Tipos para pedidos
export interface Order {
  id: string
  order_number: string
  order_type: 'delivery' | 'pickup' | 'table_service'
  status: 'pending' | 'confirmed' | 'preparing' | 'ready' | 'out_for_delivery' | 'delivered' | 'cancelled' | 'refunded'
  subtotal: number
  delivery_fee: number
  service_fee: number
  discount_amount: number
  total_amount: number
  customer_name: string
  customer_phone: string
  customer_email?: string
  delivery_address?: string
  delivery_city?: string
  delivery_state?: string
  delivery_zip_code?: string
  notes?: string
  estimated_prep_time?: number
  estimated_delivery_time?: number
  created_at: string
  updated_at: string
  items: OrderItem[]
}

export interface OrderItem {
  id: string
  order_id: string
  menu_item_id: string
  quantity: number
  unit_price: number
  total_price: number
  item_name: string
  item_description?: string
  customizations?: any
  notes?: string
}

export interface CartItem {
  menu_item: MenuItem
  quantity: number
  customizations?: any
  notes?: string
}

// Tipos para upload
export interface UploadResponse {
  url: string
  message: string
}

export interface UploadProgress {
  loaded: number
  total: number
  percentage: number
}

// Tipos para filtros e paginação
export interface PaginationParams {
  page?: number
  per_page?: number
}

export interface EstablishmentFilters extends PaginationParams {
  city?: string
  state?: string
  search?: string
}

export interface MenuItemFilters extends PaginationParams {
  category_id?: string
  search?: string
  is_featured?: boolean
  is_available?: boolean
  include_inactive?: boolean
}

// Tipos para formulários
export interface FormField {
  name: string
  label: string
  type: 'text' | 'email' | 'password' | 'number' | 'textarea' | 'select' | 'checkbox' | 'file'
  placeholder?: string
  required?: boolean
  options?: { value: string | number; label: string }[]
  validation?: {
    min?: number
    max?: number
    pattern?: string
    message?: string
  }
}

// Tipos para notificações
export interface Toast {
  id: string
  type: 'success' | 'error' | 'warning' | 'info'
  title: string
  message?: string
  duration?: number
}

// Tipos para modais
export interface Modal {
  id: string
  title: string
  component: any
  props?: Record<string, any>
  size?: 'sm' | 'md' | 'lg' | 'xl'
  persistent?: boolean
}

// Tipos para loading states
export interface LoadingState {
  [key: string]: boolean
}

// Tipos para erros
export interface ValidationError {
  field: string
  message: string
}

export interface ApiError {
  message: string
  errors?: ValidationError[]
  status?: number
}
