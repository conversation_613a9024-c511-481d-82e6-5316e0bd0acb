<template>
  <div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
      <div>
        <h1 class="text-2xl font-bold text-gray-900">Gestão de Pedidos</h1>
        <p class="text-gray-600">Acompanhe e gerencie todos os pedidos em tempo real</p>
      </div>
      
      <!-- Status do estabelecimento -->
      <div class="flex items-center space-x-4">
        <div class="flex items-center space-x-2">
          <div :class="[
            'w-3 h-3 rounded-full',
            isAcceptingOrders ? 'bg-success-400' : 'bg-error-400'
          ]"></div>
          <span class="text-sm font-medium text-gray-700">
            {{ isAcceptingOrders ? 'Aceitando pedidos' : 'Pedidos pausados' }}
          </span>
        </div>
        
        <button
          @click="toggleOrders"
          :disabled="isTogglingOrders"
          :class="[
            'btn btn-sm',
            isAcceptingOrders ? 'btn-error' : 'btn-success'
          ]"
        >
          {{ isAcceptingOrders ? 'Pausar pedidos' : 'Aceitar pedidos' }}
        </button>
      </div>
    </div>

    <!-- Estatísticas rápidas -->
    <div class="grid grid-cols-1 gap-5 sm:grid-cols-2 lg:grid-cols-4">
      <div class="card">
        <div class="card-body">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <ClockIcon class="h-8 w-8 text-warning-600" />
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">
                  Pendentes
                </dt>
                <dd class="text-lg font-medium text-gray-900">
                  {{ orderStats.pending }}
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="card">
        <div class="card-body">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <FireIcon class="h-8 w-8 text-error-600" />
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">
                  Preparando
                </dt>
                <dd class="text-lg font-medium text-gray-900">
                  {{ orderStats.preparing }}
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="card">
        <div class="card-body">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <TruckIcon class="h-8 w-8 text-primary-600" />
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">
                  Saindo
                </dt>
                <dd class="text-lg font-medium text-gray-900">
                  {{ orderStats.outForDelivery }}
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>

      <div class="card">
        <div class="card-body">
          <div class="flex items-center">
            <div class="flex-shrink-0">
              <CurrencyDollarIcon class="h-8 w-8 text-success-600" />
            </div>
            <div class="ml-5 w-0 flex-1">
              <dl>
                <dt class="text-sm font-medium text-gray-500 truncate">
                  Hoje
                </dt>
                <dd class="text-lg font-medium text-gray-900">
                  {{ formatCurrency(orderStats.todayRevenue) }}
                </dd>
              </dl>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Filtros -->
    <div class="card">
      <div class="card-body">
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
          <!-- Busca -->
          <div>
            <input
              v-model="searchQuery"
              type="text"
              placeholder="Buscar por número, cliente..."
              class="input"
            />
          </div>
          
          <!-- Status -->
          <div>
            <select v-model="selectedStatus" class="input">
              <option value="">Todos os status</option>
              <option value="pending">Pendente</option>
              <option value="confirmed">Confirmado</option>
              <option value="preparing">Preparando</option>
              <option value="ready">Pronto</option>
              <option value="out_for_delivery">Saindo para entrega</option>
              <option value="delivered">Entregue</option>
              <option value="cancelled">Cancelado</option>
            </select>
          </div>
          
          <!-- Tipo -->
          <div>
            <select v-model="selectedType" class="input">
              <option value="">Todos os tipos</option>
              <option value="delivery">Delivery</option>
              <option value="pickup">Retirada</option>
              <option value="table_service">Mesa</option>
            </select>
          </div>
          
          <!-- Data -->
          <div>
            <input
              v-model="selectedDate"
              type="date"
              class="input"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Lista de pedidos -->
    <div class="space-y-4">
      <!-- Loading state -->
      <div v-if="isLoading" class="flex justify-center py-12">
        <div class="spinner w-8 h-8"></div>
      </div>

      <!-- Empty state -->
      <div v-else-if="filteredOrders.length === 0" class="text-center py-12">
        <ShoppingBagIcon class="mx-auto h-12 w-12 text-gray-400" />
        <h3 class="mt-2 text-sm font-medium text-gray-900">Nenhum pedido encontrado</h3>
        <p class="mt-1 text-sm text-gray-500">
          {{ hasFilters ? 'Tente ajustar os filtros de busca.' : 'Aguardando novos pedidos...' }}
        </p>
      </div>

      <!-- Lista de pedidos -->
      <div v-else class="space-y-4">
        <div
          v-for="order in filteredOrders"
          :key="order.id"
          :class="[
            'card cursor-pointer transition-all duration-200 hover:shadow-medium',
            getOrderBorderClass(order.status)
          ]"
          @click="openOrderDetails(order)"
        >
          <div class="card-body">
            <div class="flex items-center justify-between">
              <!-- Informações principais -->
              <div class="flex items-center space-x-4">
                <!-- Ícone do tipo -->
                <div :class="[
                  'flex-shrink-0 w-12 h-12 rounded-full flex items-center justify-center',
                  getOrderTypeClass(order.order_type)
                ]">
                  <component :is="getOrderTypeIcon(order.order_type)" class="h-6 w-6" />
                </div>

                <!-- Detalhes do pedido -->
                <div>
                  <div class="flex items-center space-x-2">
                    <h3 class="text-lg font-medium text-gray-900">
                      #{{ order.order_number }}
                    </h3>
                    <span :class="[
                      'badge',
                      getStatusBadgeClass(order.status)
                    ]">
                      {{ getStatusLabel(order.status) }}
                    </span>
                  </div>
                  
                  <p class="text-sm text-gray-600 mt-1">
                    {{ order.customer_name }} • {{ order.customer_phone }}
                  </p>
                  
                  <div class="flex items-center space-x-4 mt-2 text-xs text-gray-500">
                    <span>{{ formatDateTime(order.created_at) }}</span>
                    <span v-if="order.estimated_prep_time">
                      Preparo: {{ order.estimated_prep_time }}min
                    </span>
                    <span>{{ order.items?.length || 0 }} itens</span>
                  </div>
                </div>
              </div>

              <!-- Valor e ações -->
              <div class="flex items-center space-x-4">
                <!-- Valor total -->
                <div class="text-right">
                  <p class="text-lg font-bold text-gray-900">
                    {{ formatCurrency(order.total_amount) }}
                  </p>
                  <p v-if="order.order_type === 'delivery'" class="text-xs text-gray-500">
                    + {{ formatCurrency(order.delivery_fee) }} entrega
                  </p>
                </div>

                <!-- Ações rápidas -->
                <div class="flex flex-col space-y-2">
                  <button
                    v-if="canAdvanceStatus(order.status)"
                    @click.stop="advanceOrderStatus(order)"
                    :class="[
                      'btn btn-sm',
                      getNextActionClass(order.status)
                    ]"
                  >
                    {{ getNextActionLabel(order.status) }}
                  </button>
                  
                  <button
                    v-if="canCancelOrder(order.status)"
                    @click.stop="confirmCancelOrder(order)"
                    class="btn btn-sm btn-error"
                  >
                    Cancelar
                  </button>
                </div>
              </div>
            </div>

            <!-- Endereço de entrega (se delivery) -->
            <div v-if="order.order_type === 'delivery' && order.delivery_address" class="mt-4 pt-4 border-t border-gray-200">
              <div class="flex items-start space-x-2">
                <MapPinIcon class="h-4 w-4 text-gray-400 mt-0.5" />
                <div class="text-sm text-gray-600">
                  <p>{{ order.delivery_address }}</p>
                  <p v-if="order.delivery_city">
                    {{ order.delivery_city }}, {{ order.delivery_state }} - {{ order.delivery_zip_code }}
                  </p>
                </div>
              </div>
            </div>

            <!-- Observações -->
            <div v-if="order.notes" class="mt-4 pt-4 border-t border-gray-200">
              <div class="flex items-start space-x-2">
                <ChatBubbleLeftIcon class="h-4 w-4 text-gray-400 mt-0.5" />
                <p class="text-sm text-gray-600">{{ order.notes }}</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de detalhes do pedido -->
    <OrderDetailsModal
      v-if="showOrderDetails"
      :order="selectedOrder"
      @close="closeOrderDetails"
      @status-updated="handleOrderStatusUpdated"
    />

    <!-- Modal de confirmação de cancelamento -->
    <ConfirmModal
      v-if="showCancelConfirm"
      title="Cancelar pedido"
      :message="`Tem certeza que deseja cancelar o pedido #${orderToCancel?.order_number}? Esta ação não pode ser desfeita.`"
      confirm-text="Cancelar pedido"
      confirm-class="btn-error"
      type="danger"
      @confirm="handleCancelOrder"
      @cancel="closeCancelConfirm"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, onUnmounted } from 'vue'
import {
  ClockIcon,
  FireIcon,
  TruckIcon,
  CurrencyDollarIcon,
  ShoppingBagIcon,
  MapPinIcon,
  ChatBubbleLeftIcon,
  HomeIcon,
  BuildingStorefrontIcon
} from '@heroicons/vue/24/outline'
import { useToast } from 'vue-toastification'

import { useEstablishmentStore } from '@/stores/establishment'
import type { Order } from '@/types'

// Componentes (serão criados posteriormente)
const OrderDetailsModal = defineAsyncComponent(() => import('@/components/modals/OrderDetailsModal.vue'))
const ConfirmModal = defineAsyncComponent(() => import('@/components/modals/ConfirmModal.vue'))

const toast = useToast()
const establishmentStore = useEstablishmentStore()

// Estado local
const orders = ref<Order[]>([])
const isLoading = ref(false)
const searchQuery = ref('')
const selectedStatus = ref('')
const selectedType = ref('')
const selectedDate = ref('')
const showOrderDetails = ref(false)
const showCancelConfirm = ref(false)
const selectedOrder = ref<Order | null>(null)
const orderToCancel = ref<Order | null>(null)
const isTogglingOrders = ref(false)

// Computed
const establishment = computed(() => establishmentStore.currentEstablishment)
const isAcceptingOrders = computed(() => establishment.value?.accepts_orders || false)

const hasFilters = computed(() => {
  return searchQuery.value || selectedStatus.value || selectedType.value || selectedDate.value
})

const filteredOrders = computed(() => {
  let filtered = orders.value

  // Filtrar por busca
  if (searchQuery.value) {
    const query = searchQuery.value.toLowerCase()
    filtered = filtered.filter(order =>
      order.order_number.toLowerCase().includes(query) ||
      order.customer_name.toLowerCase().includes(query) ||
      order.customer_phone.includes(query)
    )
  }

  // Filtrar por status
  if (selectedStatus.value) {
    filtered = filtered.filter(order => order.status === selectedStatus.value)
  }

  // Filtrar por tipo
  if (selectedType.value) {
    filtered = filtered.filter(order => order.order_type === selectedType.value)
  }

  // Filtrar por data
  if (selectedDate.value) {
    filtered = filtered.filter(order => {
      const orderDate = new Date(order.created_at).toISOString().split('T')[0]
      return orderDate === selectedDate.value
    })
  }

  // Ordenar por data (mais recentes primeiro)
  return filtered.sort((a, b) => new Date(b.created_at).getTime() - new Date(a.created_at).getTime())
})

const orderStats = computed(() => {
  const stats = {
    pending: 0,
    preparing: 0,
    outForDelivery: 0,
    todayRevenue: 0
  }

  const today = new Date().toISOString().split('T')[0]

  orders.value.forEach(order => {
    // Contar por status
    if (order.status === 'pending') stats.pending++
    else if (['confirmed', 'preparing'].includes(order.status)) stats.preparing++
    else if (order.status === 'out_for_delivery') stats.outForDelivery++

    // Calcular receita de hoje
    const orderDate = new Date(order.created_at).toISOString().split('T')[0]
    if (orderDate === today && order.status !== 'cancelled') {
      stats.todayRevenue += order.total_amount
    }
  })

  return stats
})

// Métodos
const formatCurrency = (value: number): string => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value)
}

const formatDateTime = (dateString: string): string => {
  return new Intl.DateTimeFormat('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  }).format(new Date(dateString))
}

const getOrderBorderClass = (status: string): string => {
  switch (status) {
    case 'pending':
      return 'border-l-4 border-warning-400'
    case 'confirmed':
    case 'preparing':
      return 'border-l-4 border-primary-400'
    case 'ready':
    case 'out_for_delivery':
      return 'border-l-4 border-success-400'
    case 'delivered':
      return 'border-l-4 border-gray-400'
    case 'cancelled':
      return 'border-l-4 border-error-400'
    default:
      return 'border-l-4 border-gray-200'
  }
}

const getOrderTypeClass = (type: string): string => {
  switch (type) {
    case 'delivery':
      return 'bg-primary-100 text-primary-600'
    case 'pickup':
      return 'bg-warning-100 text-warning-600'
    case 'table_service':
      return 'bg-success-100 text-success-600'
    default:
      return 'bg-gray-100 text-gray-600'
  }
}

const getOrderTypeIcon = (type: string) => {
  switch (type) {
    case 'delivery':
      return TruckIcon
    case 'pickup':
      return BuildingStorefrontIcon
    case 'table_service':
      return HomeIcon
    default:
      return ShoppingBagIcon
  }
}

const getStatusBadgeClass = (status: string): string => {
  switch (status) {
    case 'pending':
      return 'badge-warning'
    case 'confirmed':
    case 'preparing':
      return 'badge-primary'
    case 'ready':
    case 'out_for_delivery':
      return 'badge-success'
    case 'delivered':
      return 'badge-secondary'
    case 'cancelled':
      return 'badge-error'
    default:
      return 'badge-secondary'
  }
}

const getStatusLabel = (status: string): string => {
  const labels = {
    pending: 'Pendente',
    confirmed: 'Confirmado',
    preparing: 'Preparando',
    ready: 'Pronto',
    out_for_delivery: 'Saindo',
    delivered: 'Entregue',
    cancelled: 'Cancelado'
  }
  return labels[status as keyof typeof labels] || status
}

const canAdvanceStatus = (status: string): boolean => {
  return ['pending', 'confirmed', 'preparing', 'ready'].includes(status)
}

const canCancelOrder = (status: string): boolean => {
  return ['pending', 'confirmed', 'preparing'].includes(status)
}

const getNextActionClass = (status: string): string => {
  switch (status) {
    case 'pending':
      return 'btn-success'
    case 'confirmed':
    case 'preparing':
      return 'btn-primary'
    case 'ready':
      return 'btn-warning'
    default:
      return 'btn-primary'
  }
}

const getNextActionLabel = (status: string): string => {
  switch (status) {
    case 'pending':
      return 'Confirmar'
    case 'confirmed':
      return 'Preparar'
    case 'preparing':
      return 'Pronto'
    case 'ready':
      return 'Saiu'
    default:
      return 'Avançar'
  }
}

const openOrderDetails = (order: Order) => {
  selectedOrder.value = order
  showOrderDetails.value = true
}

const closeOrderDetails = () => {
  showOrderDetails.value = false
  selectedOrder.value = null
}

const confirmCancelOrder = (order: Order) => {
  orderToCancel.value = order
  showCancelConfirm.value = true
}

const closeCancelConfirm = () => {
  showCancelConfirm.value = false
  orderToCancel.value = null
}

const advanceOrderStatus = async (order: Order) => {
  try {
    // Aqui seria feita a chamada para a API
    // await orderStore.updateOrderStatus(order.id, nextStatus)
    
    toast.success('Status do pedido atualizado!')
    await loadOrders()
  } catch (error) {
    console.error('Erro ao atualizar status:', error)
    toast.error('Erro ao atualizar status do pedido')
  }
}

const handleCancelOrder = async () => {
  if (!orderToCancel.value) return

  try {
    // Aqui seria feita a chamada para a API
    // await orderStore.cancelOrder(orderToCancel.value.id)
    
    toast.success('Pedido cancelado com sucesso!')
    closeCancelConfirm()
    await loadOrders()
  } catch (error) {
    console.error('Erro ao cancelar pedido:', error)
    toast.error('Erro ao cancelar pedido')
  }
}

const handleOrderStatusUpdated = async () => {
  await loadOrders()
  closeOrderDetails()
}

const toggleOrders = async () => {
  isTogglingOrders.value = true
  
  try {
    await establishmentStore.toggleAcceptOrders()
    
    const message = isAcceptingOrders.value 
      ? 'Agora você está aceitando pedidos!' 
      : 'Pedidos pausados temporariamente'
    
    toast.success(message)
  } catch (error) {
    console.error('Erro ao alterar status de pedidos:', error)
    toast.error('Erro ao alterar status de pedidos')
  } finally {
    isTogglingOrders.value = false
  }
}

const loadOrders = async () => {
  isLoading.value = true
  
  try {
    // Aqui seria feita a chamada para a API
    // orders.value = await orderStore.fetchOrders()
    
    // Dados mockados para demonstração
    orders.value = [
      {
        id: '1',
        order_number: '001',
        order_type: 'delivery',
        status: 'pending',
        subtotal: 45.90,
        delivery_fee: 5.00,
        service_fee: 0,
        discount_amount: 0,
        total_amount: 50.90,
        customer_name: 'João Silva',
        customer_phone: '(11) 99999-9999',
        customer_email: '<EMAIL>',
        delivery_address: 'Rua das Flores, 123',
        delivery_city: 'São Paulo',
        delivery_state: 'SP',
        delivery_zip_code: '01234-567',
        notes: 'Sem cebola, por favor',
        estimated_prep_time: 30,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
        items: []
      }
    ]
  } catch (error) {
    console.error('Erro ao carregar pedidos:', error)
    toast.error('Erro ao carregar pedidos')
  } finally {
    isLoading.value = false
  }
}

// Lifecycle
onMounted(() => {
  loadOrders()
  
  // Configurar atualização automática a cada 30 segundos
  const interval = setInterval(loadOrders, 30000)
  
  onUnmounted(() => {
    clearInterval(interval)
  })
})

// Lazy loading de componentes
import { defineAsyncComponent } from 'vue'
</script>
