<template>
  <form @submit.prevent="handleSubmit" class="space-y-6">
    <!-- Email -->
    <div class="form-group">
      <label for="email" class="form-label">
        Email
      </label>
      <input
        id="email"
        v-model="form.email"
        type="email"
        autocomplete="email"
        required
        :class="[
          'input',
          errors.email ? 'input-error' : ''
        ]"
        placeholder="<EMAIL>"
        :disabled="isLoading"
      />
      <p v-if="errors.email" class="form-error">
        {{ errors.email }}
      </p>
    </div>

    <!-- Senha -->
    <div class="form-group">
      <label for="password" class="form-label">
        Senha
      </label>
      <div class="relative">
        <input
          id="password"
          v-model="form.password"
          :type="showPassword ? 'text' : 'password'"
          autocomplete="current-password"
          required
          :class="[
            'input pr-10',
            errors.password ? 'input-error' : ''
          ]"
          placeholder="Sua senha"
          :disabled="isLoading"
        />
        <button
          type="button"
          class="absolute inset-y-0 right-0 pr-3 flex items-center"
          @click="showPassword = !showPassword"
          :disabled="isLoading"
        >
          <EyeIcon v-if="!showPassword" class="h-5 w-5 text-gray-400 hover:text-gray-500" />
          <EyeSlashIcon v-else class="h-5 w-5 text-gray-400 hover:text-gray-500" />
        </button>
      </div>
      <p v-if="errors.password" class="form-error">
        {{ errors.password }}
      </p>
    </div>

    <!-- Lembrar de mim e Esqueci a senha -->
    <div class="flex items-center justify-between">
      <div class="flex items-center">
        <input
          id="remember-me"
          v-model="form.rememberMe"
          type="checkbox"
          class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
          :disabled="isLoading"
        />
        <label for="remember-me" class="ml-2 block text-sm text-gray-900">
          Lembrar de mim
        </label>
      </div>

      <div class="text-sm">
        <router-link
          to="/auth/forgot-password"
          class="font-medium text-primary-600 hover:text-primary-500 transition-colors duration-200"
        >
          Esqueceu a senha?
        </router-link>
      </div>
    </div>

    <!-- Botão de submit -->
    <div>
      <button
        type="submit"
        :disabled="isLoading || !isFormValid"
        class="btn btn-primary w-full btn-lg"
      >
        <div v-if="isLoading" class="flex items-center justify-center">
          <div class="spinner w-5 h-5 mr-2"></div>
          Entrando...
        </div>
        <span v-else>Entrar</span>
      </button>
    </div>

    <!-- Erro geral -->
    <div v-if="generalError" class="rounded-md bg-error-50 p-4">
      <div class="flex">
        <ExclamationTriangleIcon class="h-5 w-5 text-error-400" />
        <div class="ml-3">
          <h3 class="text-sm font-medium text-error-800">
            Erro ao fazer login
          </h3>
          <div class="mt-2 text-sm text-error-700">
            {{ generalError }}
          </div>
        </div>
      </div>
    </div>

    <!-- Divider -->
    <div class="mt-6">
      <div class="relative">
        <div class="absolute inset-0 flex items-center">
          <div class="w-full border-t border-gray-300" />
        </div>
        <div class="relative flex justify-center text-sm">
          <span class="px-2 bg-white text-gray-500">
            Ou continue com
          </span>
        </div>
      </div>

      <!-- Login social (placeholder) -->
      <div class="mt-6 grid grid-cols-2 gap-3">
        <button
          type="button"
          class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors duration-200"
          disabled
        >
          <svg class="w-5 h-5" viewBox="0 0 24 24">
            <path fill="currentColor" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
            <path fill="currentColor" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
            <path fill="currentColor" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
            <path fill="currentColor" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
          </svg>
          <span class="ml-2">Google</span>
        </button>

        <button
          type="button"
          class="w-full inline-flex justify-center py-2 px-4 border border-gray-300 rounded-md shadow-sm bg-white text-sm font-medium text-gray-500 hover:bg-gray-50 transition-colors duration-200"
          disabled
        >
          <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 24 24">
            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
          </svg>
          <span class="ml-2">Facebook</span>
        </button>
      </div>
    </div>
  </form>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { EyeIcon, EyeSlashIcon, ExclamationTriangleIcon } from '@heroicons/vue/24/outline'
import { useToast } from 'vue-toastification'

import { useAuthStore } from '@/stores/auth'
import type { LoginCredentials } from '@/types'

const router = useRouter()
const route = useRoute()
const toast = useToast()
const authStore = useAuthStore()

// Estado do formulário
const form = ref<LoginCredentials & { rememberMe: boolean }>({
  email: '',
  password: '',
  rememberMe: false
})

const showPassword = ref(false)
const isLoading = ref(false)
const errors = ref<Record<string, string>>({})
const generalError = ref('')

// Validação
const isFormValid = computed(() => {
  return form.value.email && 
         form.value.password && 
         form.value.email.includes('@') &&
         form.value.password.length >= 6
})

// Validar campos individuais
const validateField = (field: string, value: string) => {
  errors.value[field] = ''
  
  switch (field) {
    case 'email':
      if (!value) {
        errors.value[field] = 'Email é obrigatório'
      } else if (!value.includes('@')) {
        errors.value[field] = 'Email deve ter um formato válido'
      }
      break
      
    case 'password':
      if (!value) {
        errors.value[field] = 'Senha é obrigatória'
      } else if (value.length < 6) {
        errors.value[field] = 'Senha deve ter pelo menos 6 caracteres'
      }
      break
  }
}

// Validar formulário completo
const validateForm = () => {
  validateField('email', form.value.email)
  validateField('password', form.value.password)
  
  return Object.values(errors.value).every(error => !error)
}

// Submeter formulário
const handleSubmit = async () => {
  generalError.value = ''
  
  if (!validateForm()) {
    return
  }
  
  isLoading.value = true
  
  try {
    await authStore.login({
      email: form.value.email,
      password: form.value.password
    })
    
    toast.success('Login realizado com sucesso!')
    
    // Redirecionar para a página solicitada ou dashboard
    const redirectTo = route.query.redirect as string || '/dashboard'
    router.push(redirectTo)
    
  } catch (error: any) {
    console.error('Erro no login:', error)
    
    if (error.response?.status === 401) {
      generalError.value = 'Email ou senha incorretos'
    } else if (error.response?.status === 422) {
      // Erros de validação do servidor
      const serverErrors = error.response.data.detail
      if (Array.isArray(serverErrors)) {
        serverErrors.forEach((err: any) => {
          const field = err.loc?.[1] // Pegar o nome do campo
          if (field && ['email', 'password'].includes(field)) {
            errors.value[field] = err.msg
          }
        })
      }
    } else {
      generalError.value = 'Ocorreu um erro inesperado. Tente novamente.'
    }
  } finally {
    isLoading.value = false
  }
}

// Limpar erros quando o usuário digitar
const clearFieldError = (field: string) => {
  if (errors.value[field]) {
    errors.value[field] = ''
  }
  if (generalError.value) {
    generalError.value = ''
  }
}

// Watchers para validação em tempo real
import { watch } from 'vue'

watch(() => form.value.email, (newValue) => {
  clearFieldError('email')
  if (newValue) {
    validateField('email', newValue)
  }
})

watch(() => form.value.password, (newValue) => {
  clearFieldError('password')
  if (newValue) {
    validateField('password', newValue)
  }
})

// Focar no primeiro campo ao montar
onMounted(() => {
  const emailInput = document.getElementById('email')
  if (emailInput) {
    emailInput.focus()
  }
})
</script>
