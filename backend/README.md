# Backend - API FastAPI

## Visão Geral
API REST desenvolvida em FastAPI com arquitetura multi-tenant para gerenciamento de cardápios digitais.

## Tecnologias
- **FastAPI**: Framework web moderno e rápido
- **SQLAlchemy**: ORM para PostgreSQL
- **Pydantic**: Validação de dados e serialização
- **JWT**: Autenticação e autorização
- **Redis**: Cache e filas de tarefas
- **MinIO**: Armazenamento de imagens
- **WebSockets**: Comunicação em tempo real

## Estrutura
```
backend/
├── app/
│   ├── api/                # Endpoints da API
│   │   ├── v1/            # Versão 1 da API
│   │   │   ├── auth.py    # Autenticação
│   │   │   ├── establishments.py  # Estabelecimentos
│   │   │   ├── menu.py    # Cardápio
│   │   │   ├── orders.py  # Pedidos
│   │   │   └── users.py   # Usuários
│   │   └── deps.py        # Dependências
│   ├── core/              # Configurações centrais
│   │   ├── config.py      # Configurações
│   │   ├── security.py    # Segurança e JWT
│   │   └── database.py    # Conexão com banco
│   ├── models/            # Modelos SQLAlchemy
│   │   ├── establishment.py
│   │   ├── user.py
│   │   ├── menu.py
│   │   └── order.py
│   ├── schemas/           # Schemas Pydantic
│   │   ├── establishment.py
│   │   ├── user.py
│   │   ├── menu.py
│   │   └── order.py
│   ├── services/          # Lógica de negócio
│   │   ├── auth_service.py
│   │   ├── menu_service.py
│   │   ├── order_service.py
│   │   └── payment_service.py
│   ├── utils/             # Utilitários
│   │   ├── minio_client.py
│   │   ├── redis_client.py
│   │   └── websocket.py
│   └── main.py           # Aplicação principal
├── tests/                # Testes automatizados
├── requirements.txt      # Dependências
├── Dockerfile           # Container produção
├── Dockerfile.dev       # Container desenvolvimento
└── README.md           # Esta documentação
```

## Configuração

### Instalação
```bash
cd backend
python -m venv venv
source venv/bin/activate  # Linux/Mac
pip install -r requirements.txt
```

### Variáveis de Ambiente
Copie `.env.example` para `.env` e configure:
```bash
cp ../.env.example .env
```

### Executar
```bash
# Desenvolvimento
uvicorn app.main:app --reload

# Produção
uvicorn app.main:app --host 0.0.0.0 --port 8000
```

## Funcionalidades

### Autenticação Multi-nível
- **Proprietários**: Acesso total ao estabelecimento
- **Funcionários**: Gerenciamento de pedidos
- **Clientes**: Fazer pedidos e acompanhar status

### Multi-tenancy
- Isolamento de dados por estabelecimento
- Personalização por tenant
- Subdominios ou rotas personalizadas

### APIs Principais
- `/api/v1/auth/*` - Autenticação e autorização
- `/api/v1/establishments/*` - Gestão de estabelecimentos
- `/api/v1/menu/*` - Gestão de cardápio
- `/api/v1/orders/*` - Gestão de pedidos
- `/api/v1/payments/*` - Processamento de pagamentos

### WebSockets
- Atualizações em tempo real de pedidos
- Notificações para estabelecimentos
- Status de pagamentos

## Testes
```bash
# Executar todos os testes
pytest

# Testes com cobertura
pytest --cov=app

# Testes específicos
pytest tests/test_auth.py
```

## Documentação da API
Acesse `http://localhost:8000/docs` para ver a documentação interativa do Swagger.

## Próximos Passos
1. Implementar modelos de dados
2. Configurar autenticação JWT
3. Criar endpoints da API
4. Integrar pagamentos
5. Implementar WebSockets
