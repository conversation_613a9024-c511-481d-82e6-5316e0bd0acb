"""
API endpoints para carrinho de compras
"""
from fastapi import APIRouter, Depends, HTTPException, status, Request
from sqlalchemy.orm import Session
from typing import Optional
import uuid
from datetime import datetime, timedelta

from app.database import get_db
from app.models.cart import Cart, CartItem
from app.models.menu import MenuItem
from app.models.establishment import Establishment
from app.schemas.cart import (
    CartResponse, CartCreate, CartUpdate, CartItemResponse,
    AddToCartRequest, UpdateCartItemRequest, CartSummary,
    CartValidation
)
from app.api.deps import get_optional_current_user
from app.models.user import User

router = APIRouter()


def get_or_create_cart(
    establishment_id: str,
    db: Session,
    user: Optional[User] = None,
    session_id: Optional[str] = None
) -> Cart:
    """Obter ou criar carrinho para o usuário/sessão"""
    
    # Buscar carrinho existente
    query = db.query(Cart).filter(Cart.establishment_id == establishment_id)
    
    if user:
        query = query.filter(Cart.user_id == user.id)
    elif session_id:
        query = query.filter(Cart.session_id == session_id)
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User ID ou Session ID é obrigatório"
        )
    
    cart = query.first()
    
    if not cart:
        # Criar novo carrinho
        cart = Cart(
            establishment_id=establishment_id,
            user_id=user.id if user else None,
            session_id=session_id if not user else None,
            expires_at=datetime.utcnow() + timedelta(hours=24)  # Expira em 24h
        )
        db.add(cart)
        db.commit()
        db.refresh(cart)
    
    return cart


def get_session_id(request: Request) -> str:
    """Obter ou gerar session ID"""
    session_id = request.headers.get("X-Session-ID")
    if not session_id:
        session_id = str(uuid.uuid4())
    return session_id


@router.get("/{establishment_id}", response_model=CartResponse)
async def get_cart(
    establishment_id: str,
    request: Request,
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional)
):
    """Obter carrinho atual"""
    
    # Verificar se estabelecimento existe
    establishment = db.query(Establishment).filter(
        Establishment.id == establishment_id
    ).first()
    
    if not establishment:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Estabelecimento não encontrado"
        )
    
    session_id = get_session_id(request) if not current_user else None
    cart = get_or_create_cart(establishment_id, db, current_user, session_id)
    
    return cart


@router.post("/{establishment_id}/items", response_model=CartItemResponse)
async def add_to_cart(
    establishment_id: str,
    item_data: AddToCartRequest,
    request: Request,
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional)
):
    """Adicionar item ao carrinho"""
    
    # Verificar se o item do menu existe e pertence ao estabelecimento
    menu_item = db.query(MenuItem).filter(
        MenuItem.id == item_data.menu_item_id,
        MenuItem.establishment_id == establishment_id,
        MenuItem.is_available == True
    ).first()
    
    if not menu_item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Item do menu não encontrado ou indisponível"
        )
    
    # Verificar estoque se aplicável
    if menu_item.stock_quantity is not None:
        if menu_item.stock_quantity < item_data.quantity:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Estoque insuficiente. Disponível: {menu_item.stock_quantity}"
            )
    
    session_id = get_session_id(request) if not current_user else None
    cart = get_or_create_cart(establishment_id, db, current_user, session_id)
    
    # Verificar se o item já existe no carrinho
    existing_item = db.query(CartItem).filter(
        CartItem.cart_id == cart.id,
        CartItem.menu_item_id == item_data.menu_item_id
    ).first()
    
    if existing_item:
        # Atualizar quantidade
        new_quantity = existing_item.quantity + item_data.quantity
        
        # Verificar estoque novamente
        if menu_item.stock_quantity is not None:
            if menu_item.stock_quantity < new_quantity:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Estoque insuficiente. Disponível: {menu_item.stock_quantity}"
                )
        
        existing_item.quantity = new_quantity
        existing_item.notes = item_data.notes
        existing_item.update_price_from_menu_item()
        
        db.commit()
        db.refresh(existing_item)
        return existing_item
    else:
        # Criar novo item no carrinho
        cart_item = CartItem(
            cart_id=cart.id,
            menu_item_id=item_data.menu_item_id,
            quantity=item_data.quantity,
            unit_price=menu_item.price,
            notes=item_data.notes
        )
        
        db.add(cart_item)
        db.commit()
        db.refresh(cart_item)
        return cart_item


@router.put("/{establishment_id}/items/{item_id}", response_model=CartItemResponse)
async def update_cart_item(
    establishment_id: str,
    item_id: str,
    item_data: UpdateCartItemRequest,
    request: Request,
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional)
):
    """Atualizar item do carrinho"""
    
    session_id = get_session_id(request) if not current_user else None
    cart = get_or_create_cart(establishment_id, db, current_user, session_id)
    
    # Buscar item do carrinho
    cart_item = db.query(CartItem).filter(
        CartItem.id == item_id,
        CartItem.cart_id == cart.id
    ).first()
    
    if not cart_item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Item não encontrado no carrinho"
        )
    
    # Verificar estoque se aplicável
    if cart_item.menu_item.stock_quantity is not None:
        if cart_item.menu_item.stock_quantity < item_data.quantity:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Estoque insuficiente. Disponível: {cart_item.menu_item.stock_quantity}"
            )
    
    # Atualizar item
    cart_item.quantity = item_data.quantity
    cart_item.notes = item_data.notes
    cart_item.update_price_from_menu_item()
    
    db.commit()
    db.refresh(cart_item)
    return cart_item


@router.delete("/{establishment_id}/items/{item_id}")
async def remove_from_cart(
    establishment_id: str,
    item_id: str,
    request: Request,
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional)
):
    """Remover item do carrinho"""
    
    session_id = get_session_id(request) if not current_user else None
    cart = get_or_create_cart(establishment_id, db, current_user, session_id)
    
    # Buscar item do carrinho
    cart_item = db.query(CartItem).filter(
        CartItem.id == item_id,
        CartItem.cart_id == cart.id
    ).first()
    
    if not cart_item:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Item não encontrado no carrinho"
        )
    
    db.delete(cart_item)
    db.commit()
    
    return {"message": "Item removido do carrinho"}


@router.delete("/{establishment_id}/clear")
async def clear_cart(
    establishment_id: str,
    request: Request,
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional)
):
    """Limpar carrinho"""
    
    session_id = get_session_id(request) if not current_user else None
    cart = get_or_create_cart(establishment_id, db, current_user, session_id)
    
    # Remover todos os itens
    db.query(CartItem).filter(CartItem.cart_id == cart.id).delete()
    db.commit()
    
    return {"message": "Carrinho limpo"}


@router.get("/{establishment_id}/summary", response_model=CartSummary)
async def get_cart_summary(
    establishment_id: str,
    request: Request,
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional)
):
    """Obter resumo do carrinho"""
    
    session_id = get_session_id(request) if not current_user else None
    cart = get_or_create_cart(establishment_id, db, current_user, session_id)
    
    is_valid, errors = cart.is_valid_for_checkout()
    
    return CartSummary(
        total_items=cart.total_items,
        subtotal=cart.subtotal,
        delivery_fee=cart.delivery_fee,
        service_fee=cart.service_fee,
        total_amount=cart.total_amount,
        is_valid_for_checkout=is_valid,
        validation_errors=errors
    )


@router.put("/{establishment_id}", response_model=CartResponse)
async def update_cart(
    establishment_id: str,
    cart_data: CartUpdate,
    request: Request,
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional)
):
    """Atualizar informações do carrinho"""
    
    session_id = get_session_id(request) if not current_user else None
    cart = get_or_create_cart(establishment_id, db, current_user, session_id)
    
    # Atualizar campos
    for field, value in cart_data.dict(exclude_unset=True).items():
        setattr(cart, field, value)
    
    db.commit()
    db.refresh(cart)
    
    return cart


@router.get("/{establishment_id}/validate", response_model=CartValidation)
async def validate_cart(
    establishment_id: str,
    request: Request,
    db: Session = Depends(get_db),
    current_user: Optional[User] = Depends(get_current_user_optional)
):
    """Validar carrinho para checkout"""
    
    session_id = get_session_id(request) if not current_user else None
    cart = get_or_create_cart(establishment_id, db, current_user, session_id)
    
    is_valid, errors = cart.is_valid_for_checkout()
    
    return CartValidation(
        is_valid=is_valid,
        errors=errors
    )
