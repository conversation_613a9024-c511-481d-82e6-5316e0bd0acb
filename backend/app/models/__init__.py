"""
Modelos SQLAlchemy para o sistema de cardápio digital
"""

# Importar todos os modelos para que o SQLAlchemy os reconheça
from app.models.base import BaseModel, TenantMixin, TimestampMixin
from app.models.establishment import Establishment
from app.models.user import User, UserSession, UserPreferences, UserRole
from app.models.menu import (
    Category,
    MenuItem,
    Ingredient,
    MenuItemCustomization,
    CustomizationOption,
    menu_item_ingredients
)
from app.models.order import (
    Order,
    OrderItem,
    Payment,
    OrderTracking,
    OrderStatus,
    OrderType,
    PaymentStatus,
    PaymentMethod
)
from app.models.review import Review, Favorite, EstablishmentReview
from app.models.promotion import (
    Promotion,
    Coupon,
    CouponUse,
    PromotionType,
    CouponStatus
)

# Lista de todos os modelos para facilitar importação
__all__ = [
    # Base
    "BaseModel",
    "TenantMixin",
    "TimestampMixin",

    # Establishment
    "Establishment",

    # User
    "User",
    "UserSession",
    "UserPreferences",
    "UserRole",

    # Menu
    "Category",
    "MenuItem",
    "Ingredient",
    "MenuItemCustomization",
    "CustomizationOption",
    "menu_item_ingredients",

    # Order
    "Order",
    "OrderItem",
    "Payment",
    "OrderTracking",
    "OrderStatus",
    "OrderType",
    "PaymentStatus",
    "PaymentMethod",

    # Review
    "Review",
    "Favorite",
    "EstablishmentReview",

    # Promotion
    "Promotion",
    "Coupon",
    "CouponUse",
    "PromotionType",
    "CouponStatus",
]
