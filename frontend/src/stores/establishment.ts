import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ApiService } from '@/services/api'
import type { 
  Establishment, 
  EstablishmentCreate, 
  EstablishmentUpdate, 
  EstablishmentStats,
  EstablishmentFilters,
  PaginatedResponse 
} from '@/types'

export const useEstablishmentStore = defineStore('establishment', () => {
  // Estado
  const currentEstablishment = ref<Establishment | null>(null)
  const establishments = ref<Establishment[]>([])
  const stats = ref<EstablishmentStats | null>(null)
  const isLoading = ref(false)
  const isLoadingStats = ref(false)
  const pagination = ref({
    total: 0,
    page: 1,
    per_page: 10,
    pages: 0
  })

  // Getters computados
  const hasEstablishment = computed(() => !!currentEstablishment.value)
  const isEstablishmentActive = computed(() => currentEstablishment.value?.is_active || false)
  const isEstablishmentOpen = computed(() => currentEstablishment.value?.is_open_now || false)
  const establishmentColors = computed(() => {
    if (!currentEstablishment.value) return null
    return {
      primary: currentEstablishment.value.primary_color,
      secondary: currentEstablishment.value.secondary_color,
      accent: currentEstablishment.value.accent_color
    }
  })

  // Ações para estabelecimento atual
  const fetchCurrentEstablishment = async (): Promise<void> => {
    isLoading.value = true
    
    try {
      const establishment = await ApiService.get<Establishment>('/establishments/me')
      currentEstablishment.value = establishment
    } catch (error) {
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const createEstablishment = async (data: EstablishmentCreate): Promise<Establishment> => {
    isLoading.value = true
    
    try {
      const establishment = await ApiService.post<Establishment>('/establishments/', data)
      currentEstablishment.value = establishment
      return establishment
    } catch (error) {
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const updateEstablishment = async (data: EstablishmentUpdate): Promise<void> => {
    if (!currentEstablishment.value) {
      throw new Error('Nenhum estabelecimento selecionado')
    }

    isLoading.value = true
    
    try {
      const updated = await ApiService.put<Establishment>('/establishments/me', data)
      currentEstablishment.value = updated
    } catch (error) {
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const uploadLogo = async (file: File, onProgress?: (progress: number) => void): Promise<string> => {
    try {
      const response = await ApiService.uploadFile<{ image_url: string }>(
        '/establishments/me/logo',
        file,
        onProgress
      )
      
      if (currentEstablishment.value) {
        currentEstablishment.value.logo_url = response.image_url
      }
      
      return response.image_url
    } catch (error) {
      throw error
    }
  }

  const uploadBanner = async (file: File, onProgress?: (progress: number) => void): Promise<string> => {
    try {
      const response = await ApiService.uploadFile<{ image_url: string }>(
        '/establishments/me/banner',
        file,
        onProgress
      )
      
      if (currentEstablishment.value) {
        currentEstablishment.value.banner_url = response.image_url
      }
      
      return response.image_url
    } catch (error) {
      throw error
    }
  }

  const updateHours = async (hours: any): Promise<void> => {
    isLoading.value = true
    
    try {
      await ApiService.put('/establishments/me/hours', hours)
      // Recarregar dados do estabelecimento para obter horários atualizados
      await fetchCurrentEstablishment()
    } catch (error) {
      throw error
    } finally {
      isLoading.value = false
    }
  }

  // Ações para estatísticas
  const fetchStats = async (): Promise<void> => {
    isLoadingStats.value = true
    
    try {
      const establishmentStats = await ApiService.get<EstablishmentStats>('/establishments/me/stats')
      stats.value = establishmentStats
    } catch (error) {
      throw error
    } finally {
      isLoadingStats.value = false
    }
  }

  // Ações para listagem de estabelecimentos (para admins)
  const fetchEstablishments = async (filters: EstablishmentFilters = {}): Promise<void> => {
    isLoading.value = true
    
    try {
      const response = await ApiService.get<PaginatedResponse<Establishment>>('/establishments/', {
        params: filters
      })
      
      establishments.value = response.data
      pagination.value = {
        total: response.total,
        page: response.page,
        per_page: response.per_page,
        pages: response.pages
      }
    } catch (error) {
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const getEstablishmentBySlug = async (slug: string): Promise<Establishment> => {
    try {
      return await ApiService.get<Establishment>(`/establishments/${slug}`)
    } catch (error) {
      throw error
    }
  }

  // Ações para limpar estado
  const clearCurrentEstablishment = (): void => {
    currentEstablishment.value = null
    stats.value = null
  }

  const clearEstablishments = (): void => {
    establishments.value = []
    pagination.value = {
      total: 0,
      page: 1,
      per_page: 10,
      pages: 0
    }
  }

  // Ações para configurações específicas
  const toggleAcceptOrders = async (): Promise<void> => {
    if (!currentEstablishment.value) return

    const newStatus = !currentEstablishment.value.accepts_orders
    
    try {
      await updateEstablishment({ accepts_orders: newStatus })
    } catch (error) {
      throw error
    }
  }

  const updateDeliverySettings = async (settings: {
    delivery_fee?: number
    minimum_order?: number
    max_delivery_distance?: number
    has_delivery?: boolean
  }): Promise<void> => {
    try {
      await updateEstablishment(settings)
    } catch (error) {
      throw error
    }
  }

  const updatePaymentMethods = async (methods: {
    accepts_pix?: boolean
    accepts_card?: boolean
    accepts_cash?: boolean
  }): Promise<void> => {
    try {
      await updateEstablishment(methods)
    } catch (error) {
      throw error
    }
  }

  const updateServiceTypes = async (services: {
    has_delivery?: boolean
    has_pickup?: boolean
    has_table_service?: boolean
  }): Promise<void> => {
    try {
      await updateEstablishment(services)
    } catch (error) {
      throw error
    }
  }

  return {
    // Estado
    currentEstablishment,
    establishments,
    stats,
    isLoading,
    isLoadingStats,
    pagination,
    
    // Getters
    hasEstablishment,
    isEstablishmentActive,
    isEstablishmentOpen,
    establishmentColors,
    
    // Ações principais
    fetchCurrentEstablishment,
    createEstablishment,
    updateEstablishment,
    uploadLogo,
    uploadBanner,
    updateHours,
    
    // Ações para estatísticas
    fetchStats,
    
    // Ações para listagem
    fetchEstablishments,
    getEstablishmentBySlug,
    
    // Ações para limpeza
    clearCurrentEstablishment,
    clearEstablishments,
    
    // Ações para configurações
    toggleAcceptOrders,
    updateDeliverySettings,
    updatePaymentMethods,
    updateServiceTypes,
  }
})
