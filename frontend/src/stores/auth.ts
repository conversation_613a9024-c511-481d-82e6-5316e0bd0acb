import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { ApiService } from '@/services/api'
import type { User, LoginCredentials, RegisterData, AuthResponse } from '@/types'

export const useAuthStore = defineStore('auth', () => {
  // Estado
  const user = ref<User | null>(null)
  const token = ref<string | null>(null)
  const refreshToken = ref<string | null>(null)
  const isLoading = ref(false)
  const isInitialized = ref(false)

  // Getters computados
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isOwner = computed(() => user.value?.role === 'owner')
  const isManager = computed(() => user.value?.role === 'manager')
  const isEmployee = computed(() => user.value?.role === 'employee')
  const isCustomer = computed(() => user.value?.role === 'customer')
  const isStaff = computed(() => ['owner', 'manager', 'employee'].includes(user.value?.role || ''))
  const canManageEstablishment = computed(() => ['owner', 'manager'].includes(user.value?.role || ''))

  // Ações
  const login = async (credentials: LoginCredentials): Promise<void> => {
    isLoading.value = true
    
    try {
      const response = await ApiService.post<AuthResponse>('/auth/login', credentials)
      
      // Armazenar dados de autenticação
      token.value = response.access_token
      refreshToken.value = response.refresh_token
      user.value = response.user
      
      // Salvar no localStorage
      localStorage.setItem('auth_token', response.access_token)
      localStorage.setItem('refresh_token', response.refresh_token)
      localStorage.setItem('user', JSON.stringify(response.user))
      
    } catch (error) {
      // Erro já tratado pelo interceptor da API
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const register = async (data: RegisterData): Promise<void> => {
    isLoading.value = true
    
    try {
      await ApiService.post('/auth/register', data)
      // Após registro, fazer login automaticamente
      await login({ email: data.email, password: data.password })
    } catch (error) {
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const logout = async (): Promise<void> => {
    try {
      // Tentar fazer logout no servidor
      if (refreshToken.value) {
        await ApiService.post('/auth/logout', { refresh_token: refreshToken.value })
      }
    } catch (error) {
      // Ignorar erros de logout no servidor
      console.warn('Erro ao fazer logout no servidor:', error)
    } finally {
      // Limpar estado local
      user.value = null
      token.value = null
      refreshToken.value = null
      
      // Limpar localStorage
      localStorage.removeItem('auth_token')
      localStorage.removeItem('refresh_token')
      localStorage.removeItem('user')
    }
  }

  const refreshAccessToken = async (): Promise<boolean> => {
    if (!refreshToken.value) {
      return false
    }

    try {
      const response = await ApiService.post<AuthResponse>('/auth/refresh', {
        refresh_token: refreshToken.value
      })
      
      // Atualizar tokens
      token.value = response.access_token
      refreshToken.value = response.refresh_token
      user.value = response.user
      
      // Atualizar localStorage
      localStorage.setItem('auth_token', response.access_token)
      localStorage.setItem('refresh_token', response.refresh_token)
      localStorage.setItem('user', JSON.stringify(response.user))
      
      return true
    } catch (error) {
      // Se refresh falhar, fazer logout
      await logout()
      return false
    }
  }

  const updateProfile = async (data: Partial<User>): Promise<void> => {
    isLoading.value = true
    
    try {
      const updatedUser = await ApiService.put<User>('/auth/me', data)
      user.value = updatedUser
      
      // Atualizar localStorage
      localStorage.setItem('user', JSON.stringify(updatedUser))
    } catch (error) {
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const changePassword = async (currentPassword: string, newPassword: string): Promise<void> => {
    isLoading.value = true
    
    try {
      await ApiService.post('/auth/change-password', {
        current_password: currentPassword,
        new_password: newPassword
      })
    } catch (error) {
      throw error
    } finally {
      isLoading.value = false
    }
  }

  const verifyEmail = async (): Promise<void> => {
    try {
      await ApiService.post('/auth/verify-email')
      if (user.value) {
        user.value.email_verified = true
        localStorage.setItem('user', JSON.stringify(user.value))
      }
    } catch (error) {
      throw error
    }
  }

  const initializeAuth = (): void => {
    // Recuperar dados do localStorage
    const storedToken = localStorage.getItem('auth_token')
    const storedRefreshToken = localStorage.getItem('refresh_token')
    const storedUser = localStorage.getItem('user')

    if (storedToken && storedRefreshToken && storedUser) {
      try {
        token.value = storedToken
        refreshToken.value = storedRefreshToken
        user.value = JSON.parse(storedUser)
      } catch (error) {
        console.error('Erro ao recuperar dados de autenticação:', error)
        // Limpar dados corrompidos
        localStorage.removeItem('auth_token')
        localStorage.removeItem('refresh_token')
        localStorage.removeItem('user')
      }
    }

    isInitialized.value = true
  }

  const checkAuthStatus = async (): Promise<void> => {
    if (!token.value) {
      return
    }

    try {
      // Verificar se o token ainda é válido fazendo uma requisição para /auth/me
      const currentUser = await ApiService.get<User>('/auth/me')
      user.value = currentUser
      localStorage.setItem('user', JSON.stringify(currentUser))
    } catch (error) {
      // Se falhar, tentar renovar o token
      const refreshed = await refreshAccessToken()
      if (!refreshed) {
        // Se não conseguir renovar, fazer logout
        await logout()
      }
    }
  }

  // Inicializar automaticamente quando o store for criado
  if (!isInitialized.value) {
    initializeAuth()
  }

  return {
    // Estado
    user,
    token,
    refreshToken,
    isLoading,
    isInitialized,
    
    // Getters
    isAuthenticated,
    isOwner,
    isManager,
    isEmployee,
    isCustomer,
    isStaff,
    canManageEstablishment,
    
    // Ações
    login,
    register,
    logout,
    refreshAccessToken,
    updateProfile,
    changePassword,
    verifyEmail,
    initializeAuth,
    checkAuthStatus,
  }
})
