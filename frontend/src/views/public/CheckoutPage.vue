<template>
  <div class="min-h-screen bg-gray-50">
    <!-- Header -->
    <div class="bg-white shadow-sm">
      <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div class="py-4">
          <div class="flex items-center space-x-4">
            <button
              @click="goBack"
              class="p-2 text-gray-400 hover:text-gray-600 transition-colors duration-200"
            >
              <ArrowLeftIcon class="h-6 w-6" />
            </button>
            <h1 class="text-2xl font-bold text-gray-900">Finalizar Pedido</h1>
          </div>
        </div>
      </div>
    </div>

    <!-- Conteúdo principal -->
    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <!-- Loading state -->
      <div v-if="isLoading" class="flex justify-center py-12">
        <div class="spinner w-8 h-8"></div>
      </div>

      <!-- Checkout form -->
      <div v-else class="lg:grid lg:grid-cols-3 lg:gap-8">
        <!-- Formulário -->
        <div class="lg:col-span-2 space-y-6">
          <!-- Informações do cliente -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Suas Informações</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="form-group">
                <label for="customer_name" class="form-label">Nome completo *</label>
                <input
                  id="customer_name"
                  v-model="form.customer_name"
                  type="text"
                  required
                  :class="[
                    'input',
                    errors.customer_name ? 'input-error' : ''
                  ]"
                  placeholder="Seu nome completo"
                />
                <p v-if="errors.customer_name" class="form-error">
                  {{ errors.customer_name }}
                </p>
              </div>

              <div class="form-group">
                <label for="customer_phone" class="form-label">Telefone *</label>
                <input
                  id="customer_phone"
                  v-model="form.customer_phone"
                  type="tel"
                  required
                  :class="[
                    'input',
                    errors.customer_phone ? 'input-error' : ''
                  ]"
                  placeholder="(11) 99999-9999"
                  @input="formatPhone"
                />
                <p v-if="errors.customer_phone" class="form-error">
                  {{ errors.customer_phone }}
                </p>
              </div>

              <div class="form-group md:col-span-2">
                <label for="customer_email" class="form-label">E-mail</label>
                <input
                  id="customer_email"
                  v-model="form.customer_email"
                  type="email"
                  :class="[
                    'input',
                    errors.customer_email ? 'input-error' : ''
                  ]"
                  placeholder="<EMAIL>"
                />
                <p v-if="errors.customer_email" class="form-error">
                  {{ errors.customer_email }}
                </p>
                <p class="form-help">
                  Para receber atualizações sobre seu pedido
                </p>
              </div>
            </div>
          </div>

          <!-- Endereço de entrega (se delivery) -->
          <div v-if="orderType === 'delivery'" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Endereço de Entrega</h2>
            
            <DeliveryAddressForm
              v-model="form"
              :errors="errors"
              @address-selected="handleAddressSelected"
            />
          </div>

          <!-- Mesa (se table_service) -->
          <div v-if="orderType === 'table_service'" class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Mesa</h2>
            
            <div class="form-group">
              <label for="table_number" class="form-label">Número da mesa *</label>
              <input
                id="table_number"
                v-model.number="form.table_number"
                type="number"
                min="1"
                required
                :class="[
                  'input',
                  errors.table_number ? 'input-error' : ''
                ]"
                placeholder="Ex: 5"
              />
              <p v-if="errors.table_number" class="form-error">
                {{ errors.table_number }}
              </p>
            </div>
          </div>

          <!-- Método de pagamento -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Método de Pagamento</h2>
            
            <PaymentMethodSelector
              v-model="form.payment_method"
              :available-methods="availablePaymentMethods"
              :errors="errors"
            />
          </div>

          <!-- Observações -->
          <div class="bg-white rounded-lg shadow-sm border border-gray-200 p-6">
            <h2 class="text-lg font-medium text-gray-900 mb-4">Observações</h2>
            
            <div class="form-group">
              <label for="notes" class="form-label">Observações do pedido</label>
              <textarea
                id="notes"
                v-model="form.notes"
                rows="3"
                class="input"
                placeholder="Alguma observação especial para o seu pedido?"
              />
            </div>
          </div>
        </div>

        <!-- Resumo do pedido -->
        <div class="lg:col-span-1 mt-8 lg:mt-0">
          <div class="sticky top-8">
            <CheckoutSummary
              :cart="cart"
              :order-type="orderType"
              :estimated-time="estimatedTime"
              @place-order="handlePlaceOrder"
              :is-processing="isProcessing"
              :is-valid="isFormValid"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- Modal de confirmação -->
    <OrderConfirmationModal
      v-if="showConfirmationModal"
      :order="confirmedOrder"
      @close="closeConfirmationModal"
      @track-order="goToTracking"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ArrowLeftIcon } from '@heroicons/vue/24/outline'
import { useToast } from 'vue-toastification'

import { useCartStore } from '@/stores/cart'
import { usePublicMenuStore } from '@/stores/publicMenu'
import type { CheckoutRequest, CheckoutResponse } from '@/types'

// Componentes
const DeliveryAddressForm = defineAsyncComponent(() => import('@/components/public/DeliveryAddressForm.vue'))
const PaymentMethodSelector = defineAsyncComponent(() => import('@/components/public/PaymentMethodSelector.vue'))
const CheckoutSummary = defineAsyncComponent(() => import('@/components/public/CheckoutSummary.vue'))
const OrderConfirmationModal = defineAsyncComponent(() => import('@/components/public/OrderConfirmationModal.vue'))

const router = useRouter()
const route = useRoute()
const toast = useToast()
const cartStore = useCartStore()
const menuStore = usePublicMenuStore()

// Estado local
const isLoading = ref(false)
const isProcessing = ref(false)
const showConfirmationModal = ref(false)
const confirmedOrder = ref<CheckoutResponse | null>(null)

const form = ref<CheckoutRequest>({
  customer_name: '',
  customer_phone: '',
  customer_email: '',
  order_type: 'delivery',
  table_number: undefined,
  delivery_address: '',
  delivery_city: '',
  delivery_state: '',
  delivery_zip_code: '',
  delivery_latitude: undefined,
  delivery_longitude: undefined,
  payment_method: '',
  notes: ''
})

const errors = ref<Record<string, string>>({})
const availablePaymentMethods = ref([])

// Computed
const cart = computed(() => cartStore.cart)
const orderType = computed(() => cart.value?.order_type || 'delivery')
const estimatedTime = computed(() => {
  if (orderType.value === 'delivery') {
    return menuStore.establishment?.estimated_delivery_time || 45
  }
  return menuStore.establishment?.estimated_prep_time || 30
})

const isFormValid = computed(() => {
  return form.value.customer_name &&
         form.value.customer_phone &&
         form.value.payment_method &&
         (orderType.value !== 'delivery' || form.value.delivery_address) &&
         (orderType.value !== 'table_service' || form.value.table_number)
})

// Methods
const loadCheckoutData = async () => {
  isLoading.value = true
  
  try {
    const slug = route.params.slug as string
    
    // Carregar estabelecimento se necessário
    if (!menuStore.establishment) {
      await menuStore.fetchEstablishment(slug)
    }
    
    // Carregar carrinho
    if (menuStore.establishment) {
      await cartStore.fetchCart(menuStore.establishment.id)
      
      // Carregar métodos de pagamento
      await loadPaymentMethods()
      
      // Preencher formulário com dados do carrinho
      if (cart.value) {
        form.value.order_type = cart.value.order_type
        form.value.customer_name = cart.value.customer_name || ''
        form.value.customer_phone = cart.value.customer_phone || ''
        form.value.customer_email = cart.value.customer_email || ''
        form.value.delivery_address = cart.value.delivery_address || ''
        form.value.delivery_city = cart.value.delivery_city || ''
        form.value.delivery_state = cart.value.delivery_state || ''
        form.value.delivery_zip_code = cart.value.delivery_zip_code || ''
        form.value.table_number = cart.value.table_number
        form.value.notes = cart.value.notes || ''
      }
    }
  } catch (error) {
    console.error('Erro ao carregar dados do checkout:', error)
    toast.error('Erro ao carregar dados do checkout')
  } finally {
    isLoading.value = false
  }
}

const loadPaymentMethods = async () => {
  try {
    if (menuStore.establishment) {
      // Simular carregamento de métodos de pagamento
      availablePaymentMethods.value = [
        { id: 'pix', name: 'PIX', icon: 'pix' },
        { id: 'credit_card', name: 'Cartão de Crédito', icon: 'credit_card' },
        { id: 'debit_card', name: 'Cartão de Débito', icon: 'debit_card' },
        { id: 'cash', name: 'Dinheiro', icon: 'cash' }
      ]
    }
  } catch (error) {
    console.error('Erro ao carregar métodos de pagamento:', error)
  }
}

const formatPhone = () => {
  let value = form.value.customer_phone.replace(/\D/g, '')
  
  if (value.length <= 11) {
    value = value.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3')
    if (value.length < 15) {
      value = value.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3')
    }
  }
  
  form.value.customer_phone = value
}

const validateForm = (): boolean => {
  errors.value = {}
  
  if (!form.value.customer_name.trim()) {
    errors.value.customer_name = 'Nome é obrigatório'
  }
  
  if (!form.value.customer_phone.trim()) {
    errors.value.customer_phone = 'Telefone é obrigatório'
  } else if (form.value.customer_phone.replace(/\D/g, '').length < 10) {
    errors.value.customer_phone = 'Telefone inválido'
  }
  
  if (form.value.customer_email && !isValidEmail(form.value.customer_email)) {
    errors.value.customer_email = 'E-mail inválido'
  }
  
  if (!form.value.payment_method) {
    errors.value.payment_method = 'Selecione um método de pagamento'
  }
  
  if (orderType.value === 'delivery') {
    if (!form.value.delivery_address?.trim()) {
      errors.value.delivery_address = 'Endereço é obrigatório para delivery'
    }
    if (!form.value.delivery_city?.trim()) {
      errors.value.delivery_city = 'Cidade é obrigatória'
    }
    if (!form.value.delivery_state?.trim()) {
      errors.value.delivery_state = 'Estado é obrigatório'
    }
    if (!form.value.delivery_zip_code?.trim()) {
      errors.value.delivery_zip_code = 'CEP é obrigatório'
    }
  }
  
  if (orderType.value === 'table_service' && !form.value.table_number) {
    errors.value.table_number = 'Número da mesa é obrigatório'
  }
  
  return Object.keys(errors.value).length === 0
}

const isValidEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

const handleAddressSelected = (address: any) => {
  form.value.delivery_address = address.formatted_address
  form.value.delivery_city = address.city
  form.value.delivery_state = address.state
  form.value.delivery_zip_code = address.zip_code
  form.value.delivery_latitude = address.latitude
  form.value.delivery_longitude = address.longitude
}

const handlePlaceOrder = async () => {
  if (!validateForm() || !menuStore.establishment) {
    return
  }
  
  isProcessing.value = true
  
  try {
    const response = await cartStore.checkout(menuStore.establishment.id, form.value)
    
    confirmedOrder.value = response
    showConfirmationModal.value = true
    
    toast.success('Pedido realizado com sucesso!')
  } catch (error: any) {
    toast.error(error.message || 'Erro ao processar pedido')
  } finally {
    isProcessing.value = false
  }
}

const closeConfirmationModal = () => {
  showConfirmationModal.value = false
  confirmedOrder.value = null
}

const goToTracking = () => {
  if (confirmedOrder.value) {
    router.push(`/order/${confirmedOrder.value.order_id}/tracking`)
  }
}

const goBack = () => {
  const slug = route.params.slug as string
  router.push(`/cart/${slug}`)
}

// Watchers
watch(() => orderType.value, (newType) => {
  form.value.order_type = newType
})

// Lifecycle
onMounted(() => {
  loadCheckoutData()
})

// Lazy loading de componentes
import { defineAsyncComponent } from 'vue'
</script>

<style scoped>
.spinner {
  border: 2px solid #f3f4f6;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
