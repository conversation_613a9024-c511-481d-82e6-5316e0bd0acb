"""
Testes para a aplicação principal
"""
import pytest
from fastapi.testclient import TestClient
from app.main import app

client = TestClient(app)


def test_root_endpoint():
    """Testar endpoint raiz"""
    response = client.get("/")
    assert response.status_code == 200
    data = response.json()
    assert "message" in data
    assert "version" in data
    assert "environment" in data


def test_health_check():
    """Testar endpoint de health check"""
    response = client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert "timestamp" in data
    assert "version" in data


def test_not_found():
    """Testar endpoint não encontrado"""
    response = client.get("/endpoint-inexistente")
    assert response.status_code == 404
    data = response.json()
    assert "detail" in data
    assert "path" in data
