"""
Modelos relacionados a pedidos
"""
from sqlalchemy import Column, String, Text, Boolean, Numeric, Integer, Foreign<PERSON>ey, DateTime, Enum, Float
from sqlalchemy.dialects.postgresql import UUID, JSO<PERSON>
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from enum import Enum as PyEnum
from datetime import datetime
from app.models.base import BaseModel, TenantMixin


class OrderStatus(PyEnum):
    """Status do pedido"""
    PENDING = "pending"           # Aguardando confirmação
    CONFIRMED = "confirmed"       # Confirmado
    PREPARING = "preparing"       # Em preparo
    READY = "ready"              # Pronto
    OUT_FOR_DELIVERY = "out_for_delivery"  # Saiu para entrega
    DELIVERED = "delivered"       # Entregue
    CANCELLED = "cancelled"       # Cancelado
    REFUNDED = "refunded"        # Reembolsado


class OrderType(PyEnum):
    """Tipo de pedido"""
    DELIVERY = "delivery"         # Entrega
    PICKUP = "pickup"            # Retirada
    TABLE_SERVICE = "table_service"  # Mesa


class PaymentStatus(PyEnum):
    """Status do pagamento"""
    PENDING = "pending"           # Aguardando pagamento
    PROCESSING = "processing"     # Processando
    COMPLETED = "completed"       # Pago
    FAILED = "failed"            # Falhou
    REFUNDED = "refunded"        # Reembolsado
    CANCELLED = "cancelled"       # Cancelado


class PaymentMethod(PyEnum):
    """Método de pagamento"""
    PIX = "pix"
    CREDIT_CARD = "credit_card"
    DEBIT_CARD = "debit_card"
    CASH = "cash"
    MERCADO_PAGO = "mercado_pago"
    STRIPE = "stripe"


class Order(BaseModel, TenantMixin):
    """Modelo de pedido"""
    
    __tablename__ = "orders"
    
    # Número do pedido (sequencial por estabelecimento)
    order_number = Column(String(20), nullable=False, index=True)
    
    # Tipo e status
    order_type = Column(Enum(OrderType), nullable=False, default=OrderType.DELIVERY)
    status = Column(Enum(OrderStatus), nullable=False, default=OrderStatus.PENDING)
    
    # Valores
    subtotal = Column(Numeric(10, 2), nullable=False)
    delivery_fee = Column(Numeric(10, 2), default=0.00)
    service_fee = Column(Numeric(10, 2), default=0.00)
    discount_amount = Column(Numeric(10, 2), default=0.00)
    total_amount = Column(Numeric(10, 2), nullable=False)
    
    # Informações do cliente
    customer_name = Column(String(255), nullable=False)
    customer_phone = Column(String(20), nullable=False)
    customer_email = Column(String(255))
    
    # Endereço de entrega
    delivery_address = Column(Text)
    delivery_city = Column(String(100))
    delivery_state = Column(String(50))
    delivery_zip_code = Column(String(20))
    delivery_complement = Column(String(255))
    delivery_number = Column(String(20))
    delivery_latitude = Column(Numeric(10, 8))
    delivery_longitude = Column(Numeric(11, 8))
    
    # Observações
    notes = Column(Text)
    internal_notes = Column(Text)  # Notas internas do estabelecimento
    
    # Tempos
    estimated_prep_time = Column(Integer)  # minutos
    estimated_delivery_time = Column(Integer)  # minutos
    confirmed_at = Column(DateTime)
    ready_at = Column(DateTime)
    delivered_at = Column(DateTime)
    cancelled_at = Column(DateTime)
    
    # Relacionamentos
    customer_id = Column(
        UUID(as_uuid=True),
        ForeignKey("users.id"),
        nullable=True,  # Pode ser null para pedidos de convidados
        index=True
    )
    
    establishment_id = Column(
        UUID(as_uuid=True),
        ForeignKey("establishments.id"),
        nullable=False,
        index=True
    )
    
    customer = relationship("User", back_populates="orders")
    establishment = relationship("Establishment", back_populates="orders")
    items = relationship("OrderItem", back_populates="order")
    payment = relationship("Payment", back_populates="order", uselist=False)
    tracking = relationship("OrderTracking", back_populates="order")
    
    def __repr__(self):
        return f"<Order(number='{self.order_number}', status='{self.status.value}')>"
    
    @property
    def can_be_cancelled(self) -> bool:
        """Verificar se o pedido pode ser cancelado"""
        return self.status in [OrderStatus.PENDING, OrderStatus.CONFIRMED, OrderStatus.PREPARING]

    @property
    def is_completed(self) -> bool:
        """Verificar se o pedido foi concluído"""
        return self.status in [OrderStatus.DELIVERED, OrderStatus.CANCELLED, OrderStatus.REFUNDED]

    @property
    def is_active(self) -> bool:
        """Verificar se o pedido está ativo"""
        return not self.is_completed

    def can_transition_to(self, new_status: OrderStatus) -> bool:
        """Verificar se é possível transicionar para o novo status"""
        valid_transitions = {
            OrderStatus.PENDING: [OrderStatus.CONFIRMED, OrderStatus.CANCELLED],
            OrderStatus.CONFIRMED: [OrderStatus.PREPARING, OrderStatus.CANCELLED],
            OrderStatus.PREPARING: [OrderStatus.READY, OrderStatus.CANCELLED],
            OrderStatus.READY: [OrderStatus.OUT_FOR_DELIVERY, OrderStatus.DELIVERED],
            OrderStatus.OUT_FOR_DELIVERY: [OrderStatus.DELIVERED],
            OrderStatus.DELIVERED: [OrderStatus.REFUNDED],
            OrderStatus.CANCELLED: [OrderStatus.REFUNDED],
            OrderStatus.REFUNDED: []
        }

        return new_status in valid_transitions.get(self.status, [])

    def update_status(self, new_status: OrderStatus):
        """Atualizar o status e timestamps relacionados"""
        if not self.can_transition_to(new_status):
            raise ValueError(f"Cannot transition from {self.status} to {new_status}")

        self.status = new_status
        now = func.now()

        if new_status == OrderStatus.CONFIRMED:
            self.confirmed_at = now
        elif new_status == OrderStatus.READY:
            self.ready_at = now
        elif new_status == OrderStatus.DELIVERED:
            self.delivered_at = now
        elif new_status == OrderStatus.CANCELLED:
            self.cancelled_at = now

    @property
    def payment_status(self) -> PaymentStatus:
        """Retornar o status do pagamento"""
        if self.payment:
            return self.payment.status
        return PaymentStatus.PENDING


class OrderItem(BaseModel, TenantMixin):
    """Modelo de item do pedido"""
    
    __tablename__ = "order_items"
    
    quantity = Column(Integer, nullable=False, default=1)
    unit_price = Column(Numeric(10, 2), nullable=False)
    total_price = Column(Numeric(10, 2), nullable=False)
    
    # Snapshot dos dados do item no momento do pedido
    item_name = Column(String(255), nullable=False)
    item_description = Column(Text)
    
    # Personalizações aplicadas (JSON)
    customizations = Column(JSON)
    
    # Observações específicas do item
    notes = Column(Text)
    
    # Relacionamentos
    order_id = Column(
        UUID(as_uuid=True),
        ForeignKey("orders.id"),
        nullable=False,
        index=True
    )
    
    menu_item_id = Column(
        UUID(as_uuid=True),
        ForeignKey("menuitems.id"),
        nullable=False,
        index=True
    )
    
    establishment_id = Column(
        UUID(as_uuid=True),
        ForeignKey("establishments.id"),
        nullable=False,
        index=True
    )
    
    order = relationship("Order", back_populates="items")
    menu_item = relationship("MenuItem", back_populates="order_items")
    
    def __repr__(self):
        return f"<OrderItem(name='{self.item_name}', quantity={self.quantity})>"


class Payment(BaseModel, TenantMixin):
    """Modelo de pagamento"""
    
    __tablename__ = "payments"
    
    # Identificadores externos
    external_id = Column(String(255))  # ID do gateway de pagamento
    transaction_id = Column(String(255))
    
    # Informações do pagamento
    method = Column(Enum(PaymentMethod), nullable=False)
    status = Column(Enum(PaymentStatus), nullable=False, default=PaymentStatus.PENDING)
    amount = Column(Numeric(10, 2), nullable=False)
    
    # Dados específicos do método
    payment_data = Column(JSON)  # Dados específicos do gateway
    
    # Tempos
    paid_at = Column(DateTime)
    failed_at = Column(DateTime)
    refunded_at = Column(DateTime)
    
    # Relacionamentos
    order_id = Column(
        UUID(as_uuid=True),
        ForeignKey("orders.id"),
        nullable=False,
        unique=True,
        index=True
    )
    
    establishment_id = Column(
        UUID(as_uuid=True),
        ForeignKey("establishments.id"),
        nullable=False,
        index=True
    )
    
    order = relationship("Order", back_populates="payment")
    
    def __repr__(self):
        return f"<Payment(method='{self.method.value}', status='{self.status.value}')>"


class OrderTracking(BaseModel, TenantMixin):
    """Modelo de rastreamento de pedido"""
    
    __tablename__ = "order_tracking"
    
    status = Column(Enum(OrderStatus), nullable=False)
    message = Column(String(500))
    timestamp = Column(DateTime, nullable=False, default=datetime.utcnow)
    
    # Informações adicionais
    user_id = Column(UUID(as_uuid=True), ForeignKey("users.id"))  # Quem fez a alteração
    extra_data = Column(JSON)  # Dados adicionais
    
    # Relacionamentos
    order_id = Column(
        UUID(as_uuid=True),
        ForeignKey("orders.id"),
        nullable=False,
        index=True
    )
    
    establishment_id = Column(
        UUID(as_uuid=True),
        ForeignKey("establishments.id"),
        nullable=False,
        index=True
    )
    
    order = relationship("Order", back_populates="tracking")
    user = relationship("User")
    
    def __repr__(self):
        return f"<OrderTracking(status='{self.status.value}', timestamp='{self.timestamp}')>"
