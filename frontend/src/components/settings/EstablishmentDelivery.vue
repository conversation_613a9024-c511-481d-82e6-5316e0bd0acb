<template>
  <div class="space-y-6">
    <!-- Configurações de delivery -->
    <div class="card">
      <div class="card-header">
        <h3 class="text-lg font-medium text-gray-900">Configurações de Delivery</h3>
        <p class="text-sm text-gray-500">Configure como funciona a entrega dos seus pedidos</p>
      </div>
      
      <div class="card-body">
        <form @submit.prevent="handleSubmit" class="space-y-6">
          <!-- Tipos de serviço -->
          <div class="space-y-4">
            <h4 class="text-sm font-medium text-gray-900">Tipos de Serviço</h4>
            
            <div class="space-y-3">
              <label class="flex items-center">
                <input
                  v-model="form.has_delivery"
                  type="checkbox"
                  class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  :disabled="isLoading"
                />
                <span class="ml-3 text-sm text-gray-700">
                  Delivery
                  <span class="block text-xs text-gray-500">
                    Entrega no endereço do cliente
                  </span>
                </span>
              </label>

              <label class="flex items-center">
                <input
                  v-model="form.has_pickup"
                  type="checkbox"
                  class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  :disabled="isLoading"
                />
                <span class="ml-3 text-sm text-gray-700">
                  Retirada no local
                  <span class="block text-xs text-gray-500">
                    Cliente retira no estabelecimento
                  </span>
                </span>
              </label>

              <label class="flex items-center">
                <input
                  v-model="form.has_table_service"
                  type="checkbox"
                  class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
                  :disabled="isLoading"
                />
                <span class="ml-3 text-sm text-gray-700">
                  Atendimento na mesa
                  <span class="block text-xs text-gray-500">
                    Pedidos feitos na mesa do restaurante
                  </span>
                </span>
              </label>
            </div>
          </div>

          <!-- Configurações de delivery -->
          <div v-if="form.has_delivery" class="space-y-6">
            <div class="border-t border-gray-200 pt-6">
              <h4 class="text-sm font-medium text-gray-900 mb-4">Configurações de Entrega</h4>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Taxa de entrega -->
                <div class="form-group">
                  <label for="delivery_fee" class="form-label">
                    Taxa de entrega
                  </label>
                  <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span class="text-gray-500 sm:text-sm">R$</span>
                    </div>
                    <input
                      id="delivery_fee"
                      v-model.number="form.delivery_fee"
                      type="number"
                      step="0.01"
                      min="0"
                      :class="[
                        'input pl-10',
                        errors.delivery_fee ? 'input-error' : ''
                      ]"
                      placeholder="0,00"
                      :disabled="isLoading"
                    />
                  </div>
                  <p v-if="errors.delivery_fee" class="form-error">
                    {{ errors.delivery_fee }}
                  </p>
                  <p class="form-help">
                    Valor fixo cobrado por entrega (deixe 0 para grátis)
                  </p>
                </div>

                <!-- Pedido mínimo -->
                <div class="form-group">
                  <label for="minimum_order" class="form-label">
                    Pedido mínimo para delivery
                  </label>
                  <div class="relative">
                    <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                      <span class="text-gray-500 sm:text-sm">R$</span>
                    </div>
                    <input
                      id="minimum_order"
                      v-model.number="form.minimum_order"
                      type="number"
                      step="0.01"
                      min="0"
                      :class="[
                        'input pl-10',
                        errors.minimum_order ? 'input-error' : ''
                      ]"
                      placeholder="0,00"
                      :disabled="isLoading"
                    />
                  </div>
                  <p v-if="errors.minimum_order" class="form-error">
                    {{ errors.minimum_order }}
                  </p>
                  <p class="form-help">
                    Valor mínimo para aceitar pedidos de delivery
                  </p>
                </div>

                <!-- Distância máxima -->
                <div class="form-group">
                  <label for="max_delivery_distance" class="form-label">
                    Distância máxima de entrega (km)
                  </label>
                  <input
                    id="max_delivery_distance"
                    v-model.number="form.max_delivery_distance"
                    type="number"
                    step="0.1"
                    min="0"
                    :class="[
                      'input',
                      errors.max_delivery_distance ? 'input-error' : ''
                    ]"
                    placeholder="5.0"
                    :disabled="isLoading"
                  />
                  <p v-if="errors.max_delivery_distance" class="form-error">
                    {{ errors.max_delivery_distance }}
                  </p>
                  <p class="form-help">
                    Raio máximo de entrega a partir do seu estabelecimento
                  </p>
                </div>

                <!-- Tempo estimado -->
                <div class="form-group">
                  <label for="estimated_delivery_time" class="form-label">
                    Tempo estimado de entrega (minutos)
                  </label>
                  <input
                    id="estimated_delivery_time"
                    v-model.number="form.estimated_delivery_time"
                    type="number"
                    min="0"
                    :class="[
                      'input',
                      errors.estimated_delivery_time ? 'input-error' : ''
                    ]"
                    placeholder="30"
                    :disabled="isLoading"
                  />
                  <p v-if="errors.estimated_delivery_time" class="form-error">
                    {{ errors.estimated_delivery_time }}
                  </p>
                  <p class="form-help">
                    Tempo médio para entrega dos pedidos
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- Configurações de retirada -->
          <div v-if="form.has_pickup" class="space-y-6">
            <div class="border-t border-gray-200 pt-6">
              <h4 class="text-sm font-medium text-gray-900 mb-4">Configurações de Retirada</h4>
              
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Tempo de preparo -->
                <div class="form-group">
                  <label for="estimated_pickup_time" class="form-label">
                    Tempo estimado de preparo (minutos)
                  </label>
                  <input
                    id="estimated_pickup_time"
                    v-model.number="form.estimated_pickup_time"
                    type="number"
                    min="0"
                    :class="[
                      'input',
                      errors.estimated_pickup_time ? 'input-error' : ''
                    ]"
                    placeholder="20"
                    :disabled="isLoading"
                  />
                  <p v-if="errors.estimated_pickup_time" class="form-error">
                    {{ errors.estimated_pickup_time }}
                  </p>
                  <p class="form-help">
                    Tempo médio para preparo dos pedidos
                  </p>
                </div>

                <!-- Desconto para retirada -->
                <div class="form-group">
                  <label for="pickup_discount" class="form-label">
                    Desconto para retirada (%)
                  </label>
                  <input
                    id="pickup_discount"
                    v-model.number="form.pickup_discount"
                    type="number"
                    step="0.1"
                    min="0"
                    max="100"
                    class="input"
                    placeholder="0"
                    :disabled="isLoading"
                  />
                  <p class="form-help">
                    Desconto percentual para pedidos retirados no local
                  </p>
                </div>
              </div>
            </div>
          </div>

          <!-- Instruções especiais -->
          <div class="space-y-4">
            <h4 class="text-sm font-medium text-gray-900">Instruções Especiais</h4>
            
            <div class="form-group">
              <label for="delivery_instructions" class="form-label">
                Instruções para entregadores
              </label>
              <textarea
                id="delivery_instructions"
                v-model="form.delivery_instructions"
                rows="3"
                class="input"
                placeholder="Ex: Tocar campainha, não buzinar, etc."
                :disabled="isLoading"
              />
              <p class="form-help">
                Instruções que aparecerão para os entregadores
              </p>
            </div>
          </div>

          <!-- Botões -->
          <div class="flex justify-end space-x-3 pt-6 border-t border-gray-200">
            <button
              type="button"
              @click="resetForm"
              class="btn btn-outline"
              :disabled="isLoading"
            >
              Cancelar
            </button>
            
            <button
              type="submit"
              :disabled="isLoading || !isFormValid"
              class="btn btn-primary"
            >
              <div v-if="isLoading" class="flex items-center">
                <div class="spinner w-4 h-4 mr-2"></div>
                Salvando...
              </div>
              <span v-else>
                Salvar Configurações
              </span>
            </button>
          </div>
        </form>
      </div>
    </div>

    <!-- Zonas de entrega -->
    <div v-if="form.has_delivery" class="card">
      <div class="card-header">
        <div class="flex justify-between items-center">
          <div>
            <h3 class="text-lg font-medium text-gray-900">Zonas de Entrega</h3>
            <p class="text-sm text-gray-500">Configure áreas específicas com taxas diferenciadas</p>
          </div>
          <button class="btn btn-outline btn-sm">
            <PlusIcon class="h-4 w-4 mr-2" />
            Adicionar Zona
          </button>
        </div>
      </div>
      
      <div class="card-body">
        <div class="text-center py-8 text-gray-500">
          <MapIcon class="mx-auto h-12 w-12 text-gray-400" />
          <h3 class="mt-2 text-sm font-medium text-gray-900">Nenhuma zona configurada</h3>
          <p class="mt-1 text-sm text-gray-500">
            Configure zonas de entrega para ter mais controle sobre taxas e áreas atendidas
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { PlusIcon, MapIcon } from '@heroicons/vue/24/outline'
import { useToast } from 'vue-toastification'

import { useEstablishmentStore } from '@/stores/establishment'

const toast = useToast()
const establishmentStore = useEstablishmentStore()

// Estado do formulário
const form = ref({
  has_delivery: true,
  has_pickup: true,
  has_table_service: false,
  delivery_fee: 0,
  minimum_order: 0,
  max_delivery_distance: 5,
  estimated_delivery_time: 30,
  estimated_pickup_time: 20,
  pickup_discount: 0,
  delivery_instructions: ''
})

const isLoading = ref(false)
const errors = ref<Record<string, string>>({})

// Computed
const establishment = computed(() => establishmentStore.currentEstablishment)

const isFormValid = computed(() => {
  // Pelo menos um tipo de serviço deve estar ativo
  return form.value.has_delivery || form.value.has_pickup || form.value.has_table_service
})

// Métodos
const validateField = (field: string, value: any) => {
  errors.value[field] = ''
  
  switch (field) {
    case 'delivery_fee':
    case 'minimum_order':
      if (value < 0) {
        errors.value[field] = 'Valor não pode ser negativo'
      }
      break
      
    case 'max_delivery_distance':
      if (value <= 0) {
        errors.value[field] = 'Distância deve ser maior que zero'
      }
      break
      
    case 'estimated_delivery_time':
    case 'estimated_pickup_time':
      if (value <= 0) {
        errors.value[field] = 'Tempo deve ser maior que zero'
      }
      break
  }
}

const validateForm = () => {
  if (form.value.has_delivery) {
    validateField('delivery_fee', form.value.delivery_fee)
    validateField('minimum_order', form.value.minimum_order)
    validateField('max_delivery_distance', form.value.max_delivery_distance)
    validateField('estimated_delivery_time', form.value.estimated_delivery_time)
  }
  
  if (form.value.has_pickup) {
    validateField('estimated_pickup_time', form.value.estimated_pickup_time)
  }
  
  return Object.values(errors.value).every(error => !error)
}

const handleSubmit = async () => {
  if (!validateForm()) {
    return
  }
  
  isLoading.value = true
  
  try {
    await establishmentStore.updateDeliverySettings(form.value)
    toast.success('Configurações de delivery atualizadas com sucesso!')
  } catch (error) {
    console.error('Erro ao atualizar configurações:', error)
    toast.error('Erro ao atualizar configurações de delivery')
  } finally {
    isLoading.value = false
  }
}

const resetForm = () => {
  if (establishment.value) {
    form.value = {
      has_delivery: establishment.value.has_delivery,
      has_pickup: establishment.value.has_pickup,
      has_table_service: establishment.value.has_table_service,
      delivery_fee: establishment.value.delivery_fee,
      minimum_order: establishment.value.minimum_order,
      max_delivery_distance: establishment.value.max_delivery_distance,
      estimated_delivery_time: establishment.value.estimated_delivery_time || 30,
      estimated_pickup_time: establishment.value.estimated_pickup_time || 20,
      pickup_discount: establishment.value.pickup_discount || 0,
      delivery_instructions: establishment.value.delivery_instructions || ''
    }
  }
  errors.value = {}
}

// Lifecycle
onMounted(() => {
  resetForm()
})
</script>
